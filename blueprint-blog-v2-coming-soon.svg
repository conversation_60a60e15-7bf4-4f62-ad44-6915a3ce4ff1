<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Cyber Background Gradient (cores exatas do layout) -->
    <linearGradient id="cyberBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f0f23;stop-opacity:1" />
    </linearGradient>

    <!-- Neon <PERSON> (cor exata do layout) -->
    <linearGradient id="neonCyan" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00e6e6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00cccc;stop-opacity:1" />
    </linearGradient>

    <!-- Neon Magenta (cor exata do layout) -->
    <linearGradient id="neonMagenta" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff00ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e600e6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc00cc;stop-opacity:1" />
    </linearGradient>

    <!-- Neon Yellow (cor exata do layout) -->
    <linearGradient id="neonYellow" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffff00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e6e600;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cccc00;stop-opacity:1" />
    </linearGradient>

    <!-- Neon Green (cor exata do layout) -->
    <linearGradient id="neonGreen" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ff00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00e600;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00cc00;stop-opacity:1" />
    </linearGradient>

    <!-- Neon Purple (cor exata do layout) -->
    <linearGradient id="neonPurple" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8000ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#7300e6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6600cc;stop-opacity:1" />
    </linearGradient>

    <!-- Cyber Surface (cor exata do layout) -->
    <linearGradient id="cyberSurface" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e1e2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2a2a3e;stop-opacity:1" />
    </linearGradient>

    <!-- Glow Effects -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="strongGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="ultraGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="10" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#cyberBg)"/>

  <!-- Tech Grid Pattern -->
  <defs>
    <pattern id="techGrid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#00ffff" stroke-width="0.5" opacity="0.08"/>
      <circle cx="25" cy="25" r="1" fill="#00ffff" opacity="0.05"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#techGrid)"/>

  <!-- Circuit Lines Background -->
  <g opacity="0.1">
    <path d="M 0 100 L 200 100 L 200 150 L 400 150" stroke="url(#neonCyan)" stroke-width="1" fill="none"/>
    <path d="M 800 200 L 1000 200 L 1000 250 L 1200 250" stroke="url(#neonMagenta)" stroke-width="1" fill="none"/>
    <path d="M 0 400 L 150 400 L 150 450 L 350 450" stroke="url(#neonYellow)" stroke-width="1" fill="none"/>
    <path d="M 850 500 L 1050 500 L 1050 550 L 1200 550" stroke="url(#neonGreen)" stroke-width="1" fill="none"/>
  </g>

  <!-- Floating Tech Particles -->
  <g opacity="0.8">
    <circle cx="120" cy="80" r="2" fill="url(#neonCyan)" filter="url(#softGlow)">
      <animate attributeName="cy" values="80;60;80" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="3s" repeatCount="indefinite"/>
    </circle>
    <rect x="1040" y="140" width="4" height="4" fill="url(#neonMagenta)" filter="url(#softGlow)">
      <animate attributeName="y" values="140;120;140" dur="5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate" values="0 1042 142;360 1042 142" dur="8s" repeatCount="indefinite"/>
    </rect>
    <polygon points="180,480 185,490 175,490" fill="url(#neonYellow)" filter="url(#softGlow)">
      <animate attributeName="transform" values="translate(0,0);translate(0,-20);translate(0,0)" dur="3.5s" repeatCount="indefinite"/>
    </polygon>
    <circle cx="980" cy="420" r="3" fill="url(#neonGreen)" filter="url(#softGlow)">
      <animate attributeName="cy" values="420;400;420" dur="4.5s" repeatCount="indefinite"/>
      <animate attributeName="r" values="3;1;3" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Tech Server/Oven Icon (Saindo do Forno) -->
  <g transform="translate(80, 70)">
    <!-- Server Rack Body -->
    <rect x="0" y="20" width="100" height="80" rx="6" fill="url(#cyberSurface)" stroke="url(#neonCyan)" stroke-width="2" filter="url(#softGlow)"/>

    <!-- Server Panels -->
    <rect x="8" y="28" width="84" height="12" rx="2" fill="#1a1a2e" stroke="url(#neonCyan)" stroke-width="1"/>
    <rect x="8" y="45" width="84" height="12" rx="2" fill="#1a1a2e" stroke="url(#neonMagenta)" stroke-width="1"/>
    <rect x="8" y="62" width="84" height="12" rx="2" fill="#1a1a2e" stroke="url(#neonYellow)" stroke-width="1"/>
    <rect x="8" y="79" width="84" height="12" rx="2" fill="#1a1a2e" stroke="url(#neonGreen)" stroke-width="1"/>

    <!-- LED Indicators -->
    <circle cx="15" cy="34" r="2" fill="url(#neonCyan)">
      <animate attributeName="opacity" values="1;0.3;1" dur="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="25" cy="34" r="2" fill="url(#neonCyan)">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="35" cy="34" r="2" fill="url(#neonCyan)">
      <animate attributeName="opacity" values="1;0.3;1" dur="0.8s" repeatCount="indefinite"/>
    </circle>

    <!-- Processing Heat Lines -->
    <path d="M 50 51 Q 60 46 70 51 Q 80 56 85 51" stroke="url(#neonMagenta)" stroke-width="2" fill="none" opacity="0.8" filter="url(#softGlow)">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1.5s" repeatCount="indefinite"/>
    </path>
    <path d="M 45 68 Q 55 63 65 68 Q 75 73 85 68" stroke="url(#neonYellow)" stroke-width="2" fill="none" opacity="0.6" filter="url(#softGlow)">
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.8s" repeatCount="indefinite"/>
    </path>

    <!-- Cooling Vents -->
    <rect x="75" y="25" width="2" height="8" fill="url(#neonCyan)" opacity="0.5"/>
    <rect x="80" y="25" width="2" height="8" fill="url(#neonCyan)" opacity="0.5"/>
    <rect x="85" y="25" width="2" height="8" fill="url(#neonCyan)" opacity="0.5"/>
  </g>

  <!-- Data Stream Effect (replacing steam) -->
  <g transform="translate(190, 50)">
    <!-- Binary Data Stream -->
    <text x="0" y="20" font-family="monospace" font-size="12" fill="url(#neonCyan)" opacity="0.7">01</text>
    <text x="0" y="35" font-family="monospace" font-size="10" fill="url(#neonMagenta)" opacity="0.5">10</text>
    <text x="0" y="50" font-family="monospace" font-size="8" fill="url(#neonYellow)" opacity="0.3">11</text>

    <animateTransform attributeName="transform" type="translate" values="190,50; 190,30; 190,50" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
  </g>
  
  <!-- Main Title with Tech Effect -->
  <g transform="translate(600, 180)">
    <!-- Background Glow -->
    <text x="0" y="0" text-anchor="middle" font-family="'Courier New', monospace" font-size="52" font-weight="bold" fill="url(#neonCyan)" filter="url(#ultraGlow)" opacity="0.3">
      BLUEPRINT BLOG
    </text>
    <!-- Main Text -->
    <text x="0" y="0" text-anchor="middle" font-family="'Courier New', monospace" font-size="52" font-weight="bold" fill="url(#neonCyan)" filter="url(#strongGlow)">
      BLUEPRINT BLOG
    </text>
    <!-- Glitch Effect -->
    <text x="2" y="2" text-anchor="middle" font-family="'Courier New', monospace" font-size="52" font-weight="bold" fill="url(#neonMagenta)" opacity="0.3">
      BLUEPRINT BLOG
      <animate attributeName="opacity" values="0.3;0;0.3" dur="0.1s" repeatCount="indefinite"/>
    </text>
  </g>

  <!-- Version Badge Tech Style -->
  <g transform="translate(750, 140)">
    <rect x="0" y="0" width="70" height="28" rx="4" fill="url(#cyberSurface)" stroke="url(#neonMagenta)" stroke-width="2" filter="url(#softGlow)"/>
    <text x="35" y="19" text-anchor="middle" font-family="'Courier New', monospace" font-size="14" font-weight="bold" fill="url(#neonMagenta)">v2.0</text>
    <circle cx="60" cy="14" r="2" fill="url(#neonMagenta)">
      <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Tech Subtitle -->
  <text x="600" y="240" text-anchor="middle" font-family="'Courier New', monospace" font-size="20" fill="url(#neonYellow)" filter="url(#softGlow)">
    &gt; SAINDO DO SERVIDOR! 🚀
  </text>

  <!-- Main Description with Typing Effect -->
  <g transform="translate(600, 290)">
    <text x="0" y="0" text-anchor="middle" font-family="'Courier New', monospace" font-size="28" font-weight="bold" fill="url(#neonYellow)" filter="url(#strongGlow)">
      TOTALMENTE REPAGINADO
    </text>
    <rect x="-180" y="-25" width="360" height="35" fill="none" stroke="url(#neonYellow)" stroke-width="1" opacity="0.3" rx="4"/>
  </g>
  
  <!-- Tech Features Grid -->
  <g transform="translate(200, 350)">
    <!-- Left Column -->
    <g>
      <!-- Professional Interface -->
      <g>
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonCyan)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonCyan)" filter="url(#softGlow)">
          ▶ Interface Profissional
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Dashboard unificado e responsivo
        </text>
      </g>

      <!-- Tutorials -->
      <g transform="translate(0, 45)">
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonMagenta)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonMagenta)" filter="url(#softGlow)">
          ▶ Tutoriais Avançados
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Conteúdo técnico de qualidade
        </text>
      </g>

      <!-- Tech Articles -->
      <g transform="translate(0, 90)">
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonYellow)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonYellow)" filter="url(#softGlow)">
          ▶ Artigos de Tecnologia
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Análises e tendências tech
        </text>
      </g>
    </g>

    <!-- Right Column -->
    <g transform="translate(420, 0)">
      <!-- Dev Tips -->
      <g>
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonGreen)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonGreen)" filter="url(#softGlow)">
          ▶ Dicas para Devs
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Produtividade e boas práticas
        </text>
      </g>

      <!-- Cheatsheets -->
      <g transform="translate(0, 45)">
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonPurple)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonPurple)" filter="url(#softGlow)">
          ▶ Cheatsheets Exclusivos
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Referências rápidas e úteis
        </text>
      </g>

      <!-- Performance -->
      <g transform="translate(0, 90)">
        <rect x="-5" y="-8" width="280" height="25" rx="4" fill="url(#cyberSurface)" stroke="url(#neonCyan)" stroke-width="1" opacity="0.3"/>
        <text x="0" y="0" font-family="'Courier New', monospace" font-size="16" fill="url(#neonCyan)" filter="url(#softGlow)">
          ▶ Performance Otimizada
        </text>
        <text x="0" y="15" font-family="'Courier New', monospace" font-size="12" fill="#ffffff" opacity="0.7">
          Carregamento ultra-rápido
        </text>
      </g>
    </g>
  </g>
  
  <!-- Tech Coming Soon Banner -->
  <g transform="translate(600, 540)">
    <!-- Background with border animation -->
    <rect x="-140" y="-25" width="280" height="50" rx="8" fill="url(#cyberSurface)" stroke="url(#neonMagenta)" stroke-width="2" filter="url(#strongGlow)">
      <animate attributeName="stroke-width" values="2;4;2" dur="2s" repeatCount="indefinite"/>
    </rect>
    <!-- Inner glow -->
    <rect x="-135" y="-20" width="270" height="40" rx="6" fill="url(#neonMagenta)" opacity="0.1">
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="1.5s" repeatCount="indefinite"/>
    </rect>
    <!-- Main text -->
    <text x="0" y="5" text-anchor="middle" font-family="'Courier New', monospace" font-size="22" font-weight="bold" fill="url(#neonMagenta)" filter="url(#softGlow)">
      &gt; EM BREVE &lt;
    </text>
    <!-- Blinking cursor -->
    <text x="85" y="5" text-anchor="middle" font-family="'Courier New', monospace" font-size="22" font-weight="bold" fill="url(#neonMagenta)">
      _
      <animate attributeName="opacity" values="1;0;1" dur="1s" repeatCount="indefinite"/>
    </text>
  </g>

  <!-- Tech Code Decorations -->
  <g opacity="0.2">
    <text x="40" y="280" font-family="'Courier New', monospace" font-size="48" fill="url(#neonCyan)" filter="url(#softGlow)">
      &lt;/&gt;
    </text>
    <text x="1080" y="320" font-family="'Courier New', monospace" font-size="48" fill="url(#neonMagenta)" filter="url(#softGlow)">
      { }
    </text>
    <text x="60" y="450" font-family="'Courier New', monospace" font-size="36" fill="url(#neonYellow)" filter="url(#softGlow)">
      [ ]
    </text>
    <text x="1100" y="480" font-family="'Courier New', monospace" font-size="36" fill="url(#neonGreen)" filter="url(#softGlow)">
      ( )
    </text>
  </g>

  <!-- Tech Bottom Tagline -->
  <text x="600" y="600" text-anchor="middle" font-family="'Courier New', monospace" font-size="14" fill="url(#neonCyan)" opacity="0.8" filter="url(#softGlow)">
    // O futuro do desenvolvimento está sendo compilado...
  </text>
</svg>
