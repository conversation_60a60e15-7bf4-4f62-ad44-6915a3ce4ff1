<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradientes Neon -->
    <linearGradient id="neonGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow Effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Glitch Effect -->
    <filter id="glitch">
      <feOffset in="SourceGraphic" dx="1" dy="0" result="offset1"/>
      <feFlood flood-color="#ff00ff" flood-opacity="0.3" result="color1"/>
      <feComposite in="color1" in2="offset1" operator="in" result="glitch1"/>
      
      <feOffset in="SourceGraphic" dx="-1" dy="0" result="offset2"/>
      <feFlood flood-color="#00ffff" flood-opacity="0.3" result="color2"/>
      <feComposite in="color2" in2="offset2" operator="in" result="glitch2"/>
      
      <feMerge>
        <feMergeNode in="glitch1"/>
        <feMergeNode in="glitch2"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background Circuit Pattern -->
  <g opacity="0.1">
    <path d="M10,20 L30,20 L30,40 L50,40" stroke="#00ffff" stroke-width="0.5" fill="none"/>
    <path d="M170,15 L190,15 L190,35 L180,35" stroke="#ff00ff" stroke-width="0.5" fill="none"/>
    <circle cx="30" cy="20" r="1" fill="#00ffff"/>
    <circle cx="50" cy="40" r="1" fill="#ff00ff"/>
    <circle cx="170" cy="15" r="1" fill="#8b5cf6"/>
  </g>
  
  <!-- Logo Icon - Stylized "B" -->
  <g transform="translate(15, 10)">
    <!-- Main B Shape -->
    <path d="M0,5 L0,35 L15,35 Q25,35 25,27.5 Q25,22.5 20,20 Q25,17.5 25,12.5 Q25,5 15,5 Z M5,10 L15,10 Q20,10 20,12.5 Q20,15 15,15 L5,15 Z M5,20 L15,20 Q20,20 20,22.5 Q20,25 15,25 L5,25 Z" 
          fill="url(#neonGradient)" 
          filter="url(#glow)"
          stroke="#00ffff" 
          stroke-width="1"/>
    
    <!-- Circuit Lines on B -->
    <path d="M8,12 L12,12 M8,22 L12,22" stroke="#000" stroke-width="0.5" opacity="0.8"/>
    <circle cx="10" cy="12" r="0.5" fill="#000" opacity="0.8"/>
    <circle cx="10" cy="22" r="0.5" fill="#000" opacity="0.8"/>
  </g>
  
  <!-- Text "BLUEPRINT" -->
  <g transform="translate(50, 15)">
    <text x="0" y="15" 
          font-family="Arial, sans-serif" 
          font-size="14" 
          font-weight="bold" 
          fill="url(#neonGradient)"
          filter="url(#glow)">BLUEPRINT</text>
    
    <!-- Underline with circuit pattern -->
    <path d="M0,20 L85,20" stroke="url(#neonGradient)" stroke-width="1" opacity="0.8"/>
    <circle cx="10" cy="20" r="0.5" fill="#00ffff"/>
    <circle cx="30" cy="20" r="0.5" fill="#ff00ff"/>
    <circle cx="50" cy="20" r="0.5" fill="#8b5cf6"/>
    <circle cx="70" cy="20" r="0.5" fill="#00ffff"/>
  </g>
  
  <!-- Text "BLOG" -->
  <g transform="translate(50, 35)">
    <text x="0" y="15" 
          font-family="Arial, sans-serif" 
          font-size="12" 
          font-weight="normal" 
          fill="#ffffff" 
          opacity="0.9">BLOG</text>
    
    <!-- Version indicator -->
    <text x="35" y="15" 
          font-family="Arial, sans-serif" 
          font-size="8" 
          font-weight="normal" 
          fill="#00ffff" 
          opacity="0.7">v2.0</text>
  </g>
  
  <!-- Decorative Elements -->
  <g opacity="0.6">
    <!-- Left side decoration -->
    <path d="M5,5 L8,5 M5,55 L8,55" stroke="#00ffff" stroke-width="1"/>
    <path d="M5,5 L5,8 M5,52 L5,55" stroke="#00ffff" stroke-width="1"/>
    
    <!-- Right side decoration -->
    <path d="M192,5 L195,5 M192,55 L195,55" stroke="#ff00ff" stroke-width="1"/>
    <path d="M195,5 L195,8 M195,52 L195,55" stroke="#ff00ff" stroke-width="1"/>
    
    <!-- Corner dots -->
    <circle cx="5" cy="5" r="1" fill="#00ffff"/>
    <circle cx="195" cy="5" r="1" fill="#ff00ff"/>
    <circle cx="5" cy="55" r="1" fill="#8b5cf6"/>
    <circle cx="195" cy="55" r="1" fill="#00ffff"/>
  </g>
  
  <!-- Animated pulse effect (CSS animation would be added separately) -->
  <g opacity="0.3">
    <circle cx="25" cy="25" r="20" fill="none" stroke="#00ffff" stroke-width="0.5">
      <animate attributeName="r" values="15;25;15" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
