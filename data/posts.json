[{"id": "post-1", "title": "Nova API do OpenAI: Revolucionando a IA Conversacional", "slug": "nova-api-openai-revolucionando-ia-conversacional", "author": {"id": "author-1", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", "bio": "Especialista em IA e Machine Learning com 8 anos de experiência.", "role": "writer", "socialLinks": {"twitter": "https://twitter.com/joaosilva", "linkedin": "https://linkedin.com/in/joaosilva", "github": "https://github.com/joaosilva"}, "postsCount": 15, "joinedAt": "2023-01-15"}, "date": "2025-06-14", "category": {"id": "cat-1", "name": "Inteligência Artificial", "slug": "inteligencia-artificial", "description": "Artigos sobre IA, Machine Learning e tecnologias emergentes", "color": "#00ffff", "icon": "🤖", "postsCount": 25}, "tags": [{"id": "tag-1", "name": "OpenAI", "slug": "openai", "color": "#ff00ff", "postsCount": 8}, {"id": "tag-2", "name": "API", "slug": "api", "color": "#8b5cf6", "postsCount": 12}, {"id": "tag-3", "name": "GPT", "slug": "gpt", "color": "#00ff88", "postsCount": 6}], "thumbnail": "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop", "excerpt": "A OpenAI lançou uma nova API revolucionária que promete transformar a forma como interagimos com inteligência artificial. Descubra as principais funcionalidades e como implementar em seus projetos.", "content": "<p>A OpenAI acaba de anunciar uma nova API que representa um marco significativo no desenvolvimento de aplicações de IA...</p>", "featured": true, "published": true, "views": 1250, "likes": 89, "readTime": 8, "seo": {"metaTitle": "Nova API do OpenAI: Guia Completo para Desenvolvedores", "metaDescription": "Descubra como a nova API da OpenAI está revolucionando o desenvolvimento de aplicações de IA. Guia completo com exemplos práticos.", "keywords": ["OpenAI", "API", "IA", "GPT", "desenvolvimento", "inteligência artificial"], "ogImage": "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=630&fit=crop", "ogTitle": "Nova API do OpenAI: Revolucionando a IA Conversacional", "ogDescription": "A OpenAI lançou uma nova API revolucionária. Descubra as principais funcionalidades e como implementar.", "twitterCard": "summary_large_image"}, "createdAt": "2025-06-14T10:00:00Z", "updatedAt": "2025-06-14T10:00:00Z"}, {"id": "post-2", "title": "Tailwind CSS 4.0: O Futuro do Design Web", "slug": "tailwind-css-4-futuro-design-web", "author": {"id": "author-2", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", "bio": "Frontend Developer e UI/UX Designer apaixonada por tecnologias web modernas.", "role": "editor", "socialLinks": {"twitter": "https://twitter.com/mariasantos", "linkedin": "https://linkedin.com/in/maria<PERSON>tos", "github": "https://github.com/mariasantos"}, "postsCount": 22, "joinedAt": "2022-08-20"}, "date": "2025-06-13", "category": {"id": "cat-2", "name": "Frontend", "slug": "frontend", "description": "Desenvolvimento frontend, frameworks e bibliotecas modernas", "color": "#ff00ff", "icon": "💻", "postsCount": 35}, "tags": [{"id": "tag-4", "name": "Tailwind CSS", "slug": "tailwind-css", "color": "#00ffff", "postsCount": 10}, {"id": "tag-5", "name": "CSS", "slug": "css", "color": "#ff0080", "postsCount": 18}, {"id": "tag-6", "name": "Design System", "slug": "design-system", "color": "#8b5cf6", "postsCount": 7}], "thumbnail": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop", "excerpt": "O Tailwind CSS 4.0 traz inovações revolucionárias para o desenvolvimento web. Explore as novas funcionalidades e como elas vão impactar seus projetos.", "content": "<p>O Tailwind CSS 4.0 representa uma evolução significativa no framework de CSS utilitário mais popular do mundo...</p>", "featured": true, "published": true, "views": 980, "likes": 67, "readTime": 6, "seo": {"metaTitle": "Tailwind CSS 4.0: Novidades e Funcionalidades Revolucionárias", "metaDescription": "Descubra todas as novidades do Tailwind CSS 4.0 e como elas vão revolucionar o desenvolvimento web moderno.", "keywords": ["Tailwind CSS", "CSS", "frontend", "design system", "web development"], "ogImage": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=630&fit=crop", "ogTitle": "Tailwind CSS 4.0: O Futuro do Design Web", "ogDescription": "O Tailwind CSS 4.0 traz inovações revolucionárias. Explore as novas funcionalidades.", "twitterCard": "summary_large_image"}, "createdAt": "2025-06-13T14:30:00Z", "updatedAt": "2025-06-13T14:30:00Z"}, {"id": "post-3", "title": "Docker: Vulnerabilidade Crítica Descoberta", "slug": "docker-vulnerabilidade-critica-descoberta", "author": {"id": "author-3", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", "bio": "DevOps Engineer e especialista em segurança de containers.", "role": "writer", "socialLinks": {"twitter": "https://twitter.com/carlosoliveira", "linkedin": "https://linkedin.com/in/carlosoliveira", "github": "https://github.com/carlosoliveira"}, "postsCount": 18, "joinedAt": "2023-03-10"}, "date": "2025-06-12", "category": {"id": "cat-3", "name": "DevOps", "slug": "devops", "description": "DevOps, containers, CI/CD e infraestrutura como código", "color": "#00ff88", "icon": "🚀", "postsCount": 28}, "tags": [{"id": "tag-7", "name": "<PERSON>er", "slug": "docker", "color": "#0066ff", "postsCount": 15}, {"id": "tag-8", "name": "Segurança", "slug": "seguran<PERSON>", "color": "#ff0000", "postsCount": 12}, {"id": "tag-9", "name": "Containers", "slug": "containers", "color": "#00ffff", "postsCount": 20}], "thumbnail": "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop", "excerpt": "Uma vulnerabilidade crítica foi descoberta no Docker que pode comprometer a segurança de milhões de containers. Saiba como se proteger e aplicar as correções necessárias.", "content": "<p>Pesquisadores de segurança descobriram uma vulnerabilidade crítica no Docker que afeta versões anteriores à 24.0.7...</p>", "featured": false, "published": true, "views": 2100, "likes": 156, "readTime": 5, "seo": {"metaTitle": "Vulnerabilidade Crítica no Docker: Como Se Proteger", "metaDescription": "Vulnerabilidade crítica descoberta no Docker. Aprenda como identificar, corrigir e proteger seus containers.", "keywords": ["<PERSON>er", "segurança", "vulnerabilidade", "containers", "DevOps"], "ogImage": "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=1200&h=630&fit=crop", "ogTitle": "Docker: Vulnerabilidade Crítica Descoberta", "ogDescription": "Uma vulnerabilidade crítica foi descoberta no Docker. Saiba como se proteger.", "twitterCard": "summary_large_image"}, "createdAt": "2025-06-12T09:15:00Z", "updatedAt": "2025-06-12T09:15:00Z"}, {"id": "post-4", "title": "React 19: Novidades e Breaking Changes", "slug": "react-19-novidades-breaking-changes", "author": {"id": "author-2", "name": "<PERSON>", "email": "<EMAIL>", "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", "bio": "Frontend Developer e UI/UX Designer apaixonada por tecnologias web modernas.", "role": "editor", "socialLinks": {"twitter": "https://twitter.com/mariasantos", "linkedin": "https://linkedin.com/in/maria<PERSON>tos", "github": "https://github.com/mariasantos"}, "postsCount": 22, "joinedAt": "2022-08-20"}, "date": "2025-06-11", "category": {"id": "cat-2", "name": "Frontend", "slug": "frontend", "description": "Desenvolvimento frontend, frameworks e bibliotecas modernas", "color": "#ff00ff", "icon": "💻", "postsCount": 35}, "tags": [{"id": "tag-10", "name": "React", "slug": "react", "color": "#61dafb", "postsCount": 25}, {"id": "tag-11", "name": "JavaScript", "slug": "javascript", "color": "#f7df1e", "postsCount": 30}, {"id": "tag-12", "name": "Frontend", "slug": "frontend-tag", "color": "#ff00ff", "postsCount": 35}], "thumbnail": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop", "excerpt": "O React 19 chegou com mudanças significativas. Descubra as principais novidades, breaking changes e como migrar seus projetos para a nova versão.", "content": "<p>O React 19 representa uma das atualizações mais significativas da biblioteca nos últimos anos...</p>", "featured": false, "published": true, "views": 1580, "likes": 112, "readTime": 7, "seo": {"metaTitle": "React 19: <PERSON><PERSON><PERSON> de Migração e Novidades", "metaDescription": "Tudo sobre o React 19: novidades, breaking changes e guia de migração completo para desenvolvedores.", "keywords": ["React", "React 19", "JavaScript", "frontend", "migra<PERSON>"], "ogImage": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=1200&h=630&fit=crop", "ogTitle": "React 19: Novidades e Breaking Changes", "ogDescription": "O React 19 chegou com mudanças significativas. Descubra as principais novidades.", "twitterCard": "summary_large_image"}, "createdAt": "2025-06-11T16:45:00Z", "updatedAt": "2025-06-11T16:45:00Z"}]