# Blueprint Blog - Robots.txt
# https://blueprintblog.tech/robots.txt

# Permitir todos os crawlers para conteúdo público
User-agent: *
Allow: /

# Bloquear áreas administrativas e privadas
Disallow: /admin/
Disallow: /login
Disallow: /api/
Disallow: /_next/
Disallow: /static/
Disallow: /assets/

# Bloquear arquivos temporários e de desenvolvimento
Disallow: /*.json$
Disallow: /*.xml$
Disallow: /*.txt$
Disallow: /temp/
Disallow: /tmp/
Disallow: /.git/
Disallow: /node_modules/

# Permitir especificamente arquivos importantes
Allow: /sitemap.xml
Allow: /manifest.json
Allow: /robots.txt

# Crawl-delay para ser respeitoso com o servidor
Crawl-delay: 1

# Sitemap location
Sitemap: https://blueprintblog.tech/sitemap.xml

# Configurações específicas para diferentes bots

# Google Bot
User-agent: Googlebot
Allow: /
Disallow: /admin/
Disallow: /login
Crawl-delay: 1

# Bing Bot
User-agent: Bingbot
Allow: /
Disallow: /admin/
Disallow: /login
Crawl-delay: 2

# Facebook Bot (para Open Graph)
User-agent: facebookexternalhit
Allow: /
Disallow: /admin/

# Twitter Bot (para Twitter Cards)
User-agent: Twitterbot
Allow: /
Disallow: /admin/

# LinkedIn Bot
User-agent: LinkedInBot
Allow: /
Disallow: /admin/

# WhatsApp Bot
User-agent: WhatsApp
Allow: /
Disallow: /admin/

# Telegram Bot
User-agent: TelegramBot
Allow: /
Disallow: /admin/

# Bloquear bots maliciosos conhecidos
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: BLEXBot
Disallow: /

# Configurações para SEO
# Permitir indexação de imagens
User-agent: Googlebot-Image
Allow: /images/
Allow: /assets/images/
Allow: *.jpg
Allow: *.jpeg
Allow: *.png
Allow: *.gif
Allow: *.webp
Allow: *.svg

# Permitir indexação de vídeos (se houver)
User-agent: Googlebot-Video
Allow: /videos/
Allow: *.mp4
Allow: *.webm

# Configurações para mobile
User-agent: Googlebot-Mobile
Allow: /
Disallow: /admin/
Disallow: /login
