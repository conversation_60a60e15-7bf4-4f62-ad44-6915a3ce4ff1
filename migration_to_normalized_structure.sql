-- ============================================================================
-- MIGRAÇÃO COMPLETA PARA ESTRUTURA NORMALIZADA
-- Blueprint Blog v2 - Database Migration Script
-- ============================================================================
-- 
-- OBJETIVO: Migrar de estrutura híbrida (posts.tags array + category_id) 
--           para estrutura totalmente normalizada (post_tags + post_categories)
--
-- ATENÇÃO: Este script irá:
-- 1. Migrar dados existentes para tabelas normalizadas
-- 2. Remover campo posts.tags (array legacy)
-- 3. Manter posts.category_id como FK principal
-- 4. Limpar dados inconsistentes
--
-- ============================================================================

BEGIN;

-- ============================================================================
-- FASE 1: BACKUP DOS DADOS EXISTENTES
-- ============================================================================

-- Criar tabela temporária para backup dos posts
CREATE TEMP TABLE posts_backup AS 
SELECT * FROM posts;

-- Criar tabela temporária para backup das relações existentes
CREATE TEMP TABLE existing_post_tags AS 
SELECT * FROM post_tags;

CREATE TEMP TABLE existing_post_categories AS 
SELECT * FROM post_categories;

-- ============================================================================
-- FASE 2: MIGRAÇÃO DE TAGS (posts.tags → post_tags)
-- ============================================================================

-- Migrar tags do campo array para tabela normalizada
DO $$
DECLARE
    post_record RECORD;
    tag_uuid UUID;
    tag_name TEXT;
BEGIN
    -- Para cada post que tem tags no campo array
    FOR post_record IN 
        SELECT id, tags 
        FROM posts 
        WHERE tags IS NOT NULL 
        AND array_length(tags, 1) > 0
    LOOP
        -- Para cada tag no array
        FOR i IN 1..array_length(post_record.tags, 1)
        LOOP
            tag_name := post_record.tags[i];
            
            -- Se a tag é um UUID (36 caracteres), usar diretamente
            IF length(tag_name) = 36 AND tag_name ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
                tag_uuid := tag_name::UUID;
                
                -- Verificar se a tag existe
                IF EXISTS (SELECT 1 FROM tags WHERE id = tag_uuid) THEN
                    -- Inserir na post_tags se não existir
                    INSERT INTO post_tags (post_id, tag_id)
                    VALUES (post_record.id, tag_uuid)
                    ON CONFLICT (post_id, tag_id) DO NOTHING;
                END IF;
            ELSE
                -- Se é nome da tag, buscar o UUID
                SELECT id INTO tag_uuid 
                FROM tags 
                WHERE name = tag_name 
                LIMIT 1;
                
                IF tag_uuid IS NOT NULL THEN
                    -- Inserir na post_tags se não existir
                    INSERT INTO post_tags (post_id, tag_id)
                    VALUES (post_record.id, tag_uuid)
                    ON CONFLICT (post_id, tag_id) DO NOTHING;
                ELSE
                    -- Criar tag se não existir
                    INSERT INTO tags (name, slug, color, is_active)
                    VALUES (
                        tag_name,
                        lower(replace(replace(tag_name, ' ', '-'), '_', '-')),
                        '#3B82F6',
                        true
                    )
                    ON CONFLICT (name) DO NOTHING
                    RETURNING id INTO tag_uuid;
                    
                    -- Se conseguiu criar/encontrar, inserir relação
                    IF tag_uuid IS NOT NULL THEN
                        INSERT INTO post_tags (post_id, tag_id)
                        VALUES (post_record.id, tag_uuid)
                        ON CONFLICT (post_id, tag_id) DO NOTHING;
                    END IF;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- ============================================================================
-- FASE 3: MIGRAÇÃO DE CATEGORIAS (category_id → post_categories)
-- ============================================================================

-- Migrar categorias do campo FK para tabela normalizada
INSERT INTO post_categories (post_id, category_id)
SELECT id, category_id
FROM posts 
WHERE category_id IS NOT NULL
ON CONFLICT (post_id, category_id) DO NOTHING;

-- ============================================================================
-- FASE 4: LIMPEZA DA ESTRUTURA LEGACY
-- ============================================================================

-- Remover campo tags (array) da tabela posts
ALTER TABLE posts DROP COLUMN IF EXISTS tags;

-- ============================================================================
-- FASE 5: OTIMIZAÇÕES E ÍNDICES
-- ============================================================================

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_post_tags_post_id ON post_tags(post_id);
CREATE INDEX IF NOT EXISTS idx_post_tags_tag_id ON post_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_post_categories_post_id ON post_categories(post_id);
CREATE INDEX IF NOT EXISTS idx_post_categories_category_id ON post_categories(category_id);

-- Índices para busca de tags
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_slug ON tags(slug);
CREATE INDEX IF NOT EXISTS idx_tags_active ON tags(is_active);

-- Índices para busca de categorias
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- ============================================================================
-- FASE 6: VALIDAÇÃO DOS DADOS
-- ============================================================================

-- Verificar se todos os posts têm pelo menos uma categoria
DO $$
DECLARE
    posts_without_category INTEGER;
    default_category_id UUID;
BEGIN
    -- Contar posts sem categoria
    SELECT COUNT(*) INTO posts_without_category
    FROM posts p
    LEFT JOIN post_categories pc ON p.id = pc.post_id
    WHERE pc.post_id IS NULL AND p.category_id IS NULL;
    
    IF posts_without_category > 0 THEN
        -- Criar categoria padrão se não existir
        INSERT INTO categories (name, slug, description, color)
        VALUES ('Geral', 'geral', 'Categoria padrão para posts sem categoria específica', '#6B7280')
        ON CONFLICT (slug) DO NOTHING
        RETURNING id INTO default_category_id;
        
        -- Se não conseguiu inserir, buscar a existente
        IF default_category_id IS NULL THEN
            SELECT id INTO default_category_id FROM categories WHERE slug = 'geral';
        END IF;
        
        -- Atribuir categoria padrão aos posts sem categoria
        UPDATE posts 
        SET category_id = default_category_id
        WHERE category_id IS NULL;
        
        -- Inserir na tabela normalizada também
        INSERT INTO post_categories (post_id, category_id)
        SELECT id, default_category_id
        FROM posts 
        WHERE id NOT IN (SELECT post_id FROM post_categories)
        ON CONFLICT (post_id, category_id) DO NOTHING;
    END IF;
END $$;

-- ============================================================================
-- FASE 7: RELATÓRIO DE MIGRAÇÃO
-- ============================================================================

-- Gerar relatório da migração
DO $$
DECLARE
    total_posts INTEGER;
    posts_with_tags INTEGER;
    posts_with_categories INTEGER;
    total_tags INTEGER;
    total_categories INTEGER;
    total_post_tag_relations INTEGER;
    total_post_category_relations INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_posts FROM posts;
    SELECT COUNT(DISTINCT post_id) INTO posts_with_tags FROM post_tags;
    SELECT COUNT(DISTINCT post_id) INTO posts_with_categories FROM post_categories;
    SELECT COUNT(*) INTO total_tags FROM tags WHERE is_active = true;
    SELECT COUNT(*) INTO total_categories FROM categories;
    SELECT COUNT(*) INTO total_post_tag_relations FROM post_tags;
    SELECT COUNT(*) INTO total_post_category_relations FROM post_categories;
    
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'RELATÓRIO DE MIGRAÇÃO - ESTRUTURA NORMALIZADA';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'Total de posts: %', total_posts;
    RAISE NOTICE 'Posts com tags: %', posts_with_tags;
    RAISE NOTICE 'Posts com categorias: %', posts_with_categories;
    RAISE NOTICE 'Total de tags ativas: %', total_tags;
    RAISE NOTICE 'Total de categorias: %', total_categories;
    RAISE NOTICE 'Relações post-tag criadas: %', total_post_tag_relations;
    RAISE NOTICE 'Relações post-categoria criadas: %', total_post_category_relations;
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'MIGRAÇÃO CONCLUÍDA COM SUCESSO!';
    RAISE NOTICE '============================================================================';
END $$;

COMMIT;

-- ============================================================================
-- COMANDOS DE VERIFICAÇÃO PÓS-MIGRAÇÃO
-- ============================================================================

-- Verificar estrutura final
-- SELECT 'Posts' as tabela, COUNT(*) as total FROM posts
-- UNION ALL
-- SELECT 'Tags', COUNT(*) FROM tags WHERE is_active = true
-- UNION ALL  
-- SELECT 'Categorias', COUNT(*) FROM categories
-- UNION ALL
-- SELECT 'Post-Tags', COUNT(*) FROM post_tags
-- UNION ALL
-- SELECT 'Post-Categorias', COUNT(*) FROM post_categories;

-- Verificar posts com suas tags e categorias
-- SELECT 
--     p.title,
--     c.name as categoria,
--     string_agg(t.name, ', ') as tags
-- FROM posts p
-- LEFT JOIN categories c ON p.category_id = c.id
-- LEFT JOIN post_tags pt ON p.id = pt.post_id
-- LEFT JOIN tags t ON pt.tag_id = t.id
-- GROUP BY p.id, p.title, c.name
-- ORDER BY p.created_at DESC;
