# 🚀 Blueprint Blog v2

Um blog moderno e responsivo com estética cyberpunk neon, construído com React, TypeScript e Vite.

## ✨ Características

### 🎨 Design & UX

- **Above-the-Fold Layout**: Hero Post (2/3) + Top do Mês (1/3) lado a lado
- **Cards Hero-Style**: Imagens de fundo com overlays e gradientes
- **Estética Cyberpunk**: Paleta focada em neon-cyan para visual limpo
- **Totalmente Responsivo**: Adaptável a todos os dispositivos
- **Performance Otimizada**: Carregamento rápido sem animações desnecessárias

### 🛠️ Tecnologias

- **React 18** com TypeScript
- **Vite** para build e desenvolvimento
- **Tailwind CSS** para estilização
- **Framer Motion** para animações essenciais
- **React Icons** para ícones otimizados
- **React Router** para navegação
- **Supabase** para backend e banco de dados
- **React Helmet Async** para SEO dinâmico

### 📱 Componentes Principais

- **HeroPost**: Card principal em destaque
- **TopOfMonth**: Ranking dos posts mais populares
- **PostSlider**: Carousel com cards estilo hero
- **Breadcrumbs**: Navegação hierárquica com 4 variantes
- **SEOHead**: Gerenciamento dinâmico de meta tags
- **Layout Responsivo**: Grid adaptável e navegação intuitiva

## 🚀 Instalação e Uso

### Pré-requisitos

- Node.js 18+
- npm ou yarn

### Instalação

```bash
# Clone o repositório
git clone <repository-url>
cd blueprintblog-v2

# Instale as dependências
npm install

# Inicie o servidor de desenvolvimento
npm run dev
```

### Scripts Disponíveis

```bash
npm run dev          # Servidor de desenvolvimento
npm run build        # Build para produção
npm run preview      # Preview do build
npm run lint         # Verificação de código
```

## 📁 Estrutura do Projeto

```
src/
├── components/
│   ├── layout/          # Componentes de layout
│   ├── seo/             # Componentes SEO (SEOHead, JSON-LD)
│   └── ui/              # Componentes reutilizáveis
├── hooks/               # Hooks customizados
├── pages/               # Páginas da aplicação
├── types/               # Definições TypeScript
├── utils/               # Utilitários e helpers (incluindo seoUtils)
├── lib/                 # Configurações (Supabase)
└── data/                # Dados mockados
```

## 🎯 Funcionalidades

### ✅ Implementado

- [x] Layout Above-the-Fold otimizado
- [x] Sistema de componentes reutilizáveis
- [x] Cards hero-style com overlays
- [x] Carousel de posts recentes
- [x] Sistema de categorias e tags
- [x] Responsividade completa
- [x] Performance otimizada
- [x] **Integração com Supabase** completa
- [x] **Sistema de autenticação** funcional
- [x] **Painel administrativo** completo
- [x] **SEO profissional** implementado
  - [x] Meta tags dinâmicas (Open Graph, Twitter Cards)
  - [x] Dados estruturados JSON-LD (Article, Organization, Breadcrumbs)
  - [x] Componentes SEO reutilizáveis
  - [x] Breadcrumbs visuais com 4 variantes
  - [x] URLs canônicas e keywords automáticas
  - [x] Arquivos estáticos (robots.txt, manifest.json, favicon)

### 🔄 Em Desenvolvimento

- [ ] Sistema de comentários
- [ ] Analytics avançado
- [ ] Newsletter integration
- [ ] PWA completo

## 🎨 Paleta de Cores

```css
/* Cores principais */
--neon-cyan: #00ffff        /* Cor principal */
--cyber-bg: #0a0a0a         /* Fundo escuro */
--cyber-text: #ffffff       /* Texto principal */
--cyber-muted: #888888      /* Texto secundário */
--cyber-surface: #1a1a1a    /* Superfícies */
```

## 📊 Performance

- **Carregamento**: < 2s
- **Animações**: Apenas essenciais
- **Bundle Size**: 345.22 kB (109.86 kB gzipped)
- **Responsividade**: 100% mobile-first
- **SEO Score**: Otimizado para 100/100 no Lighthouse
- **Database**: Integração otimizada com Supabase

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

---

**Blueprint Blog v2** - Desenvolvido com ❤️ e ⚡ neon cyberpunk
