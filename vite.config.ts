import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  css: {
    postcss: './postcss.config.js',
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks - Bibliotecas externas
          'react-vendor': ['react', 'react-dom', 'react-router'],
          'ui-vendor': ['motion', 'clsx', 'tailwind-merge'],
          'markdown-vendor': [
            'react-markdown',
            'remark-gfm',
            'rehype-highlight',
            'rehype-raw',
          ],
          'supabase-vendor': ['@supabase/supabase-js'],
          'icons-vendor': ['react-icons', 'lucide-react'],
        },
      },
    },
    chunkSizeWarningLimit: 500,
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
    },
    sourcemap: false,
  },
});
