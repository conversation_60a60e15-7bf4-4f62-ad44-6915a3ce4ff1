@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Nunito Sans', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  color-scheme: dark;
  color: #f0f6fc;
  background-color: #0d1117;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Custom CSS Variables for Cyberpunk Theme */
  --neon-cyan: #00ffff;
  --neon-magenta: #ff00ff;
  --neon-purple: #8b5cf6;
  --cyber-bg: #0d1117;
  --cyber-surface: #161b22;
  --cyber-border: #30363d;
  --cyber-text: #f0f6fc;
  --cyber-muted: #8b949e;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  min-height: 100vh;
  background: var(--cyber-bg);
  color: var(--cyber-text);
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--cyber-surface);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-magenta));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--neon-magenta), var(--neon-purple));
}

/* Selection */
::selection {
  background: var(--neon-cyan);
  color: var(--cyber-bg);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--neon-cyan);
  outline-offset: 2px;
}

/* Links */
a {
  color: var(--neon-cyan);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--neon-magenta);
  text-shadow: 0 0 10px currentColor;
}

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--cyber-text);
}

/* Utility Classes */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.border-glow {
  box-shadow: 0 0 10px var(--neon-cyan);
}

.bg-cyber-gradient {
  background: linear-gradient(
    135deg,
    var(--cyber-bg) 0%,
    var(--cyber-surface) 100%
  );
}

.neon-border {
  border: 1px solid var(--neon-cyan);
  box-shadow: 0 0 5px var(--neon-cyan), inset 0 0 5px var(--neon-cyan);
}

/* Animation for neon effects */
@keyframes neon-pulse {
  0%,
  100% {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor,
      0 0 15px currentColor;
  }
  50% {
    text-shadow: 0 0 2px currentColor, 0 0 5px currentColor,
      0 0 8px currentColor;
  }
}
