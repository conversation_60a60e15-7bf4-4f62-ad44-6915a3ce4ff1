/**
 * @fileoverview Contexto para gerenciar notificações toast
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { createContext, useContext, type ReactNode } from 'react';
import toast, { Toaster, type ToastOptions } from 'react-hot-toast';

// ============================================================================
// INTERFACES
// ============================================================================

interface ToastContextType {
  success: (message: string, options?: ToastOptions) => void;
  error: (message: string, options?: ToastOptions) => void;
  loading: (message: string, options?: ToastOptions) => string;
  dismiss: (toastId?: string) => void;
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => Promise<T>;
}

interface ToastProviderProps {
  children: ReactNode;
}

// ============================================================================
// CONTEXTO
// ============================================================================

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// ============================================================================
// CONFIGURAÇÕES CUSTOMIZADAS
// ============================================================================

const defaultToastOptions: ToastOptions = {
  duration: 4000,
  position: 'top-right',
  style: {
    background: '#0a0a0a',
    color: '#00ffff',
    border: '1px solid #00ffff33',
    borderRadius: '8px',
    fontSize: '14px',
    fontFamily: 'Inter, system-ui, sans-serif',
  },
};

const successToastOptions: ToastOptions = {
  ...defaultToastOptions,
  style: {
    ...defaultToastOptions.style,
    color: '#00ff88',
    border: '1px solid #00ff8833',
  },
  iconTheme: {
    primary: '#00ff88',
    secondary: '#0a0a0a',
  },
};

const errorToastOptions: ToastOptions = {
  ...defaultToastOptions,
  style: {
    ...defaultToastOptions.style,
    color: '#ff4444',
    border: '1px solid #ff444433',
  },
  iconTheme: {
    primary: '#ff4444',
    secondary: '#0a0a0a',
  },
};

const loadingToastOptions: ToastOptions = {
  ...defaultToastOptions,
  style: {
    ...defaultToastOptions.style,
    color: '#ffaa00',
    border: '1px solid #ffaa0033',
  },
};

// ============================================================================
// PROVIDER
// ============================================================================

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const success = (message: string, options?: ToastOptions) => {
    toast.success(message, { ...successToastOptions, ...options });
  };

  const error = (message: string, options?: ToastOptions) => {
    toast.error(message, { ...errorToastOptions, ...options });
  };

  const loading = (message: string, options?: ToastOptions): string => {
    return toast.loading(message, { ...loadingToastOptions, ...options });
  };

  const dismiss = (toastId?: string) => {
    toast.dismiss(toastId);
  };

  const promise = <T,>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ): Promise<T> => {
    return toast.promise(promise, messages, {
      loading: { ...loadingToastOptions, ...options },
      success: { ...successToastOptions, ...options },
      error: { ...errorToastOptions, ...options },
    });
  };

  const value: ToastContextType = {
    success,
    error,
    loading,
    dismiss,
    promise,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <Toaster
        position="top-right"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{
          top: 20,
          right: 20,
        }}
        toastOptions={{
          className: '',
          duration: 4000,
          style: {
            background: '#0a0a0a',
            color: '#00ffff',
            border: '1px solid #00ffff33',
            borderRadius: '8px',
            fontSize: '14px',
            fontFamily: 'Inter, system-ui, sans-serif',
            boxShadow: '0 4px 12px rgba(0, 255, 255, 0.15)',
          },
          success: {
            style: {
              color: '#00ff88',
              border: '1px solid #00ff8833',
              boxShadow: '0 4px 12px rgba(0, 255, 136, 0.15)',
            },
            iconTheme: {
              primary: '#00ff88',
              secondary: '#0a0a0a',
            },
          },
          error: {
            style: {
              color: '#ff4444',
              border: '1px solid #ff444433',
              boxShadow: '0 4px 12px rgba(255, 68, 68, 0.15)',
            },
            iconTheme: {
              primary: '#ff4444',
              secondary: '#0a0a0a',
            },
          },
          loading: {
            style: {
              color: '#ffaa00',
              border: '1px solid #ffaa0033',
              boxShadow: '0 4px 12px rgba(255, 170, 0, 0.15)',
            },
          },
        }}
      />
    </ToastContext.Provider>
  );
};

// ============================================================================
// HOOK
// ============================================================================

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast deve ser usado dentro de um ToastProvider');
  }
  return context;
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Toast para upload de arquivo
 */
export const uploadToast = {
  start: (fileName: string) => {
    return toast.loading(`📤 Enviando ${fileName}...`, loadingToastOptions);
  },

  success: (fileName: string, toastId: string) => {
    toast.success(`✅ ${fileName} enviado com sucesso!`, {
      ...successToastOptions,
      id: toastId,
    });
  },

  error: (fileName: string, error: string, toastId: string) => {
    toast.error(`❌ Erro ao enviar ${fileName}: ${error}`, {
      ...errorToastOptions,
      id: toastId,
    });
  },
};

/**
 * Toast para operações de arquivo
 */
export const fileToast = {
  deleted: (fileName: string) => {
    toast.success(`🗑️ ${fileName} deletado com sucesso!`, successToastOptions);
  },

  copied: (fileName: string) => {
    toast.success(`📋 URL de ${fileName} copiada!`, successToastOptions);
  },

  deleteError: (fileName: string, error: string) => {
    toast.error(`❌ Erro ao deletar ${fileName}: ${error}`, errorToastOptions);
  },
};

export default ToastProvider;
