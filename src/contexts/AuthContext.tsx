/**
 * @fileoverview Context de Autenticação do Blueprint Blog v2 - Integrado com Supabase
 * Migrado do v1 conforme PLANO_MIGRACAO_V1_V2.md
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';
import {
  supabaseAuthService,
  type AuthUser,
} from '../services/supabaseAuthService';
import { ComponentLogger } from '../utils/component-logger';
import { logger } from '../utils/logger';

// ===== INTERFACES =====
interface AuthContextValue {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (
    email: string,
    password: string,
    metadata?: UserMetadata
  ) => Promise<AuthResult>;
  signOut: () => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updatePassword: (newPassword: string) => Promise<{ error: string | null }>;
  // Métodos de compatibilidade com v2
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<AuthUser>) => Promise<void>;
  status: 'loading' | 'authenticated' | 'unauthenticated';
}

interface AuthResult {
  user: AuthUser | null;
  error: string | null;
}

interface UserMetadata {
  name?: string;
  [key: string]: any;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface AuthProviderProps {
  children: ReactNode;
}

// ===== CONTEXT =====
const AuthContext = createContext<AuthContextValue | undefined>(undefined);

// ===== PROVIDER =====
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Status derivado do user e loading
  const status: 'loading' | 'authenticated' | 'unauthenticated' = loading
    ? 'loading'
    : user
    ? 'authenticated'
    : 'unauthenticated';

  useEffect(() => {
    ComponentLogger.logMount('AuthProvider');

    // Verificar sessão atual
    getSession();

    // Escutar mudanças de autenticação
    const {
      data: { subscription },
    } = supabaseAuthService.onAuthStateChange((authUser) => {
      setUser(authUser);
      setLoading(false);
    });

    return () => {
      ComponentLogger.logUnmount('AuthProvider');
      subscription.unsubscribe();
    };
  }, []);

  const getSession = async (): Promise<void> => {
    try {
      const { user: authUser, error } =
        await supabaseAuthService.getCurrentUser();

      if (error) {
        // Só logar se não for erro de sessão ausente
        if (
          !error.includes('Auth session missing') &&
          !error.includes('session missing')
        ) {
          console.error('Erro ao obter sessão:', error);
        }
        setUser(null);
      } else {
        setUser(authUser);
      }
    } catch (error) {
      // Só logar se não for erro de sessão ausente
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      if (
        !errorMessage.includes('Auth session missing') &&
        !errorMessage.includes('session missing')
      ) {
        console.error('Erro ao obter sessão:', error);
      }
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // ===== MÉTODOS SUPABASE ORIGINAIS =====
  const signIn = async (
    email: string,
    password: string
  ): Promise<AuthResult> => {
    logger.info('Tentativa de login', { email });
    try {
      setLoading(true);
      const result = await supabaseAuthService.signIn(email, password);

      if (result.user) {
        setUser(result.user);
        logger.setUserId(result.user.id);
        logger.info('Login realizado com sucesso', { userId: result.user.id });
      } else if (result.error) {
        logger.warn('Falha no login', { email, error: result.error });
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      logger.error('Erro no login', { email, error: errorMessage });
      return {
        user: null,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string,
    password: string,
    metadata: UserMetadata = {}
  ): Promise<AuthResult> => {
    try {
      setLoading(true);
      const result = await supabaseAuthService.signUp(
        email,
        password,
        metadata
      );

      if (result.user) {
        setUser(result.user);
      }

      return result;
    } catch (error) {
      console.error('Erro no registro:', error);
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<{ error: string | null }> => {
    logger.info('Tentativa de logout', { userId: user?.id });
    try {
      setLoading(true);
      const result = await supabaseAuthService.signOut();

      if (!result.error) {
        logger.info('Logout realizado com sucesso', { userId: user?.id });
        setUser(null);
      } else {
        logger.warn('Falha no logout', { error: result.error });
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      logger.error('Erro no logout', { error: errorMessage });
      return {
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (
    email: string
  ): Promise<{ error: string | null }> => {
    try {
      return await supabaseAuthService.resetPassword(email);
    } catch (error) {
      console.error('Erro ao resetar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  };

  const updatePassword = async (
    newPassword: string
  ): Promise<{ error: string | null }> => {
    try {
      return await supabaseAuthService.updatePassword(newPassword);
    } catch (error) {
      console.error('Erro ao atualizar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  };

  // ===== MÉTODOS DE COMPATIBILIDADE COM V2 =====
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    const result = await signIn(credentials.email, credentials.password);
    return result.user !== null && result.error === null;
  };

  const logout = async (): Promise<void> => {
    await signOut();
  };

  const updateUser = async (userData: Partial<AuthUser>) => {
    if (!user) return;

    try {
      // Atualiza no Supabase
      const { error } = await supabaseAuthService.updateUser(userData);

      if (error) {
        console.error('Erro ao atualizar usuário:', error);
        return;
      }

      // Atualiza o estado local
      setUser({ ...user, ...userData });
    } catch (error) {
      console.error('Erro ao atualizar usuário:', error);
    }
  };

  const value: AuthContextValue = {
    user,
    loading,
    status,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    login,
    logout,
    updateUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// ===== HOOK =====
export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

// ===== EXPORT DEFAULT =====
export default AuthContext;

// ===== EXPORTS DE TIPOS =====
export type {
  AuthContextValue,
  AuthProviderProps,
  AuthResult,
  LoginCredentials,
  UserMetadata,
};
