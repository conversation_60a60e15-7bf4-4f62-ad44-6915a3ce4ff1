/**
 * @fileoverview Página Dashboard do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Card, CardContent, CardHeader } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import { useBlogStats } from '../hooks/usePostAnalytics';

// ============================================================================
// INTERFACES
// ============================================================================

interface DashboardProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Dashboard principal com métricas e ações rápidas
 */
export const Dashboard: React.FC<DashboardProps> = ({ className }) => {
  const { user } = useAuth();

  // Usar dados reais do Supabase via useBlogStats
  const { stats: blogStats, loading, error } = useBlogStats();

  if (!user) return null;

  // Tratamento de erro
  if (error) {
    console.error('Erro ao carregar estatísticas:', error);
  }

  return (
    <div className={clsx('space-y-8', className)}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-cyber-text mb-2">
          📊 Dashboard
        </h1>
        <p className="text-cyber-muted">
          Visão geral das métricas e atividades do blog
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card neonBorder>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Total de Posts</p>
                <p className="text-2xl font-bold text-cyber-text">
                  {loading ? '...' : blogStats?.totalPosts || 0}
                </p>
              </div>
              <div className="text-3xl">📝</div>
            </div>
            <div className="mt-2 text-xs text-neon-cyan">
              Dados em tempo real
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">
                  Total de Visualizações
                </p>
                <p className="text-2xl font-bold text-cyber-text">
                  {loading
                    ? '...'
                    : (blogStats?.totalViews || 0).toLocaleString('pt-BR')}
                </p>
              </div>
              <div className="text-3xl">👁️</div>
            </div>
            <div className="mt-2 text-xs text-neon-magenta">
              Analytics integrado
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Posts Ativos</p>
                <p className="text-2xl font-bold text-cyber-text">
                  {loading
                    ? '...'
                    : (blogStats?.totalPosts || 0).toLocaleString('pt-BR')}
                </p>
              </div>
              <div className="text-3xl">✅</div>
            </div>
            <div className="mt-2 text-xs text-neon-yellow">Conteúdo ativo</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Média de Views</p>
                <p className="text-2xl font-bold text-cyber-text">
                  {loading
                    ? '...'
                    : (blogStats?.avgViewsPerPost || 0).toLocaleString('pt-BR')}
                </p>
              </div>
              <div className="text-3xl">📊</div>
            </div>
            <div className="mt-2 text-xs text-neon-purple">
              Performance média
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Atividade Recente */}
      <Card>
        <CardHeader
          title="📈 Atividade Recente"
          subtitle="Últimas ações no sistema"
        />
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="text-lg">✅</div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Login realizado</p>
                <p className="text-xs text-cyber-muted">Agora mesmo</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="text-lg">📊</div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Dashboard acessado</p>
                <p className="text-xs text-cyber-muted">2 minutos atrás</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="text-lg">🔐</div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Sessão iniciada</p>
                <p className="text-xs text-cyber-muted">5 minutos atrás</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
