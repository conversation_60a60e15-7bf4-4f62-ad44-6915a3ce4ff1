/**
 * @fileoverview Página Profile do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { Link } from 'react-router';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, Input } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';

// ============================================================================
// INTERFACES
// ============================================================================

interface ProfileProps {
  className?: string;
}

interface ProfileFormData {
  name: string;
  email: string;
  bio: string;
  avatar: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de perfil do usuário com edição de dados
 */
export const Profile: React.FC<ProfileProps> = ({ className }) => {
  const { user, updateUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    name: user?.name || '',
    email: user?.email || '',
    bio: user?.bio || '',
    avatar: user?.avatar || '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!user) return null;

  /**
   * Valida os dados do formulário
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.bio.length > 500) {
      newErrors.bio = 'Bio deve ter no máximo 500 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Salva as alterações do perfil
   */
  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);

    // Simula delay de API
    await new Promise((resolve) => setTimeout(resolve, 1000));

    updateUser(formData);
    setIsEditing(false);
    setLoading(false);
  };

  /**
   * Cancela a edição
   */
  const handleCancel = () => {
    setFormData({
      name: user.name,
      email: user.email,
      bio: user.bio || '',
      avatar: user.avatar,
    });
    setErrors({});
    setIsEditing(false);
  };

  /**
   * Atualiza campo do formulário
   */
  const handleChange = (field: keyof ProfileFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return {
          icon: '👑',
          label: 'Administrador',
          color: 'from-neon-cyan to-blue-400',
        };
      case 'editor':
        return {
          icon: '✏️',
          label: 'Editor',
          color: 'from-neon-magenta to-purple-400',
        };
      case 'writer':
        return {
          icon: '📝',
          label: 'Escritor',
          color: 'from-neon-yellow to-orange-400',
        };
      default:
        return {
          icon: '👤',
          label: 'Usuário',
          color: 'from-cyber-muted to-gray-400',
        };
    }
  };

  const roleBadge = getRoleBadge(user.role);

  return (
    <div className={clsx('max-w-4xl mx-auto space-y-8', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-cyber-text mb-2">
            👤 Meu Perfil
          </h1>
          <p className="text-cyber-muted">
            Gerencie suas informações pessoais e preferências
          </p>
        </div>
        <Button variant="secondary" size="sm">
          <Link to="/admin">← Voltar ao Dashboard</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Card */}
        <div className="lg:col-span-1">
          <Card neonBorder>
            <CardContent className="p-6 text-center">
              {/* Avatar */}
              <div className="relative mb-6">
                <img
                  src={formData.avatar}
                  alt={formData.name}
                  className="w-32 h-32 rounded-full mx-auto border-4 border-neon-cyan"
                />
                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r ${roleBadge.color} text-cyber-bg`}>
                    {roleBadge.icon} {roleBadge.label}
                  </span>
                </div>
              </div>

              {/* Basic Info */}
              <h2 className="text-xl font-bold text-cyber-text mb-2">
                {formData.name}
              </h2>
              <p className="text-cyber-muted mb-4">{formData.email}</p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-neon-cyan">
                    {user.role === 'admin'
                      ? '156'
                      : user.role === 'editor'
                      ? '89'
                      : '23'}
                  </div>
                  <div className="text-xs text-cyber-muted">Posts</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-neon-magenta">
                    {user.role === 'admin'
                      ? '45K'
                      : user.role === 'editor'
                      ? '28K'
                      : '8K'}
                  </div>
                  <div className="text-xs text-cyber-muted">Views</div>
                </div>
              </div>

              {/* Member Since */}
              <div className="text-xs text-cyber-muted">
                Membro desde{' '}
                {new Date(user.createdAt).toLocaleDateString('pt-BR')}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Profile Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader
              title="📝 Informações Pessoais"
              subtitle={isEditing ? 'Editando perfil' : 'Visualizando perfil'}
            />
            <CardContent className="space-y-6">
              {/* Nome */}
              <div>
                <Input
                  label="Nome Completo"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  error={errors.name}
                  disabled={!isEditing}
                  leftIcon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  }
                  fullWidth
                />
              </div>

              {/* Email */}
              <div>
                <Input
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  error={errors.email}
                  disabled={!isEditing}
                  leftIcon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                  }
                  fullWidth
                />
              </div>

              {/* Bio */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Bio
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => handleChange('bio', e.target.value)}
                  disabled={!isEditing}
                  placeholder="Conte um pouco sobre você..."
                  rows={4}
                  className={clsx(
                    'w-full px-3 py-2 rounded-lg transition-all duration-200',
                    'bg-cyber-surface border text-cyber-text placeholder:text-cyber-muted',
                    'focus:outline-none focus:ring-2 focus:ring-neon-cyan/50',
                    errors.bio
                      ? 'border-red-500 focus:border-red-500'
                      : 'border-cyber-border focus:border-neon-cyan',
                    !isEditing && 'opacity-50 cursor-not-allowed'
                  )}
                />
                {errors.bio && (
                  <p className="mt-1 text-sm text-red-400">{errors.bio}</p>
                )}
                <p className="mt-1 text-xs text-cyber-muted">
                  {formData.bio.length}/500 caracteres
                </p>
              </div>

              {/* Avatar URL */}
              <div>
                <Input
                  label="URL do Avatar"
                  value={formData.avatar}
                  onChange={(e) => handleChange('avatar', e.target.value)}
                  disabled={!isEditing}
                  placeholder="https://exemplo.com/avatar.jpg"
                  leftIcon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  }
                  fullWidth
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t border-cyber-border">
                {!isEditing ? (
                  <Button onClick={() => setIsEditing(true)} leftIcon="✏️">
                    Editar Perfil
                  </Button>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleCancel}
                      disabled={loading}>
                      Cancelar
                    </Button>
                    <Button
                      onClick={handleSave}
                      loading={loading}
                      leftIcon="💾">
                      {loading ? 'Salvando...' : 'Salvar Alterações'}
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Account Info */}
      <Card>
        <CardHeader
          title="🔐 Informações da Conta"
          subtitle="Dados do sistema e segurança"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-cyber-text mb-2">
                Nível de Acesso
              </h4>
              <p className="text-cyber-muted text-sm mb-4">
                Seu nível atual determina quais funcionalidades você pode
                acessar.
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span
                    className={
                      user.role === 'admin'
                        ? 'text-neon-cyan'
                        : 'text-cyber-muted'
                    }>
                    {user.role === 'admin' ? '✅' : '❌'}
                  </span>
                  <span className="text-sm">Administração completa</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={
                      ['admin', 'editor'].includes(user.role)
                        ? 'text-neon-cyan'
                        : 'text-cyber-muted'
                    }>
                    {['admin', 'editor'].includes(user.role) ? '✅' : '❌'}
                  </span>
                  <span className="text-sm">Edição de posts</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={
                      ['admin', 'editor', 'writer'].includes(user.role)
                        ? 'text-neon-cyan'
                        : 'text-cyber-muted'
                    }>
                    {['admin', 'editor', 'writer'].includes(user.role)
                      ? '✅'
                      : '❌'}
                  </span>
                  <span className="text-sm">Criação de posts</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-cyber-text mb-2">Atividade</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-cyber-muted">Conta criada:</span>
                  <span className="text-cyber-text">
                    {new Date(user.createdAt).toLocaleDateString('pt-BR')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-cyber-muted">Último login:</span>
                  <span className="text-cyber-text">
                    {user.lastLogin
                      ? new Date(user.lastLogin).toLocaleString('pt-BR')
                      : 'Nunca'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-cyber-muted">Status:</span>
                  <span className="text-neon-cyan">🟢 Ativo</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Profile;
