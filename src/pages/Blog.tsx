/**
 * @fileoverview Página de Blog (lista de posts) do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Button, Card, CardContent, Input } from '../components/ui';
import { usePosts } from '../hooks/usePosts';
import useSEO from '../hooks/useSEO';
import { supabase } from '../lib/supabaseClient';
import type { Category } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface BlogProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de blog com lista de posts e filtros
 */
export const Blog: React.FC<BlogProps> = ({ className }) => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 6;

  // SEO para página de blog
  const selectedCategoryData = categories.find(
    (cat) => cat.id === selectedCategory
  );
  const seoData = useSEO({
    pageType: 'blog',
    customTitle: searchQuery
      ? `Busca: "${searchQuery}" | Blog Blueprint`
      : selectedCategoryData
      ? `${selectedCategoryData.name} | Blog Blueprint`
      : 'Blog | Blueprint - Artigos sobre Tecnologia',
    customDescription: searchQuery
      ? `Resultados da busca por "${searchQuery}" no Blueprint Blog. Artigos sobre tecnologia, desenvolvimento e inovação.`
      : selectedCategoryData
      ? `Artigos sobre ${selectedCategoryData.name.toLowerCase()} no Blueprint Blog. ${
          selectedCategoryData.description ||
          'Conteúdo especializado e atualizado.'
        }`
      : 'Explore todos os artigos do Blueprint Blog sobre tecnologia, programação, design e inovação digital.',
    customKeywords: [
      'blog',
      'artigos',
      'tecnologia',
      'programação',
      ...(searchQuery ? [searchQuery] : []),
      ...(selectedCategoryData
        ? [selectedCategoryData.name.toLowerCase()]
        : []),
    ],
  });

  // Usar hook real do Supabase para posts
  const { posts, loading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });

  // Carrega categorias do Supabase
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .order('name');

        if (categoriesError) {
          console.error('Erro ao carregar categorias:', categoriesError);
          return;
        }

        // Calcular postsCount para cada categoria
        const categoriesWithCount = (categoriesData || []).map((category) => ({
          ...category,
          postsCount: posts.filter((post) => post.category.id === category.id)
            .length,
        }));

        setCategories(categoriesWithCount);
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    };

    if (posts.length > 0) {
      loadCategories();
    }
  }, [posts]);

  // Filtra posts baseado na busca e categoria
  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === 'all' || post.category.id === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando filtros mudam
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory]);

  // Função para navegar para o post
  const handlePostClick = (post: any) => {
    navigate(`/post/${post.slug}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando posts...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="blog"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Blog', url: '/blog' },
        ]}
      />

      <div className={clsx('space-y-8', className)}>
        {/* Header */}
        <header className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-4">
            📝 Blog
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Artigos sobre tecnologia, desenvolvimento e inovação digital
          </p>
        </header>

        {/* Filters */}
        <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="flex-1 w-full lg:w-auto">
              <Input
                type="search"
                placeholder="Buscar posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
                fullWidth
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory('all')}
                className={clsx(
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                  selectedCategory === 'all'
                    ? 'bg-neon-cyan text-cyber-bg'
                    : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                )}>
                Todas
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={clsx(
                    'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                    selectedCategory === category.id
                      ? 'text-cyber-bg border'
                      : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                  )}
                  style={{
                    backgroundColor:
                      selectedCategory === category.id
                        ? category.color
                        : undefined,
                    borderColor:
                      selectedCategory === category.id
                        ? category.color
                        : undefined,
                  }}>
                  <div
                    className="w-2 h-2 rounded-full mr-1"
                    style={{ backgroundColor: category.color }}
                  />
                  <span>{category.name}</span>
                  <span className="text-xs opacity-75">
                    ({category.postsCount})
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Results Info */}
          <div className="mt-4 text-sm text-cyber-muted">
            {filteredPosts.length === 0
              ? 'Nenhum post encontrado'
              : `${filteredPosts.length} post${
                  filteredPosts.length !== 1 ? 's' : ''
                } encontrado${filteredPosts.length !== 1 ? 's' : ''}`}
          </div>
        </section>

        {/* Posts Grid */}
        {paginatedPosts.length > 0 ? (
          <section>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedPosts.map((post) => (
                <Card
                  key={post.id}
                  className="group cursor-pointer"
                  hoverable
                  onClick={() => handlePostClick(post)}>
                  <div className="aspect-video bg-gradient-cyber rounded-t-lg mb-4 relative overflow-hidden">
                    <img
                      src={post.thumbnail}
                      alt={post.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-3 left-3">
                      <span
                        className="px-2 py-1 rounded text-xs font-medium"
                        style={{
                          backgroundColor: `${post.category.color}20`,
                          color: post.category.color,
                          border: `1px solid ${post.category.color}30`,
                        }}>
                        {post.category.name}
                      </span>
                    </div>
                    <div className="absolute bottom-3 right-3 bg-cyber-bg/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-cyber-muted">
                      {post.readTime} min
                    </div>
                    {post.featured && (
                      <div className="absolute top-3 right-3">
                        <span className="bg-neon-yellow text-cyber-bg px-2 py-1 rounded text-xs font-bold">
                          ⭐ DESTAQUE
                        </span>
                      </div>
                    )}
                  </div>

                  <CardContent>
                    <h3 className="text-lg font-bold text-cyber-text mb-2 group-hover:text-neon-cyan transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-cyber-muted text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <img
                          src={post.author.avatar}
                          alt={post.author.name}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-xs text-cyber-muted">
                          {post.author.name}
                        </span>
                      </div>
                      <span className="text-xs text-cyber-muted">
                        {new Date(post.date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-xs text-cyber-muted">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center space-x-1">
                          <span>👁️</span>
                          <span>{post.views}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <span>❤️</span>
                          <span>{post.likes}</span>
                        </span>
                      </div>
                      <Button size="xs" variant="secondary">
                        Ler mais →
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        ) : (
          <section className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-cyber-text mb-2">
              Nenhum post encontrado
            </h3>
            <p className="text-cyber-muted mb-6">
              Tente ajustar os filtros ou buscar por outros termos
            </p>
            <Button
              variant="secondary"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
              }}>
              Limpar Filtros
            </Button>
          </section>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <section className="flex justify-center">
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}>
                ← Anterior
              </Button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={clsx(
                      'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                      currentPage === page
                        ? 'bg-neon-cyan text-cyber-bg'
                        : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                    )}>
                    {page}
                  </button>
                )
              )}

              <Button
                variant="secondary"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}>
                Próxima →
              </Button>
            </div>
          </section>
        )}
      </div>
    </>
  );
};

export default Blog;
