/**
 * @fileoverview Página Settings do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { Link } from 'react-router';
import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader } from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import type { UserPreferences } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface SettingsProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de configurações do usuário
 */
export const Settings: React.FC<SettingsProps> = ({ className }) => {
  const { user, updateUser, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferences>(
    user?.preferences || {
      theme: 'dark',
      language: 'pt',
      notifications: {
        email: true,
        push: true,
        comments: true,
        mentions: true,
      },
    }
  );

  if (!user) return null;

  /**
   * Salva as preferências
   */
  const handleSavePreferences = async () => {
    setLoading(true);

    // Simula delay de API
    await new Promise((resolve) => setTimeout(resolve, 1000));

    updateUser({ preferences });
    setLoading(false);
  };

  /**
   * Atualiza preferência
   */
  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setPreferences((prev) => ({ ...prev, [key]: value }));
  };

  /**
   * Atualiza notificação específica
   */
  const updateNotification = (
    key: keyof UserPreferences['notifications'],
    value: boolean
  ) => {
    setPreferences((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value,
      },
    }));
  };

  /**
   * Limpa dados locais
   */
  const handleClearData = () => {
    if (
      confirm(
        'Tem certeza que deseja limpar todos os dados locais? Esta ação não pode ser desfeita.'
      )
    ) {
      localStorage.clear();
      logout();
    }
  };

  return (
    <div className={clsx('max-w-4xl mx-auto space-y-8', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-cyber-text mb-2">
            ⚙️ Configurações
          </h1>
          <p className="text-cyber-muted">
            Personalize sua experiência no Blueprint Blog
          </p>
        </div>
        <Button variant="secondary" size="sm">
          <Link to="/admin">← Voltar ao Dashboard</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Aparência */}
        <Card>
          <CardHeader title="🎨 Aparência" subtitle="Personalize a interface" />
          <CardContent className="space-y-6">
            {/* Tema */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-3">
                Tema
              </label>
              <div className="space-y-2">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="theme"
                    value="dark"
                    checked={preferences.theme === 'dark'}
                    onChange={(e) =>
                      updatePreference(
                        'theme',
                        e.target.value as 'dark' | 'light'
                      )
                    }
                    className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border focus:ring-neon-cyan"
                  />
                  <span className="text-cyber-text">🌙 Escuro (Cyberpunk)</span>
                </label>
                <label className="flex items-center space-x-3 cursor-pointer opacity-50">
                  <input
                    type="radio"
                    name="theme"
                    value="light"
                    disabled
                    className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border focus:ring-neon-cyan"
                  />
                  <span className="text-cyber-text">☀️ Claro (Em breve)</span>
                </label>
              </div>
            </div>

            {/* Idioma */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-3">
                Idioma
              </label>
              <select
                value={preferences.language}
                onChange={(e) =>
                  updatePreference(
                    'language',
                    e.target.value as 'pt' | 'en' | 'es'
                  )
                }
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50">
                <option value="pt">🇧🇷 Português</option>
                <option value="en" disabled>
                  🇺🇸 English (Em breve)
                </option>
                <option value="es" disabled>
                  🇪🇸 Español (Em breve)
                </option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Notificações */}
        <Card>
          <CardHeader
            title="🔔 Notificações"
            subtitle="Gerencie suas notificações"
          />
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">📧 Email</span>
                  <p className="text-xs text-cyber-muted">
                    Receber notificações por email
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.email}
                  onChange={(e) =>
                    updateNotification('email', e.target.checked)
                  }
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">📱 Push</span>
                  <p className="text-xs text-cyber-muted">
                    Notificações push no navegador
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.push}
                  onChange={(e) => updateNotification('push', e.target.checked)}
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">💬 Comentários</span>
                  <p className="text-xs text-cyber-muted">
                    Novos comentários nos seus posts
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.comments}
                  onChange={(e) =>
                    updateNotification('comments', e.target.checked)
                  }
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">@️ Menções</span>
                  <p className="text-xs text-cyber-muted">
                    Quando alguém te mencionar
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.mentions}
                  onChange={(e) =>
                    updateNotification('mentions', e.target.checked)
                  }
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Segurança */}
        <Card>
          <CardHeader
            title="🔐 Segurança"
            subtitle="Configurações de segurança"
          />
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="p-4 bg-cyber-surface rounded-lg">
                <h4 className="font-medium text-cyber-text mb-2">
                  Sessão Atual
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Dispositivo:</span>
                    <span className="text-cyber-text">Desktop</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Navegador:</span>
                    <span className="text-cyber-text">Chrome</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Login:</span>
                    <span className="text-cyber-text">
                      {user.lastLogin
                        ? new Date(user.lastLogin).toLocaleString('pt-BR')
                        : 'Nunca'}
                    </span>
                  </div>
                </div>
              </div>

              <Button variant="outline" fullWidth disabled>
                🔑 Alterar Senha (Em breve)
              </Button>

              <Button variant="outline" fullWidth disabled>
                📱 Configurar 2FA (Em breve)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Dados */}
        <Card>
          <CardHeader title="💾 Dados" subtitle="Gerenciar dados locais" />
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="p-4 bg-cyber-surface rounded-lg">
                <h4 className="font-medium text-cyber-text mb-2">
                  Armazenamento Local
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Sessão:</span>
                    <span className="text-neon-cyan">Ativa</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Preferências:</span>
                    <span className="text-neon-cyan">Salvas</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Cache:</span>
                    <span className="text-cyber-text">~2MB</span>
                  </div>
                </div>
              </div>

              <Button variant="outline" fullWidth disabled>
                📥 Exportar Dados (Em breve)
              </Button>

              <Button
                variant="outline"
                fullWidth
                onClick={handleClearData}
                className="text-red-400 border-red-400 hover:bg-red-400/10">
                🗑️ Limpar Dados Locais
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleSavePreferences}
          loading={loading}
          size="lg"
          leftIcon="💾">
          {loading ? 'Salvando...' : 'Salvar Configurações'}
        </Button>
      </div>

      {/* System Info */}
      <Card className="border-dashed border-cyber-border">
        <CardContent className="p-6 text-center">
          <div className="text-4xl mb-4">⚙️</div>
          <h3 className="text-xl font-bold text-cyber-text mb-2">
            Configurações Básicas
          </h3>
          <p className="text-cyber-muted mb-4">
            Esta é uma versão básica das configurações. Funcionalidades
            avançadas serão implementadas nas próximas fases.
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-xs">
            <span className="px-2 py-1 bg-neon-cyan/20 text-neon-cyan rounded">
              Preferências ✅
            </span>
            <span className="px-2 py-1 bg-cyber-surface text-cyber-muted rounded">
              2FA 🔄
            </span>
            <span className="px-2 py-1 bg-cyber-surface text-cyber-muted rounded">
              Backup 📋
            </span>
            <span className="px-2 py-1 bg-cyber-surface text-cyber-muted rounded">
              API Keys 🚀
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;
