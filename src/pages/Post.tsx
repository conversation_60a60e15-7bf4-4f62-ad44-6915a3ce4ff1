/**
 * @fileoverview Página de Post individual do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { FaArrowRight, FaGithub, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { Link, useNavigate, useParams } from 'react-router';
import MarkdownRenderer from '../components/markdown/MarkdownRenderer';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Breadcrumbs, Button, Card, CardContent } from '../components/ui';
import AuthorAvatar from '../components/ui/AuthorAvatar';
import { usePosts } from '../hooks/usePosts';
import useSEO from '../hooks/useSEO';
import type { Post } from '../types';
import { generateBreadcrumbs } from '../utils/seoUtils';
// ============================================================================
// INTERFACES
// ============================================================================

interface PostPageProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de post individual com conteúdo completo
 */
export const PostPage: React.FC<PostPageProps> = ({ className }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [post, setPost] = useState<Post | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);

  // Hook para buscar posts do Supabase
  const { posts, getPostBySlug } = usePosts();

  // SEO dinâmico para o post
  const seoData = useSEO({
    post: post || undefined,
    pageType: 'post',
  });

  // Breadcrumbs para o post
  const breadcrumbs = post
    ? generateBreadcrumbs(`/post/${post.slug}`, post, post.category.name)
    : [];

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  // Carrega o post e posts relacionados
  useEffect(() => {
    const loadPost = async () => {
      if (!slug) return;

      try {
        setLoading(true);

        // Busca o post pelo slug usando o hook
        const { data: foundPost, error } = await getPostBySlug(slug);

        if (error) {
          console.error('Erro ao carregar post:', error);
          setPost(null);
          return;
        }

        // Tags já são carregadas corretamente pelo getPostBySlug() usando estrutura normalizada

        setPost(foundPost);

        // 🔍 DEBUG: Log das tags do post
        console.log('🏷️ [Post] Tags do post:', {
          postTitle: foundPost?.title,
          tags: foundPost?.tags,
          tagsType: typeof foundPost?.tags,
          tagsLength: foundPost?.tags?.length,
          firstTag: foundPost?.tags?.[0],
        });

        // Posts relacionados (mesma categoria, exceto o atual)
        if (foundPost && posts.length > 0) {
          const related = posts
            .filter(
              (p) =>
                p.category.id === foundPost.category.id && p.id !== foundPost.id
            )
            .slice(0, 3);
          setRelatedPosts(related);
        }
      } catch (error) {
        console.error('Erro ao carregar post:', error);
        setPost(null);
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [slug, getPostBySlug, posts]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando post...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-cyber-text mb-4">404</h1>
          <p className="text-cyber-muted mb-6">Post não encontrado</p>
          <Button>Voltar ao Início</Button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema type="post" post={post} breadcrumbs={breadcrumbs} />

      <div className={clsx('max-w-4xl mx-auto', className)}>
        {/* Breadcrumb */}
        <Breadcrumbs items={breadcrumbs} className="mb-8" />

        {/* Post Header */}
        <header className="mb-8">
          <div className="mb-4">
            <span
              className="inline-block px-3 py-1 rounded-full text-sm font-medium"
              style={{
                backgroundColor: `${post.category.color}20`,
                color: post.category.color,
                border: `1px solid ${post.category.color}30`,
              }}>
              {post.category.name}
            </span>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-6 leading-tight">
            {post.title}
          </h1>

          <p className="text-xl text-cyber-muted mb-8 leading-relaxed">
            {post.excerpt}
          </p>

          {/* Meta Info */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-6 bg-cyber-surface rounded-lg border border-cyber-border">
            <div className="flex items-center space-x-4">
              <AuthorAvatar
                avatar={post.author.avatar}
                name={post.author.name}
                size="lg"
              />
              <div>
                <p className="font-medium text-cyber-text">
                  {post.author.name}
                </p>
                <p className="text-sm text-cyber-muted">{post.author.bio}</p>
              </div>
            </div>

            <div className="flex items-center space-x-6 text-sm text-cyber-muted">
              <div className="flex items-center space-x-1">
                <span>📅</span>
                <span>{new Date(post.date).toLocaleDateString('pt-BR')}</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>⏱️</span>
                <span>{post.readTime} min</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>👁️</span>
                <span>{post.views}</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>❤️</span>
                <span>{post.likes}</span>
              </div>
            </div>
          </div>
        </header>

        {/* Featured Image */}
        <div className="mb-8">
          <img
            src={post.thumbnail}
            alt={post.title}
            className="w-full h-64 md:h-96 object-cover rounded-lg"
          />
        </div>

        {/* Post Content */}
        <Card className="mb-12 shadow-lg shadow-cyber-bg/20">
          <CardContent className="p-6 sm:p-8">
            <article className="prose prose-lg prose-invert max-w-none">
              <MarkdownRenderer
                content={post.content}
                className="text-cyber-text leading-relaxed"
                isLegacyContent={true}
              />
            </article>
          </CardContent>
        </Card>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <Card className="mb-8 shadow-md shadow-cyber-bg/10">
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-cyber-text mb-4">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => {
                  // Verifica se tag é objeto ou string
                  const tagData =
                    typeof tag === 'string'
                      ? {
                          id: tag,
                          name: tag,
                          slug: (tag as string)
                            .toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
                            .replace(/\s+/g, '-') // Substitui espaços por hífens
                            .replace(/-+/g, '-') // Remove hífens duplicados
                            .trim(),
                        }
                      : tag;

                  return (
                    <Link
                      key={tagData.id || index}
                      to={`/tags/${tagData.slug}`}
                      className="px-3 py-1 rounded-full text-sm border border-cyber-border text-cyber-muted hover:text-neon-cyan hover:border-neon-cyan transition-colors">
                      #{tagData.name}
                    </Link>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Social Share */}
        <Card className="mb-12 shadow-md shadow-cyber-bg/10">
          <CardContent className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-cyber-text mb-4">
              Compartilhar
            </h3>
            <div className="flex space-x-4">
              <Button size="sm" variant="secondary">
                <FaTwitter />
              </Button>
              <Button size="sm" variant="secondary">
                <FaLinkedin />
              </Button>
              <Button size="sm" variant="secondary">
                <FaGithub />
              </Button>
              <Button size="sm" variant="secondary">
                🔗 Copiar Link
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Comments Section (Placeholder) */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-cyber-text mb-6">
            💬 Comentários
          </h2>

          <Card>
            <CardContent>
              <div className="text-center py-12">
                <p className="text-cyber-muted mb-4">
                  Sistema de comentários será implementado em breve
                </p>
                <Button variant="secondary" size="sm">
                  Notificar quando disponível
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-cyber-text mb-6">
              📚 Posts Relacionados
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <div
                  key={relatedPost.id}
                  className="relative h-64 sm:h-72 lg:h-80 rounded-lg overflow-hidden cursor-pointer group"
                  onClick={() => handlePostClick(relatedPost)}>
                  {/* Background Image */}
                  <div className="absolute inset-0">
                    <img
                      src={relatedPost.thumbnail}
                      alt={relatedPost.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Blur overlay para melhor legibilidade */}
                    <div className="absolute inset-0 bg-cyber-bg/20 backdrop-blur-[0.5px]" />
                  </div>

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-cyber-bg/90 via-cyber-bg/20 to-transparent" />

                  {/* Category Badge */}
                  <div className="absolute top-2 left-2 sm:top-4 sm:left-4 z-10">
                    <span
                      className="px-2 py-1 rounded-full text-xs font-bold backdrop-blur-sm border"
                      style={{
                        backgroundColor: `${relatedPost.category.color}20`,
                        color: relatedPost.category.color,
                        borderColor: `${relatedPost.category.color}40`,
                      }}>
                      {relatedPost.category.name}
                    </span>
                  </div>

                  {/* Content Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 z-10">
                    <h3 className="text-lg font-bold text-white mb-2 group-hover:text-neon-cyan transition-colors line-clamp-2">
                      {relatedPost.title}
                    </h3>

                    <p className="text-gray-200 text-sm mb-3 line-clamp-2 opacity-90">
                      {relatedPost.excerpt}
                    </p>

                    {/* Meta Info */}
                    <div className="flex items-center justify-between text-xs text-gray-300 mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center space-x-1">
                          <span>📅</span>
                          <span>
                            {new Date(relatedPost.date).toLocaleDateString(
                              'pt-BR'
                            )}
                          </span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <span>⏱️</span>
                          <span>{relatedPost.readTime} min</span>
                        </span>
                      </div>
                      <span className="flex items-center space-x-1">
                        <span>👁️</span>
                        <span>{relatedPost.views}</span>
                      </span>
                    </div>

                    {/* CTA Button */}
                    <div className="pt-1">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePostClick(relatedPost);
                        }}
                        className="group/btn text-xs">
                        <span>Leia mais</span>
                        <FaArrowRight className="ml-1 group-hover/btn:translate-x-1 transition-transform duration-300 text-xs" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Navigation */}
        <nav className="flex justify-between items-center">
          <Button variant="secondary" leftIcon="←">
            Post Anterior
          </Button>
          <Button variant="secondary">Voltar ao Blog</Button>
          <Button variant="secondary" rightIcon="→">
            Próximo Post
          </Button>
        </nav>
      </div>
    </>
  );
};

export default PostPage;
