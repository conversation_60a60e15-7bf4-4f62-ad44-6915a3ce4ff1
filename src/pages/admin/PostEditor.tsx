/**
 * @fileoverview Editor de posts do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { FaEdit, FaEye, FaFileAlt, FaGlobe, FaSave } from 'react-icons/fa';
import { useNavigate, useParams } from 'react-router';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Input,
} from '../../components/ui';
import { useToast } from '../../contexts/ToastContext';
import { useTags, type Tag } from '../../hooks/useTags';
import { supabase } from '../../lib/supabaseClient';
import { supabasePostsService } from '../../services/supabasePostsService';
import { supabaseCategoryService } from '../../services/supabaseService';
import type { Category } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface PostEditorProps {
  className?: string;
}

interface PostFormData {
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  thumbnail: string;
  categoryId: string;
  tags: string[];
  featured: boolean;
  published: boolean;
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Editor WYSIWYG para criação e edição de posts
 */
export const PostEditor: React.FC<PostEditorProps> = ({ className }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const isEditing = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [showConfirmReset, setShowConfirmReset] = useState(false);
  const [formData, setFormData] = useState<PostFormData>({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    thumbnail: '',
    categoryId: '',
    tags: [],
    featured: false,
    published: false,
    metaTitle: '',
    metaDescription: '',
    keywords: [],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Hook para carregar tags do Supabase
  const {
    tags: availableTags,
    loading: loadingTags,
    error: tagsError,
  } = useTags();

  // Carrega categorias do Supabase
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true);
        const response = await supabaseCategoryService.getCategories();

        if (response.error) {
          console.error('Erro ao carregar categorias:', response.error);
          return;
        }

        // Converter categorias do Supabase para o formato esperado
        const convertedCategories: Category[] = (response.data || []).map(
          (cat) => ({
            id: cat.id,
            name: cat.name,
            slug: cat.slug,
            description: cat.description || '',
            color: cat.color || '#00ffff',
            postsCount: 0, // TODO: Implementar contagem real
          })
        );

        setCategories(convertedCategories);
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Carrega post para edição
  useEffect(() => {
    if (isEditing && id) {
      const loadPost = async () => {
        setLoading(true);

        try {
          // Carrega post real do Supabase
          const response = await supabasePostsService.getPostById(id);

          if (response.error) {
            console.error('Erro ao carregar post:', response.error);
            // Se não encontrar o post, redireciona para lista
            navigate('/admin/posts');
            return;
          }

          if (response.data) {
            // Converte dados do Supabase para o formato do formulário
            const post = response.data;

            // Carregar tags da estrutura normalizada
            let parsedTags: string[] = [];
            const { data: postTagsData } = await supabase
              .from('post_tags')
              .select(
                `
                tags (
                  id,
                  name
                )
              `
              )
              .eq('post_id', post.id);

            parsedTags = postTagsData?.map((item: any) => item.tags.name) || [];

            const postData: PostFormData = {
              title: post.title || '',
              slug: post.slug || '',
              excerpt: post.excerpt || '',
              content: post.content || '',
              thumbnail: post.featured_image || '',
              categoryId: post.category_id || '', // Carrega categoria do post
              tags: parsedTags,
              featured: post.featured || false, // Carrega campo featured do banco
              published: post.status === 'published',
              metaTitle: post.title || '',
              metaDescription: post.excerpt || '',
              keywords: [], // TODO: Implementar keywords
            };

            setFormData(postData);
          }
        } catch (error) {
          console.error('Erro inesperado ao carregar post:', error);
          navigate('/admin/posts');
        } finally {
          setLoading(false);
        }
      };

      loadPost();
    } else {
      // Para novos posts, carrega rascunho salvo se existir
      const savedDraft = localStorage.getItem('blueprint_post_draft');
      if (savedDraft) {
        try {
          const draftData = JSON.parse(savedDraft);
          setFormData(draftData);
        } catch (error) {
          console.error('Erro ao carregar rascunho:', error);
        }
      }
    }
  }, [isEditing, id, navigate]);

  /**
   * Gera slug automaticamente baseado no título
   */
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens duplicados
      .replace(/^-+|-+$/g, '') // Remove hífens do início e fim
      .trim();
  };

  /**
   * Atualiza campo do formulário
   */
  const handleChange = (field: keyof PostFormData, value: any) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Auto-gera slug quando título muda
      if (field === 'title' && value) {
        newData.slug = generateSlug(value);
      }

      return newData;
    });

    // Remove erro do campo
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  /**
   * Valida formulário
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Título é obrigatório';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug é obrigatório';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug =
        'Slug deve conter apenas letras minúsculas, números e hífens';
    }

    if (!formData.excerpt.trim()) {
      newErrors.excerpt = 'Resumo é obrigatório';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Conteúdo é obrigatório';
    }

    // Categoria é opcional por enquanto
    // if (!formData.categoryId) {
    //   newErrors.categoryId = 'Categoria é obrigatória';
    // }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Salva post no Supabase
   */
  const handleSave = async (publish = false) => {
    if (!validateForm()) return;

    setSaving(true);

    const action = isEditing ? 'Atualizando' : 'Criando';
    const status = publish ? 'e publicando' : '';
    const loadingToastId = toast.loading(`${action} post${status}...`);

    try {
      // Se marcar como destaque, primeiro remove destaque de outros posts
      if (formData.featured) {
        await supabasePostsService.clearAllFeatured();
      }

      // Converte nomes das tags para UUIDs
      let tagIds: string[] = [];
      if (formData.tags.length > 0) {
        const { data: tagsData } = await supabase
          .from('tags')
          .select('id')
          .in('name', formData.tags);

        tagIds = tagsData?.map((tag: any) => tag.id) || [];
      }

      // Prepara dados para o Supabase (SEM campo tags - usaremos estrutura normalizada)
      const postData = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        excerpt: formData.excerpt,
        featured_image: formData.thumbnail || undefined,
        featured: formData.featured, // Adiciona campo featured
        status: publish ? ('published' as const) : ('draft' as const),
        language: 'pt' as const, // Padrão português
        reading_time: Math.ceil(formData.content.split(' ').length / 200), // Estimativa: 200 palavras/min
        published_at: publish ? new Date().toISOString() : undefined,
        category_id: formData.categoryId || undefined, // FK para categoria principal
        author_id: '00000000-0000-0000-0000-000000000001', // UUID do usuário Blueprint
      };

      let response;

      if (isEditing && id) {
        // Atualiza post existente
        response = await supabasePostsService.updatePost({
          id,
          ...postData,
        });
      } else {
        // Cria novo post
        response = await supabasePostsService.createPost(postData);
      }

      if (response.error) {
        console.error('Erro ao salvar post:', response.error);
        toast.dismiss(loadingToastId);
        toast.error('Erro ao salvar post. Tente novamente.');
        return;
      }

      const savedPostId = response.data?.id || id;

      // Limpar relações existentes se for edição
      if (isEditing && savedPostId) {
        await supabase.from('post_tags').delete().eq('post_id', savedPostId);
        await supabase
          .from('post_categories')
          .delete()
          .eq('post_id', savedPostId);
      }

      // Salvar tags na estrutura normalizada
      if (tagIds.length > 0 && savedPostId) {
        const tagRelations = tagIds.map((tagId) => ({
          post_id: savedPostId,
          tag_id: tagId,
        }));

        const { error: tagsError } = await supabase
          .from('post_tags')
          .insert(tagRelations);

        if (tagsError) {
          console.error('Erro ao salvar tags:', tagsError);
          toast.error('Post salvo, mas erro ao associar tags');
        }
      }

      // Salvar categoria na estrutura normalizada (se especificada)
      if (formData.categoryId && savedPostId) {
        const { error: categoryError } = await supabase
          .from('post_categories')
          .insert({
            post_id: savedPostId,
            category_id: formData.categoryId,
          });

        if (categoryError) {
          console.error('Erro ao salvar categoria:', categoryError);
          toast.error('Post salvo, mas erro ao associar categoria');
        }
      }

      // Remove rascunho salvo localmente
      localStorage.removeItem('blueprint_post_draft');

      const action = isEditing ? 'atualizado' : 'criado';
      const status = publish ? 'publicado' : 'salvo como rascunho';
      const featuredText = formData.featured ? ' e marcado como destaque' : '';

      toast.dismiss(loadingToastId);
      toast.success(`✅ Post ${action}, ${status}${featuredText} com sucesso!`);

      // Para novos posts, limpa o formulário e fica na página para criar outro
      if (!isEditing) {
        resetForm();
        // Pequeno delay para mostrar o toast de sucesso primeiro
        setTimeout(() => {
          toast.success('💡 Formulário limpo! Você pode criar um novo post.');
        }, 1000);
      } else {
        // Para edições, redireciona para lista de posts
        navigate('/admin/posts');
      }
    } catch (error) {
      console.error('Erro inesperado ao salvar post:', error);
      toast.dismiss(loadingToastId);
      toast.error('Erro inesperado ao salvar post. Tente novamente.');
    } finally {
      setSaving(false);
    }
  };

  /**
   * Limpa o formulário após salvar
   */
  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      thumbnail: '',
      categoryId: '',
      tags: [],
      featured: false,
      published: false,
      metaTitle: '',
      metaDescription: '',
      keywords: [],
    });
    setErrors({});
    setPreviewMode(false);

    // ✅ IMPORTANTE: Remove rascunho do localStorage para evitar que volte
    localStorage.removeItem('blueprint_post_draft');
  };

  /**
   * Confirma se deseja limpar o formulário
   */
  const handleConfirmReset = () => {
    if (isEditing || formData.title || formData.content) {
      setShowConfirmReset(true);
    } else {
      resetForm();
      toast.success('✨ Formulário limpo para novo post!');
    }
  };

  /**
   * Executa o reset após confirmação
   */
  const executeReset = () => {
    resetForm();
    setShowConfirmReset(false);
    toast.success('✨ Formulário limpo! Criando novo post...');
    navigate('/admin/posts/new');
  };

  /**
   * Cancela o reset
   */
  const cancelReset = () => {
    setShowConfirmReset(false);
    toast.success('❌ Reset cancelado.');
  };

  /**
   * Salva rascunho automaticamente
   */
  const handleAutoSave = () => {
    if (formData.title || formData.content) {
      localStorage.setItem('blueprint_post_draft', JSON.stringify(formData));
    }
  };

  // Auto-save a cada 30 segundos
  useEffect(() => {
    const interval = setInterval(handleAutoSave, 30000);
    return () => clearInterval(interval);
  }, [formData]);

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="w-8 h-8 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text flex items-center space-x-3">
            {isEditing ? (
              <FaEdit className="text-neon-cyan" />
            ) : (
              <FaFileAlt className="text-neon-cyan" />
            )}
            <span>{isEditing ? 'Editar Post' : 'Novo Post'}</span>
          </h1>
          <p className="text-cyber-muted">
            {isEditing
              ? 'Edite as informações do post'
              : 'Crie um novo post para o blog'}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}>
            {previewMode ? (
              <>
                <FaEdit className="mr-2" />
                Editar
              </>
            ) : (
              <>
                <FaEye className="mr-2" />
                Preview
              </>
            )}
          </Button>

          <Button variant="outline" onClick={() => navigate('/admin/posts')}>
            Cancelar
          </Button>
        </div>
      </div>

      <div
        className={clsx(
          'grid gap-6',
          previewMode
            ? 'grid-cols-1 xl:grid-cols-2'
            : 'grid-cols-1 lg:grid-cols-3'
        )}>
        {/* Main Editor */}
        <div
          className={clsx(
            'space-y-6',
            previewMode ? 'xl:col-span-1' : 'lg:col-span-2'
          )}>
          {/* Basic Info */}
          <Card>
            <CardHeader
              title="📝 Informações Básicas"
              subtitle="Título, resumo e conteúdo principal"
            />
            <CardContent className="space-y-6">
              {/* Title */}
              <div>
                <Input
                  label="Título"
                  value={formData.title}
                  onChange={(e) => handleChange('title', e.target.value)}
                  error={errors.title}
                  placeholder="Digite o título do post..."
                  fullWidth
                  required
                />
              </div>

              {/* Slug */}
              <div>
                <Input
                  label="Slug (URL)"
                  value={formData.slug}
                  onChange={(e) => handleChange('slug', e.target.value)}
                  error={errors.slug}
                  placeholder="slug-do-post"
                  helpText="URL amigável para o post"
                  fullWidth
                  required
                />
              </div>

              {/* Excerpt */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Resumo *
                </label>
                <textarea
                  value={formData.excerpt}
                  onChange={(e) => handleChange('excerpt', e.target.value)}
                  placeholder="Escreva um resumo atrativo do post..."
                  rows={3}
                  className={clsx(
                    'w-full px-3 py-2 rounded-lg transition-all duration-200',
                    'bg-cyber-surface border text-cyber-text placeholder:text-cyber-muted',
                    'focus:outline-none focus:ring-2 focus:ring-neon-cyan/50',
                    errors.excerpt
                      ? 'border-red-500 focus:border-red-500'
                      : 'border-cyber-border focus:border-neon-cyan'
                  )}
                />
                {errors.excerpt && (
                  <p className="mt-1 text-sm text-red-400">{errors.excerpt}</p>
                )}
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Conteúdo *
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => handleChange('content', e.target.value)}
                  placeholder="Escreva o conteúdo do post em Markdown..."
                  rows={15}
                  className={clsx(
                    'w-full px-3 py-2 rounded-lg transition-all duration-200 font-mono text-sm',
                    'bg-cyber-surface border text-cyber-text placeholder:text-cyber-muted',
                    'focus:outline-none focus:ring-2 focus:ring-neon-cyan/50',
                    errors.content
                      ? 'border-red-500 focus:border-red-500'
                      : 'border-cyber-border focus:border-neon-cyan'
                  )}
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-red-400">{errors.content}</p>
                )}
                <p className="mt-1 text-xs text-cyber-muted">
                  Suporte a Markdown: **negrito**, *itálico*, [links](url),
                  `código`, etc.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Preview Column (only in preview mode) */}
        {previewMode && (
          <div className="space-y-6">
            <Card>
              <CardHeader
                title="👁️ Preview"
                subtitle="Visualização em tempo real"
              />
              <CardContent>
                <div className="prose prose-invert max-w-none">
                  <div
                    className="markdown-preview leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html:
                        formData.content ||
                        '<div class="text-center py-12 text-cyber-muted"><div class="text-4xl mb-4">📝</div><p>Comece a escrever para ver o preview...</p></div>',
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Sidebar */}
        <div className={clsx('space-y-6', previewMode ? 'xl:col-span-2' : '')}>
          {/* Actions */}
          <Card>
            <CardHeader title="🚀 Ações" />
            <CardContent className="space-y-4">
              {isEditing ? (
                // Botões para edição
                <>
                  <Button
                    fullWidth
                    onClick={() => handleSave(formData.published)}
                    loading={saving}>
                    <FaSave className="mr-2" />
                    {saving ? 'Atualizando...' : 'Atualizar'}
                  </Button>

                  {!formData.published && (
                    <Button
                      fullWidth
                      onClick={() => handleSave(true)}
                      loading={saving}>
                      <FaGlobe className="mr-2" />
                      {saving ? 'Publicando...' : 'Publicar Agora'}
                    </Button>
                  )}

                  {formData.published && (
                    <Button
                      fullWidth
                      variant="outline"
                      onClick={() => handleSave(false)}
                      loading={saving}>
                      <FaSave className="mr-2" />
                      {saving ? 'Despublicando...' : 'Voltar para Rascunho'}
                    </Button>
                  )}
                </>
              ) : (
                // Botões para criação
                <>
                  <Button
                    fullWidth
                    onClick={() => handleSave(false)}
                    loading={saving}>
                    <FaSave className="mr-2" />
                    {saving ? 'Salvando...' : 'Salvar Rascunho'}
                  </Button>

                  <Button
                    fullWidth
                    onClick={() => handleSave(true)}
                    loading={saving}>
                    <FaGlobe className="mr-2" />
                    {saving ? 'Publicando...' : 'Publicar'}
                  </Button>
                </>
              )}

              {/* Botão para criar novo post (sempre visível) */}
              <div className="pt-4 border-t border-cyber-border">
                <Button
                  fullWidth
                  variant="outline"
                  onClick={handleConfirmReset}>
                  ✨ Criar Novo Post
                </Button>
              </div>

              {/* Modal de confirmação */}
              {showConfirmReset && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                  <div className="bg-cyber-surface border border-cyber-border rounded-lg p-6 max-w-md mx-4">
                    <h3 className="text-lg font-bold text-cyber-text mb-4">
                      ⚠️ Confirmar Reset
                    </h3>
                    <p className="text-cyber-muted mb-6">
                      Deseja limpar o formulário e criar um novo post?
                      <br />
                      <strong className="text-yellow-400">
                        Dados não salvos serão perdidos.
                      </strong>
                    </p>
                    <div className="flex space-x-3">
                      <Button
                        variant="outline"
                        onClick={cancelReset}
                        className="flex-1">
                        ❌ Cancelar
                      </Button>
                      <Button onClick={executeReset} className="flex-1">
                        ✅ Confirmar
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Category & Tags */}
          <Card>
            <CardHeader title="🏷️ Categorização" />
            <CardContent className="space-y-4">
              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Categoria *
                </label>
                <select
                  value={formData.categoryId}
                  onChange={(e) => handleChange('categoryId', e.target.value)}
                  disabled={loadingCategories}
                  className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50 disabled:opacity-50">
                  <option value="">
                    {loadingCategories
                      ? 'Carregando categorias...'
                      : 'Selecione uma categoria'}
                  </option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.categoryId && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.categoryId}
                  </p>
                )}
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Tags
                </label>
                {loadingTags ? (
                  <div className="flex items-center space-x-2 py-2">
                    <div className="w-4 h-4 border-2 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm text-cyber-muted">
                      Carregando tags...
                    </span>
                  </div>
                ) : tagsError ? (
                  <div className="text-red-400 text-sm py-2">
                    ⚠️ Erro ao carregar tags: {tagsError}
                  </div>
                ) : availableTags.length === 0 ? (
                  <div className="text-cyber-muted text-sm py-2">
                    Nenhuma tag disponível
                  </div>
                ) : (
                  <div className="space-y-2">
                    {availableTags.map((tag: Tag) => (
                      <label
                        key={tag.id}
                        className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.tags.includes(tag.id)}
                          onChange={(e) => {
                            const newTags = e.target.checked
                              ? [...formData.tags, tag.id]
                              : formData.tags.filter((t) => t !== tag.id);
                            handleChange('tags', newTags);
                          }}
                          className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                        />
                        <span className="text-sm text-cyber-text flex items-center space-x-2">
                          <span>{tag.name}</span>
                          {tag.color && (
                            <div
                              className="w-3 h-3 rounded-full border border-cyber-border"
                              style={{ backgroundColor: tag.color }}
                            />
                          )}
                        </span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader title="🖼️ Imagem Destacada" />
            <CardContent>
              <div className="space-y-4">
                {/* Image Upload Component */}
                <div className="border border-cyber-border rounded-lg p-4 bg-cyber-surface">
                  <div className="space-y-4">
                    {/* Current Image Preview */}
                    {formData.thumbnail && (
                      <div className="relative">
                        <img
                          src={formData.thumbnail}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-lg border border-cyber-border"
                        />
                        <button
                          onClick={() => handleChange('thumbnail', '')}
                          className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors text-xs">
                          ✕
                        </button>
                      </div>
                    )}

                    {/* Upload Area */}
                    {!formData.thumbnail && (
                      <div className="border-2 border-dashed border-cyber-border rounded-lg p-6 text-center hover:border-neon-cyan/50 transition-colors">
                        <div className="text-4xl mb-2 text-cyber-muted">
                          <FaFileAlt />
                        </div>
                        <p className="text-cyber-text text-sm mb-3">
                          Clique para selecionar uma imagem
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Simula seleção de imagem
                            const randomImages = [
                              'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
                              'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop',
                              'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
                              'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=800&h=400&fit=crop',
                            ];
                            const randomImage =
                              randomImages[
                                Math.floor(Math.random() * randomImages.length)
                              ];
                            handleChange('thumbnail', randomImage);
                          }}>
                          Selecionar Imagem
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Manual URL Input */}
                <div>
                  <Input
                    label="Ou insira URL manualmente"
                    value={formData.thumbnail}
                    onChange={(e) => handleChange('thumbnail', e.target.value)}
                    placeholder="https://exemplo.com/imagem.jpg"
                    fullWidth
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Options */}
          <Card>
            <CardHeader title="⚙️ Opções" />
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleChange('featured', e.target.checked)}
                    className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                  />
                  <div className="flex-1">
                    <span className="text-sm font-medium text-cyber-text">
                      ⭐ Post em destaque
                    </span>
                    <p className="text-xs text-cyber-text-secondary mt-1">
                      {formData.featured
                        ? 'Este post será destacado na página inicial'
                        : 'Marque para destacar este post na página inicial'}
                    </p>
                  </div>
                </label>

                {formData.featured && (
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                    <p className="text-xs text-yellow-400">
                      ⚠️ Apenas um post pode estar em destaque por vez. Ao
                      marcar este post, o destaque atual será removido
                      automaticamente.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PostEditor;
