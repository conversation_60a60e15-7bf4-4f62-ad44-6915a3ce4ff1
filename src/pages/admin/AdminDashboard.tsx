/**
 * @fileoverview Dashboard administrativo unificado
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import {
  BarChart3,
  Eye,
  FileText,
  Folder,
  Settings,
  Tag,
  TrendingUp,
  Upload,
  Users,
} from 'lucide-react';
import { Link } from 'react-router';
import {
  ErrorNotifications,
  LogsWidget,
  SystemStatusIndicator,
} from '../../components/debug';
import { Button, Card, CardContent, CardHeader } from '../../components/ui';
import { useAuth } from '../../contexts/AuthContext';
import { useBlogStats } from '../../hooks/usePostAnalytics';

// ============================================================================
// INTERFACES
// ============================================================================

interface AdminDashboardProps {
  className?: string;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Dashboard administrativo unificado com métricas e ações rápidas
 */
export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  className,
}) => {
  const { user } = useAuth();
  const { stats: blogStats, loading } = useBlogStats();

  if (!user) return null;

  // Ações rápidas
  const quickActions: QuickAction[] = [
    {
      id: 'new-post',
      title: 'Novo Post',
      description: 'Criar um novo artigo',
      icon: <FileText className="h-5 w-5" />,
      href: '/admin/posts/new',
      color: 'neon-cyan',
    },
    {
      id: 'upload-media',
      title: 'Upload Mídia',
      description: 'Enviar imagens e arquivos',
      icon: <Upload className="h-5 w-5" />,
      href: '/admin/media',
      color: 'neon-magenta',
    },
    {
      id: 'manage-categories',
      title: 'Categorias',
      description: 'Gerenciar categorias',
      icon: <Folder className="h-5 w-5" />,
      href: '/admin/categories',
      color: 'neon-yellow',
    },
    {
      id: 'manage-tags',
      title: 'Tags',
      description: 'Gerenciar tags',
      icon: <Tag className="h-5 w-5" />,
      href: '/admin/tags',
      color: 'neon-green',
    },
  ];

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            Dashboard Administrativo
          </h1>
          <p className="text-cyber-muted mt-2">
            Bem-vindo de volta, {user.name || 'Admin'}! Gerencie seu blog aqui.
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Link to="/admin/settings">
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Settings className="h-4 w-4" />}>
              Configurações
            </Button>
          </Link>

          <Link to="/admin/analytics">
            <Button size="sm" leftIcon={<BarChart3 className="h-4 w-4" />}>
              Analytics
            </Button>
          </Link>
        </div>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-neon-cyan/20 bg-neon-cyan/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Total de Posts</p>
                <p className="text-2xl font-bold text-neon-cyan">
                  {loading ? '...' : blogStats?.totalPosts || 0}
                </p>
              </div>
              <FileText className="h-8 w-8 text-neon-cyan" />
            </div>
            <div className="mt-2 text-xs text-neon-cyan/70">
              Conteúdo publicado
            </div>
          </CardContent>
        </Card>

        <Card className="border-neon-magenta/20 bg-neon-magenta/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Visualizações</p>
                <p className="text-2xl font-bold text-neon-magenta">
                  {loading
                    ? '...'
                    : (blogStats?.totalViews || 0).toLocaleString('pt-BR')}
                </p>
              </div>
              <Eye className="h-8 w-8 text-neon-magenta" />
            </div>
            <div className="mt-2 text-xs text-neon-magenta/70">
              Total de acessos
            </div>
          </CardContent>
        </Card>

        <Card className="border-neon-yellow/20 bg-neon-yellow/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Média de Views</p>
                <p className="text-2xl font-bold text-neon-yellow">
                  {loading
                    ? '...'
                    : Math.round(blogStats?.avgViewsPerPost || 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-neon-yellow" />
            </div>
            <div className="mt-2 text-xs text-neon-yellow/70">Por post</div>
          </CardContent>
        </Card>

        <Card className="border-neon-green/20 bg-neon-green/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-cyber-muted text-sm">Usuário Ativo</p>
                <p className="text-2xl font-bold text-neon-green">1</p>
              </div>
              <Users className="h-8 w-8 text-neon-green" />
            </div>
            <div className="mt-2 text-xs text-neon-green/70">Administrador</div>
          </CardContent>
        </Card>
      </div>

      {/* Ações Rápidas */}
      <Card>
        <CardHeader
          title="⚡ Ações Rápidas"
          subtitle="Acesso direto às funcionalidades principais"
        />
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.id} to={action.href} className="group block">
                <div
                  className={clsx(
                    'p-4 rounded-lg border transition-all duration-200 hover:scale-105',
                    `border-${action.color}/20 bg-${action.color}/5 hover:border-${action.color}/40`
                  )}>
                  <div
                    className={clsx(
                      'flex items-center gap-3 mb-2',
                      `text-${action.color}`
                    )}>
                    {action.icon}
                    <span className="font-medium">{action.title}</span>
                  </div>
                  <p className="text-sm text-cyber-muted">
                    {action.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Notificações de Erro */}
      <ErrorNotifications />

      {/* Atividade Recente e Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Atividade Recente */}
        <Card>
          <CardHeader
            title="📈 Atividade Recente"
            subtitle="Últimas ações no sistema"
          />
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="w-2 h-2 bg-neon-cyan rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Dashboard acessado</p>
                <p className="text-xs text-cyber-muted">Agora mesmo</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="w-2 h-2 bg-neon-magenta rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Login realizado</p>
                <p className="text-xs text-cyber-muted">2 minutos atrás</p>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-cyber-surface rounded-lg">
              <div className="w-2 h-2 bg-neon-yellow rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-cyber-text">Sistema atualizado</p>
                <p className="text-xs text-cyber-muted">1 hora atrás</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status do Sistema */}
        <Card>
          <CardHeader
            title="🔧 Status do Sistema"
            subtitle="Informações técnicas"
            actions={<SystemStatusIndicator />}
          />
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-cyber-muted">Banco de Dados</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-neon-green rounded-full"></div>
                <span className="text-neon-green text-sm">Online</span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-cyber-muted">Storage</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-neon-green rounded-full"></div>
                <span className="text-neon-green text-sm">Conectado</span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-cyber-muted">Analytics</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-neon-green rounded-full"></div>
                <span className="text-neon-green text-sm">Ativo</span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-cyber-muted">Última Atualização</span>
              <span className="text-cyber-text text-sm">
                {new Date().toLocaleDateString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Logs do Sistema */}
        <LogsWidget />
      </div>
    </div>
  );
};

export default AdminDashboard;
