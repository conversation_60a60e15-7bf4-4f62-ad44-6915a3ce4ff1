/**
 * @fileoverview Gerenciador de categorias do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Input,
} from '../../components/ui';
import { useCategories } from '../../hooks/useCategories';
import type { Category } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface CategoriesManagerProps {
  className?: string;
}

interface CategoryFormData {
  name: string;
  slug: string;
  description: string;
  color: string;
  icon: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Gerenciador de categorias com CRUD completo
 */
export const CategoriesManager: React.FC<CategoriesManagerProps> = ({
  className,
}) => {
  // Hook para gerenciar categorias com Supabase
  const {
    categories,
    loading,
    error,
    createCategory,
    updateCategory,
    deleteCategory,
  } = useCategories();

  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    slug: '',
    description: '',
    color: '#00ffff',
    icon: '📁',
  });
  const [saving, setSaving] = useState(false);

  /**
   * Gera slug automaticamente baseado no nome
   */
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  /**
   * Atualiza campo do formulário
   */
  const handleChange = (field: keyof CategoryFormData, value: string) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Auto-gera slug quando nome muda
      if (field === 'name' && value) {
        newData.slug = generateSlug(value);
      }

      return newData;
    });
  };

  /**
   * Salva categoria
   */
  const handleSave = async () => {
    if (!formData.name.trim()) return;

    setSaving(true);

    try {
      const categoryData = {
        name: formData.name,
        slug: formData.slug,
        description: formData.description,
        color: formData.color,
      };

      let result;
      if (editingCategory) {
        // Editar categoria existente
        result = await updateCategory(editingCategory.id, categoryData);
      } else {
        // Adicionar nova categoria
        result = await createCategory(categoryData);
      }

      if (result.success) {
        // Reset form
        setFormData({
          name: '',
          slug: '',
          description: '',
          color: '#00ffff',
          icon: '📁',
        });
        setEditingCategory(null);
        setShowForm(false);
      } else {
        alert(result.error || 'Erro ao salvar categoria');
      }
    } catch (err) {
      console.error('Erro ao salvar categoria:', err);
      alert('Erro inesperado ao salvar categoria');
    } finally {
      setSaving(false);
    }
  };

  /**
   * Edita categoria
   */
  const handleEdit = (category: Category) => {
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color,
      icon: '📁',
    });
    setEditingCategory(category);
    setShowForm(true);
  };

  /**
   * Deleta categoria
   */
  const handleDelete = async (categoryId: string) => {
    const category = categories.find((c: Category) => c.id === categoryId);
    if (!category) return;

    if (
      !confirm(`Tem certeza que deseja deletar a categoria "${category.name}"?`)
    )
      return;

    const result = await deleteCategory(categoryId);
    if (!result.success) {
      alert(result.error || 'Erro ao deletar categoria');
    }
  };

  /**
   * Cancela edição
   */
  const handleCancel = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#00ffff',
      icon: '📁',
    });
    setEditingCategory(null);
    setShowForm(false);
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            🏷️ Gerenciar Categorias
          </h1>
          <p className="text-cyber-muted">
            {categories.length} categorias cadastradas
          </p>
          {error && <p className="text-red-400 text-sm mt-1">⚠️ {error}</p>}
        </div>

        <Button
          leftIcon="➕"
          onClick={() => setShowForm(true)}
          disabled={loading}>
          Nova Categoria
        </Button>
      </div>

      {/* Form */}
      {showForm && (
        <Card>
          <CardHeader
            title={
              editingCategory ? '✏️ Editar Categoria' : '➕ Nova Categoria'
            }
            subtitle={
              editingCategory
                ? 'Edite as informações da categoria'
                : 'Crie uma nova categoria'
            }
          />
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Name */}
              <div>
                <Input
                  label="Nome"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="Nome da categoria"
                  fullWidth
                  required
                />
              </div>

              {/* Slug */}
              <div>
                <Input
                  label="Slug (URL)"
                  value={formData.slug}
                  onChange={(e) => handleChange('slug', e.target.value)}
                  placeholder="slug-da-categoria"
                  fullWidth
                  required
                />
              </div>

              {/* Color */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Cor
                </label>
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => handleChange('color', e.target.value)}
                  className="w-full h-10 rounded-lg border border-cyber-border bg-cyber-surface"
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Descrição da categoria..."
                rows={3}
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text placeholder:text-cyber-muted focus:outline-none focus:ring-2 focus:ring-neon-cyan/50"
              />
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleSave}
                leftIcon="💾"
                disabled={saving || !formData.name.trim()}>
                {saving
                  ? 'Salvando...'
                  : editingCategory
                  ? 'Atualizar'
                  : 'Salvar'}
              </Button>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}>
                Cancelar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categories List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-8 h-8 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : categories.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">🏷️</div>
            <h3 className="text-xl font-bold text-cyber-text mb-2">
              Nenhuma categoria encontrada
            </h3>
            <p className="text-cyber-muted mb-6">
              Comece criando sua primeira categoria
            </p>
            <Button leftIcon="➕" onClick={() => setShowForm(true)}>
              Criar Primeira Categoria
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category: Category) => (
            <Card key={category.id} hoverable>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-12 h-12 rounded-lg flex items-center justify-center text-2xl font-bold"
                      style={{
                        backgroundColor: `${category.color}20`,
                        border: `1px solid ${category.color}`,
                        color: category.color,
                      }}>
                      {category.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-cyber-text">
                        {category.name}
                      </h3>
                      <p className="text-sm text-cyber-muted">
                        /{category.slug}
                      </p>
                    </div>
                  </div>

                  <span className="px-2 py-1 text-xs bg-cyber-surface border border-cyber-border rounded">
                    {category.postsCount} posts
                  </span>
                </div>

                <p className="text-cyber-muted text-sm mb-4 line-clamp-2">
                  {category.description}
                </p>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(category)}>
                    Editar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDelete(category.id)}
                    className={clsx(
                      category.postsCount > 0
                        ? 'opacity-50 cursor-not-allowed'
                        : 'text-red-400 border-red-400 hover:bg-red-400/10'
                    )}
                    disabled={category.postsCount > 0}>
                    Deletar
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoriesManager;
