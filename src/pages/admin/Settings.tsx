/**
 * @fileoverview Configurações do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Save, Shield, Bell, Palette, Database, Key } from 'lucide-react';
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent } from '../../components/ui';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';
import type { UserPreferences } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface SettingsProps {
  className?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Configurações administrativas unificadas
 */
export const Settings: React.FC<SettingsProps> = ({ className }) => {
  const { user, updateUser } = useAuth();
  const toast = useToast();
  
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferences>(
    user?.preferences || {
      theme: 'dark',
      language: 'pt',
      notifications: {
        email: true,
        push: true,
        comments: true,
        mentions: true,
      },
    }
  );

  /**
   * Salva as preferências
   */
  const handleSavePreferences = async () => {
    setLoading(true);
    
    try {
      // Simular salvamento
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      updateUser({ preferences });
      toast.success('Configurações salvas com sucesso!');
      
    } catch (error) {
      toast.error('Erro ao salvar configurações');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Atualiza preferência
   */
  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setPreferences((prev) => ({ ...prev, [key]: value }));
  };

  /**
   * Atualiza notificação específica
   */
  const updateNotification = (
    key: keyof UserPreferences['notifications'],
    value: boolean
  ) => {
    setPreferences((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value,
      },
    }));
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            Configurações
          </h1>
          <p className="text-cyber-muted mt-2">
            Gerencie preferências e configurações do sistema
          </p>
        </div>
        
        <Button
          onClick={handleSavePreferences}
          disabled={loading}
          leftIcon={<Save className="h-4 w-4" />}
        >
          {loading ? 'Salvando...' : 'Salvar Alterações'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Aparência */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <Palette className="h-5 w-5 text-neon-cyan" />
              Aparência
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-3">
                  Tema
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="theme"
                      value="dark"
                      checked={preferences.theme === 'dark'}
                      onChange={(e) =>
                        updatePreference('theme', e.target.value as 'dark' | 'light')
                      }
                      className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border focus:ring-neon-cyan"
                    />
                    <span className="text-cyber-text">🌙 Escuro (Cyberpunk)</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer opacity-50">
                    <input
                      type="radio"
                      name="theme"
                      value="light"
                      disabled
                      className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border focus:ring-neon-cyan"
                    />
                    <span className="text-cyber-text">☀️ Claro (Em breve)</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-cyber-text mb-3">
                  Idioma
                </label>
                <select
                  value={preferences.language}
                  onChange={(e) =>
                    updatePreference('language', e.target.value as 'pt' | 'en' | 'es')
                  }
                  className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50"
                >
                  <option value="pt">🇧🇷 Português</option>
                  <option value="en" disabled>🇺🇸 English (Em breve)</option>
                  <option value="es" disabled>🇪🇸 Español (Em breve)</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notificações */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <Bell className="h-5 w-5 text-neon-magenta" />
              Notificações
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">📧 Email</span>
                  <p className="text-xs text-cyber-muted">Receber notificações por email</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.email}
                  onChange={(e) => updateNotification('email', e.target.checked)}
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">📱 Push</span>
                  <p className="text-xs text-cyber-muted">Notificações push no navegador</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.push}
                  onChange={(e) => updateNotification('push', e.target.checked)}
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">💬 Comentários</span>
                  <p className="text-xs text-cyber-muted">Novos comentários nos posts</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.comments}
                  onChange={(e) => updateNotification('comments', e.target.checked)}
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-cyber-text">@️ Menções</span>
                  <p className="text-xs text-cyber-muted">Quando alguém te mencionar</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.notifications.mentions}
                  onChange={(e) => updateNotification('mentions', e.target.checked)}
                  className="w-4 h-4 text-neon-cyan bg-cyber-surface border-cyber-border rounded focus:ring-neon-cyan"
                />
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Segurança */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <Shield className="h-5 w-5 text-neon-yellow" />
              Segurança
            </h3>
            
            <div className="space-y-4">
              <div className="p-4 bg-cyber-surface rounded-lg">
                <h4 className="font-medium text-cyber-text mb-2">Sessão Atual</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Dispositivo:</span>
                    <span className="text-cyber-text">Desktop</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Login:</span>
                    <span className="text-cyber-text">
                      {user?.lastLogin
                        ? new Date(user.lastLogin).toLocaleString('pt-BR')
                        : 'Agora'}
                    </span>
                  </div>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<Key className="h-4 w-4" />}
                disabled
              >
                Alterar Senha (Em breve)
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Sistema */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <Database className="h-5 w-5 text-neon-green" />
              Sistema
            </h3>
            
            <div className="space-y-4">
              <div className="p-4 bg-cyber-surface rounded-lg">
                <h4 className="font-medium text-cyber-text mb-2">Armazenamento</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Sessão:</span>
                    <span className="text-neon-cyan">Ativa</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-cyber-muted">Cache:</span>
                    <span className="text-cyber-text">~2MB</span>
                  </div>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full justify-start text-red-400 border-red-400 hover:bg-red-400/10"
                onClick={() => {
                  if (confirm('Limpar dados locais?')) {
                    localStorage.clear();
                    toast.success('Dados locais limpos!');
                  }
                }}
              >
                🗑️ Limpar Dados Locais
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
