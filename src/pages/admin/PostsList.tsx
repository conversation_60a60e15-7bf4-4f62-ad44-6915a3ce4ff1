/**
 * @fileoverview Página de lista de posts do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router';
import { <PERSON><PERSON>, Card, CardContent, Input } from '../../components/ui';
import { useCategories } from '../../hooks/useCategories';
import { usePosts } from '../../hooks/usePosts';

// ============================================================================
// INTERFACES
// ============================================================================

interface PostsListProps {
  className?: string;
}

interface PostFilters {
  search: string;
  status: 'all' | 'published' | 'draft';
  category: string;
  author: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Lista de posts com funcionalidades CRUD
 */
export const PostsList: React.FC<PostsListProps> = ({ className }) => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<PostFilters>({
    search: '',
    status: 'all',
    category: '',
    author: '',
  });

  // Hooks para carregar dados reais do Supabase
  const {
    posts: allPosts,
    loading,
    error,
    deletePost,
    updatePost,
    refetch,
  } = usePosts({
    // Carregar todos os posts (publicados e rascunhos) para admin
    status: filters.status === 'all' ? undefined : filters.status,
    orderBy: 'updated_at',
    orderDirection: 'desc',
  });

  const { categories } = useCategories();

  // Filtra posts baseado nos filtros locais
  const filteredPosts = allPosts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(filters.search.toLowerCase()) ||
      post.author.name.toLowerCase().includes(filters.search.toLowerCase());

    const matchesStatus =
      filters.status === 'all' ||
      (filters.status === 'published' && post.published) ||
      (filters.status === 'draft' && !post.published);

    const matchesCategory =
      !filters.category || post.category.slug === filters.category;

    return matchesSearch && matchesStatus && matchesCategory;
  });

  /**
   * Deleta um post usando o hook real
   */
  const handleDeletePost = async (postId: string) => {
    if (!confirm('Tem certeza que deseja deletar este post?')) return;

    const result = await deletePost(postId);
    if (result.error) {
      alert('Erro ao deletar post: ' + result.error);
    } else {
      // Recarrega a lista após deletar
      refetch();
    }
  };

  /**
   * Alterna status de publicação usando o hook real
   */
  const handleTogglePublished = async (postId: string) => {
    const post = allPosts.find((p) => p.id === postId);
    if (!post) return;

    const newStatus = post.published ? 'draft' : 'published';
    const result = await updatePost(postId, { status: newStatus });

    if (result.error) {
      alert('Erro ao atualizar post: ' + result.error);
    } else {
      // Recarrega a lista após atualizar
      refetch();
    }
  };

  /**
   * Navega para edição do post
   */
  const handleEditPost = (postId: string) => {
    navigate(`/admin/posts/edit/${postId}`);
  };

  const getStatusBadge = (published: boolean) => {
    return published
      ? {
          label: 'Publicado',
          color: 'bg-green-500/20 text-green-400 border-green-500/30',
        }
      : {
          label: 'Rascunho',
          color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
        };
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            📝 Gerenciar Posts
          </h1>
          <p className="text-cyber-muted">
            {loading
              ? 'Carregando...'
              : `${filteredPosts.length} posts encontrados`}
            {error && (
              <span className="text-red-400 ml-2">⚠️ Erro: {error}</span>
            )}
          </p>
        </div>

        <Button leftIcon="✍️">
          <Link to="/admin/posts/new">Novo Post</Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Input
                placeholder="Buscar posts..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
              />
            </div>

            {/* Status */}
            <div>
              <select
                value={filters.status}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    status: e.target.value as any,
                  }))
                }
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50">
                <option value="all">Todos os status</option>
                <option value="published">Publicados</option>
                <option value="draft">Rascunhos</option>
              </select>
            </div>

            {/* Category */}
            <div>
              <select
                value={filters.category}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, category: e.target.value }))
                }
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50">
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.slug}>
                    {category.name} ({category.postsCount})
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            <div>
              <Button
                variant="outline"
                fullWidth
                onClick={() =>
                  setFilters({
                    search: '',
                    status: 'all',
                    category: '',
                    author: '',
                  })
                }>
                Limpar Filtros
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Posts List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-8 h-8 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredPosts.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-xl font-bold text-cyber-text mb-2">
              Nenhum post encontrado
            </h3>
            <p className="text-cyber-muted mb-6">
              {filters.search || filters.status !== 'all' || filters.category
                ? 'Tente ajustar os filtros de busca'
                : 'Comece criando seu primeiro post'}
            </p>
            <Button leftIcon="✍️">
              <Link to="/admin/posts/new">Criar Primeiro Post</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredPosts.map((post) => {
            const status = getStatusBadge(post.published);

            return (
              <Card key={post.id} hoverable>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    {/* Post Info - Clicável para edição */}
                    <div
                      className="flex space-x-4 flex-1 cursor-pointer hover:bg-cyber-surface/50 rounded-lg p-2 -m-2 transition-colors group"
                      onClick={() => handleEditPost(post.id)}
                      title="Clique para editar este post">
                      {/* Thumbnail */}
                      <div className="w-20 h-20 bg-gradient-cyber rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={post.thumbnail}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2 flex-1">
                            <h3 className="text-lg font-bold text-cyber-text truncate">
                              {post.title}
                            </h3>
                            <span className="opacity-0 group-hover:opacity-100 transition-opacity text-cyber-text-secondary text-sm">
                              ✏️
                            </span>
                          </div>
                          <span
                            className={clsx(
                              'px-2 py-1 text-xs rounded border',
                              status.color
                            )}>
                            {status.label}
                          </span>
                        </div>

                        <p className="text-cyber-muted text-sm mb-3 line-clamp-2">
                          {post.excerpt}
                        </p>

                        <div className="flex items-center space-x-4 text-xs text-cyber-muted">
                          <span>👤 {post.author.name}</span>
                          <span>🏷️ {post.category.name}</span>
                          <span>
                            📅 {new Date(post.date).toLocaleDateString('pt-BR')}
                          </span>
                          <span>👁️ {post.views}</span>
                          <span>❤️ {post.likes}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Button size="sm" variant="outline">
                        <Link to={`/admin/posts/edit/${post.id}`}>Editar</Link>
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTogglePublished(post.id)}>
                        {post.published ? 'Despublicar' : 'Publicar'}
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeletePost(post.id)}
                        className="text-red-400 border-red-400 hover:bg-red-400/10">
                        Deletar
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default PostsList;
