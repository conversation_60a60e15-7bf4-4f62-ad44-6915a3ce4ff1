/**
 * @fileoverview Gerenciador de mídia do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Download, Eye, Trash2, Upload, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { But<PERSON>, Card, CardContent, Input } from '../../components/ui';
import { fileToast, uploadToast, useToast } from '../../contexts/ToastContext';
import { useMediaUpload } from '../../hooks/useMediaUpload';
import type { MediaFile } from '../../services/supabaseStorageService';

// ============================================================================
// INTERFACES
// ============================================================================

interface MediaManagerProps {
  className?: string;
}

interface MediaFilters {
  search: string;
  folder: string;
  type: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Gerenciador de mídia com upload, visualização e organização
 */
export const MediaManager: React.FC<MediaManagerProps> = ({ className }) => {
  const [filters, setFilters] = useState<MediaFilters>({
    search: '',
    folder: '',
    type: '',
  });
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [previewFile, setPreviewFile] = useState<MediaFile | null>(null);

  const toast = useToast();

  const {
    files,
    loadingFiles,
    filesError,
    uploading,
    uploadProgress,
    uploadFile,
    listFiles,
    deleteFile,
  } = useMediaUpload();

  // Carregar arquivos na inicialização
  useEffect(() => {
    listFiles(filters.folder);
  }, [filters.folder]);

  // Filtrar arquivos baseado nos filtros
  const filteredFiles = files.filter((file) => {
    const matchesSearch = file.name
      .toLowerCase()
      .includes(filters.search.toLowerCase());
    const matchesType = !filters.type || file.type.startsWith(filters.type);
    return matchesSearch && matchesType;
  });

  /**
   * Manipula upload de arquivos
   */
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const file = selectedFiles[0];
    const folder = filters.folder || 'temp';

    // Iniciar toast de upload
    const toastId = uploadToast.start(file.name);

    try {
      const result = await uploadFile(file, {
        folder: folder as any,
        generateThumbnail: true,
      });

      if (result.success) {
        // Toast de sucesso
        uploadToast.success(file.name, toastId);

        // Recarregar lista após upload bem-sucedido
        await listFiles(filters.folder);

        // Reset do input para permitir upload do mesmo arquivo novamente
        event.target.value = '';
      } else {
        // Toast de erro
        uploadToast.error(
          file.name,
          result.error || 'Erro desconhecido',
          toastId
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      uploadToast.error(file.name, errorMessage, toastId);
    }
  };

  /**
   * Deleta arquivo selecionado
   */
  const handleDeleteFile = async (filePath: string) => {
    const fileName = filePath.split('/').pop() || 'arquivo';

    if (!confirm(`Tem certeza que deseja deletar "${fileName}"?`)) return;

    try {
      const result = await deleteFile(filePath);
      if (result.success) {
        setSelectedFiles((prev) => prev.filter((path) => path !== filePath));
        fileToast.deleted(fileName);
      } else {
        fileToast.deleteError(fileName, result.error || 'Erro desconhecido');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      fileToast.deleteError(fileName, errorMessage);
    }
  };

  /**
   * Deleta múltiplos arquivos
   */
  const handleDeleteSelected = async () => {
    if (selectedFiles.length === 0) return;
    if (!confirm(`Deletar ${selectedFiles.length} arquivo(s) selecionado(s)?`))
      return;

    for (const filePath of selectedFiles) {
      await deleteFile(filePath);
    }
    setSelectedFiles([]);
  };

  /**
   * Seleciona/deseleciona arquivo
   */
  const toggleFileSelection = (filePath: string) => {
    setSelectedFiles((prev) =>
      prev.includes(filePath)
        ? prev.filter((path) => path !== filePath)
        : [...prev, filePath]
    );
  };

  /**
   * Copia URL para clipboard
   */
  const copyUrlToClipboard = async (url: string, fileName: string) => {
    try {
      await navigator.clipboard.writeText(url);
      fileToast.copied(fileName);
    } catch (error) {
      console.error('Erro ao copiar URL:', error);
      toast.error('Erro ao copiar URL para a área de transferência');
    }
  };

  /**
   * Formata tamanho do arquivo
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            🗂️ Gerenciador de Mídia
          </h1>
          <p className="text-cyber-muted">
            {loadingFiles
              ? 'Carregando...'
              : `${filteredFiles.length} arquivos encontrados`}
            {filesError && (
              <span className="text-red-400 ml-2">⚠️ Erro: {filesError}</span>
            )}
          </p>
        </div>

        {/* Upload Button */}
        <div className="relative">
          <input
            type="file"
            id="file-upload"
            className="hidden"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
          />
          <Button
            leftIcon={<Upload className="h-4 w-4" />}
            onClick={() => document.getElementById('file-upload')?.click()}
            disabled={uploading}>
            {uploading ? `Enviando... ${uploadProgress}%` : 'Enviar Arquivo'}
          </Button>
        </div>
      </div>

      {/* Upload Progress */}
      {uploading && (
        <Card className="border-neon-cyan/20 bg-neon-cyan/5">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-6 h-6 border-2 border-neon-cyan/30 border-t-neon-cyan rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-6 h-6 border-2 border-transparent border-r-neon-magenta rounded-full animate-spin animate-reverse"></div>
                </div>
                <span className="text-neon-cyan font-medium">
                  Enviando arquivo...
                </span>
              </div>
              <span className="text-neon-cyan font-mono text-lg font-bold">
                {uploadProgress}%
              </span>
            </div>
            <div className="w-full bg-cyber-bg-secondary rounded-full h-3 overflow-hidden">
              <div
                className="bg-gradient-to-r from-neon-cyan via-neon-magenta to-neon-cyan h-3 rounded-full transition-all duration-500 ease-out relative"
                style={{ width: `${uploadProgress}%` }}>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
              </div>
            </div>
            <div className="mt-2 text-xs text-cyber-muted text-center">
              Aguarde enquanto processamos seu arquivo...
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Input
                placeholder="Buscar arquivos..."
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, search: e.target.value }))
                }
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
              />
            </div>

            {/* Folder */}
            <div>
              <select
                value={filters.folder}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, folder: e.target.value }))
                }
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50">
                <option value="">Todas as pastas</option>
                <option value="posts">Posts</option>
                <option value="thumbnails">Miniaturas</option>
                <option value="avatars">Avatars</option>
                <option value="categories">Categorias</option>
                <option value="temp">Temporários</option>
              </select>
            </div>

            {/* Type */}
            <div>
              <select
                value={filters.type}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, type: e.target.value }))
                }
                className="w-full px-3 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan/50">
                <option value="">Todos os tipos</option>
                <option value="image">Imagens</option>
                <option value="video">Vídeos</option>
                <option value="audio">Áudios</option>
              </select>
            </div>

            {/* Actions */}
            <div>
              {selectedFiles.length > 0 && (
                <Button
                  variant="outline"
                  fullWidth
                  onClick={handleDeleteSelected}
                  className="text-red-400 border-red-400 hover:bg-red-400/10">
                  Deletar ({selectedFiles.length})
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Files Grid */}
      {loadingFiles ? (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[...Array(12)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-square bg-cyber-surface rounded mb-2"></div>
                <div className="h-4 bg-cyber-surface rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredFiles.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">📁</div>
            <h3 className="text-xl font-bold text-cyber-text mb-2">
              Nenhum arquivo encontrado
            </h3>
            <p className="text-cyber-muted mb-6">
              {filters.search || filters.type || filters.folder
                ? 'Tente ajustar os filtros de busca'
                : 'Comece enviando seu primeiro arquivo'}
            </p>
            <Button
              leftIcon={<Upload className="h-4 w-4" />}
              onClick={() => document.getElementById('file-upload')?.click()}>
              Enviar Primeiro Arquivo
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {filteredFiles.map((file) => (
            <Card
              key={file.path}
              className={clsx(
                'group cursor-pointer transition-all hover:scale-105',
                selectedFiles.includes(file.path) && 'ring-2 ring-neon-cyan'
              )}
              onClick={() => toggleFileSelection(file.path)}>
              <CardContent className="p-4">
                {/* File Preview */}
                <div className="aspect-square bg-cyber-surface rounded mb-2 overflow-hidden relative">
                  {file.type.startsWith('image/') ? (
                    <img
                      src={file.url}
                      alt={file.name}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-4xl">
                      📄
                    </div>
                  )}

                  {/* Overlay Actions */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPreviewFile(file);
                      }}>
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        copyUrlToClipboard(file.url, file.name);
                      }}>
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFile(file.path);
                      }}
                      className="text-red-400 border-red-400 hover:bg-red-400/10">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* File Info */}
                <div className="space-y-1">
                  <p
                    className="text-sm font-medium text-cyber-text truncate"
                    title={file.name}>
                    {file.name}
                  </p>
                  <p className="text-xs text-cyber-muted">
                    {formatFileSize(file.size)}
                  </p>
                  <p className="text-xs text-cyber-muted">
                    {file.folder || 'root'}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Preview Modal */}
      {previewFile && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-cyber-bg border border-cyber-border rounded-lg max-w-4xl max-h-[90vh] overflow-auto">
            <div className="p-4 border-b border-cyber-border flex items-center justify-between">
              <h3 className="text-lg font-bold text-cyber-text">
                {previewFile.name}
              </h3>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setPreviewFile(null)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4">
              {previewFile.type.startsWith('image/') ? (
                <img
                  src={previewFile.url}
                  alt={previewFile.name}
                  className="max-w-full max-h-[60vh] object-contain mx-auto"
                />
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📄</div>
                  <p className="text-cyber-muted">
                    Preview não disponível para este tipo de arquivo
                  </p>
                </div>
              )}
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-cyber-muted">Tamanho:</span>
                  <span className="text-cyber-text ml-2">
                    {formatFileSize(previewFile.size)}
                  </span>
                </div>
                <div>
                  <span className="text-cyber-muted">Tipo:</span>
                  <span className="text-cyber-text ml-2">
                    {previewFile.type}
                  </span>
                </div>
                <div className="col-span-2">
                  <span className="text-cyber-muted">URL:</span>
                  <input
                    type="text"
                    value={previewFile.url}
                    readOnly
                    className="w-full mt-1 px-3 py-2 bg-cyber-surface border border-cyber-border rounded text-cyber-text text-xs"
                    onClick={(e) => e.currentTarget.select()}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaManager;
