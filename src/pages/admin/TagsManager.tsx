/**
 * @fileoverview Gerenciador de tags do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Input,
} from '../../components/ui';
import { useToast } from '../../contexts/ToastContext';
import SupabaseTagsService, {
  type TagWithPostsCount,
} from '../../services/supabaseTagsService';

// ============================================================================
// INTERFACES
// ============================================================================

interface TagsManagerProps {
  className?: string;
}

interface TagFormData {
  name: string;
  slug: string;
  color: string;
  description?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Gerenciador de tags com CRUD completo
 */
export const TagsManager: React.FC<TagsManagerProps> = ({ className }) => {
  const toast = useToast();

  const [tags, setTags] = useState<TagWithPostsCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingTag, setEditingTag] = useState<TagWithPostsCount | null>(null);
  const [formData, setFormData] = useState<TagFormData>({
    name: '',
    slug: '',
    color: '#00ffff',
    description: '',
  });
  const [searchTerm, setSearchTerm] = useState('');

  // Carrega tags do Supabase
  const loadTags = async () => {
    setLoading(true);

    try {
      const { data, error } = await SupabaseTagsService.getAllTags();

      if (error) {
        toast.error(`Erro ao carregar tags: ${error}`);
        return;
      }

      setTags(data || []);
    } catch (error) {
      toast.error('Erro inesperado ao carregar tags');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTags();
  }, []);

  // Filtra tags baseado na busca
  const filteredTags = tags.filter(
    (tag) =>
      tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tag.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  /**
   * Atualiza campo do formulário
   */
  const handleChange = (field: keyof TagFormData, value: string) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Auto-gera slug quando nome muda
      if (field === 'name' && value) {
        newData.slug = SupabaseTagsService.generateSlug(value);
      }

      return newData;
    });
  };

  /**
   * Salva tag
   */
  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast.error('Nome da tag é obrigatório');
      return;
    }

    setSaving(true);

    try {
      if (editingTag) {
        // Editar tag existente
        const { error } = await SupabaseTagsService.updateTag(editingTag.id, {
          name: formData.name,
          slug: formData.slug,
          color: formData.color,
          description: formData.description,
        });

        if (error) {
          toast.error(`Erro ao atualizar tag: ${error}`);
          return;
        }

        toast.success('Tag atualizada com sucesso!');
      } else {
        // Criar nova tag
        const { error } = await SupabaseTagsService.createTag({
          name: formData.name,
          slug: formData.slug,
          color: formData.color,
          description: formData.description,
        });

        if (error) {
          toast.error(`Erro ao criar tag: ${error}`);
          return;
        }

        toast.success('Tag criada com sucesso!');
      }

      // Recarregar lista
      await loadTags();

      // Reset form
      setFormData({
        name: '',
        slug: '',
        color: '#00ffff',
        description: '',
      });
      setEditingTag(null);
      setShowForm(false);
    } catch (error) {
      toast.error('Erro inesperado ao salvar tag');
    } finally {
      setSaving(false);
    }
  };

  /**
   * Edita tag
   */
  const handleEdit = (tag: TagWithPostsCount) => {
    setFormData({
      name: tag.name,
      slug: tag.slug,
      color: tag.color,
      description: tag.description || '',
    });
    setEditingTag(tag);
    setShowForm(true);
  };

  /**
   * Deleta tag
   */
  const handleDelete = async (tagId: string) => {
    const tag = tags.find((t) => t.id === tagId);
    if (!tag) return;

    if (tag.postsCount > 0) {
      toast.error(
        `Não é possível deletar a tag "${tag.name}" pois ela está sendo usada em ${tag.postsCount} posts.`
      );
      return;
    }

    if (!confirm(`Tem certeza que deseja deletar a tag "${tag.name}"?`)) return;

    try {
      const { error } = await SupabaseTagsService.deleteTag(tagId);

      if (error) {
        toast.error(`Erro ao deletar tag: ${error}`);
        return;
      }

      toast.success('Tag deletada com sucesso!');
      await loadTags();
    } catch (error) {
      toast.error('Erro inesperado ao deletar tag');
    }
  };

  /**
   * Cancela edição
   */
  const handleCancel = () => {
    setFormData({
      name: '',
      slug: '',
      color: '#00ffff',
      description: '',
    });
    setEditingTag(null);
    setShowForm(false);
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            🔖 Gerenciar Tags
          </h1>
          <p className="text-cyber-muted">
            {filteredTags.length} de {tags.length} tags
          </p>
        </div>

        <Button leftIcon="➕" onClick={() => setShowForm(true)}>
          Nova Tag
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <Input
            placeholder="Buscar tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            leftIcon={
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            }
            fullWidth
          />
        </CardContent>
      </Card>

      {/* Form */}
      {showForm && (
        <Card>
          <CardHeader
            title={editingTag ? '✏️ Editar Tag' : '➕ Nova Tag'}
            subtitle={
              editingTag ? 'Edite as informações da tag' : 'Crie uma nova tag'
            }
          />
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Name */}
              <div>
                <Input
                  label="Nome"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="Nome da tag"
                  fullWidth
                  required
                />
              </div>

              {/* Slug */}
              <div>
                <Input
                  label="Slug (URL)"
                  value={formData.slug}
                  onChange={(e) => handleChange('slug', e.target.value)}
                  placeholder="slug-da-tag"
                  fullWidth
                  required
                />
              </div>

              {/* Color */}
              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Cor
                </label>
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => handleChange('color', e.target.value)}
                  className="w-full h-10 rounded-lg border border-cyber-border bg-cyber-surface"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              <Button onClick={handleSave} leftIcon="💾" disabled={saving}>
                {saving ? 'Salvando...' : editingTag ? 'Atualizar' : 'Salvar'}
              </Button>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}>
                Cancelar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tags List */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-8 h-8 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredTags.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">🔖</div>
            <h3 className="text-xl font-bold text-cyber-text mb-2">
              {searchTerm ? 'Nenhuma tag encontrada' : 'Nenhuma tag cadastrada'}
            </h3>
            <p className="text-cyber-muted mb-6">
              {searchTerm
                ? 'Tente ajustar o termo de busca'
                : 'Comece criando sua primeira tag'}
            </p>
            {!searchTerm && (
              <Button leftIcon="➕" onClick={() => setShowForm(true)}>
                Criar Primeira Tag
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Tags Grid */}
          <div className="flex flex-wrap gap-3">
            {filteredTags.map((tag) => (
              <div
                key={tag.id}
                className="group relative flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105"
                style={{
                  backgroundColor: `${tag.color}20`,
                  borderColor: tag.color,
                  color: tag.color,
                }}>
                <span className="font-medium">{tag.name}</span>
                <span className="text-xs opacity-70">({tag.postsCount})</span>

                {/* Actions (appear on hover) */}
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
                  <button
                    onClick={() => handleEdit(tag)}
                    className="w-6 h-6 bg-cyber-surface border border-cyber-border rounded-full flex items-center justify-center text-xs hover:bg-neon-cyan hover:text-cyber-bg transition-colors">
                    ✏️
                  </button>
                  <button
                    onClick={() => handleDelete(tag.id)}
                    className={clsx(
                      'w-6 h-6 bg-cyber-surface border border-cyber-border rounded-full flex items-center justify-center text-xs transition-colors',
                      tag.postsCount > 0
                        ? 'opacity-50 cursor-not-allowed'
                        : 'hover:bg-red-500 hover:text-white'
                    )}
                    disabled={tag.postsCount > 0}>
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Stats */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-neon-cyan">
                    {tags.length}
                  </div>
                  <div className="text-sm text-cyber-muted">Total de Tags</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-neon-magenta">
                    {tags.reduce((sum, tag) => sum + tag.postsCount, 0)}
                  </div>
                  <div className="text-sm text-cyber-muted">
                    Posts Taggeados
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-neon-yellow">
                    {tags.filter((tag) => tag.postsCount > 0).length}
                  </div>
                  <div className="text-sm text-cyber-muted">Tags Ativas</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-neon-green">
                    {Math.round(
                      tags.reduce((sum, tag) => sum + tag.postsCount, 0) /
                        tags.length
                    ) || 0}
                  </div>
                  <div className="text-sm text-cyber-muted">Média por Tag</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default TagsManager;
