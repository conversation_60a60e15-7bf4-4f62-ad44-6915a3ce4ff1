/**
 * @fileoverview Gerenciador de usuário único do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Camera, Key, Mail, Save, User, Shield, Calendar } from 'lucide-react';
import { useState, useEffect } from 'react';
import { But<PERSON>, Card, CardContent, Input } from '../../components/ui';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';

// ============================================================================
// INTERFACES
// ============================================================================

interface UsersManagerProps {
  className?: string;
}

interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  bio?: string;
  role: string;
  createdAt: string;
  lastLogin: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Gerenciador de usuário único com perfil e configurações
 */
export const UsersManager: React.FC<UsersManagerProps> = ({ className }) => {
  const { user } = useAuth();
  const toast = useToast();
  
  const [profile, setProfile] = useState<UserProfile>({
    id: user?.id || '1',
    email: user?.email || '<EMAIL>',
    name: 'Administrador',
    avatar: '',
    bio: 'Criador e administrador do Blueprint Blog',
    role: 'Admin',
    createdAt: '2024-01-01',
    lastLogin: new Date().toISOString(),
  });

  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editForm, setEditForm] = useState(profile);

  // Carregar dados do usuário
  useEffect(() => {
    if (user) {
      setProfile(prev => ({
        ...prev,
        id: user.id,
        email: user.email,
        lastLogin: new Date().toISOString(),
      }));
      setEditForm(prev => ({
        ...prev,
        id: user.id,
        email: user.email,
      }));
    }
  }, [user]);

  /**
   * Salva alterações do perfil
   */
  const handleSaveProfile = async () => {
    setIsSaving(true);
    
    try {
      // Simular salvamento (em produção, conectar com Supabase)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile(editForm);
      setIsEditing(false);
      toast.success('Perfil atualizado com sucesso!');
      
    } catch (error) {
      toast.error('Erro ao salvar perfil');
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Cancela edição
   */
  const handleCancelEdit = () => {
    setEditForm(profile);
    setIsEditing(false);
  };

  /**
   * Atualiza campo do formulário
   */
  const updateField = (field: keyof UserProfile, value: string) => {
    setEditForm(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text">
            Gerenciamento de Usuário
          </h1>
          <p className="text-cyber-muted mt-2">
            Gerencie seu perfil e configurações de conta
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 px-3 py-2 bg-neon-cyan/10 border border-neon-cyan/20 rounded-lg">
            <Shield className="h-4 w-4 text-neon-cyan" />
            <span className="text-sm text-neon-cyan font-medium">Admin</span>
          </div>
        </div>
      </div>

      {/* Perfil Principal */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-br from-neon-cyan to-neon-magenta rounded-full flex items-center justify-center">
                  {profile.avatar ? (
                    <img 
                      src={profile.avatar} 
                      alt="Avatar" 
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-10 w-10 text-white" />
                  )}
                </div>
                <button className="absolute -bottom-1 -right-1 w-6 h-6 bg-cyber-bg border border-cyber-border rounded-full flex items-center justify-center hover:bg-cyber-surface transition-colors">
                  <Camera className="h-3 w-3 text-cyber-muted" />
                </button>
              </div>
              
              <div>
                <h2 className="text-xl font-bold text-cyber-text">{profile.name}</h2>
                <p className="text-cyber-muted">{profile.email}</p>
                <p className="text-sm text-cyber-muted mt-1">{profile.bio}</p>
              </div>
            </div>

            <Button
              variant={isEditing ? 'outline' : 'primary'}
              onClick={() => setIsEditing(!isEditing)}
              disabled={isSaving}
            >
              {isEditing ? 'Cancelar' : 'Editar Perfil'}
            </Button>
          </div>

          {/* Formulário de Edição */}
          {isEditing && (
            <div className="space-y-4 border-t border-cyber-border pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cyber-text mb-2">
                    Nome
                  </label>
                  <Input
                    value={editForm.name}
                    onChange={(e) => updateField('name', e.target.value)}
                    placeholder="Seu nome"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-cyber-text mb-2">
                    Email
                  </label>
                  <Input
                    value={editForm.email}
                    onChange={(e) => updateField('email', e.target.value)}
                    placeholder="<EMAIL>"
                    type="email"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-cyber-text mb-2">
                  Bio
                </label>
                <textarea
                  value={editForm.bio}
                  onChange={(e) => updateField('bio', e.target.value)}
                  placeholder="Conte um pouco sobre você..."
                  className="w-full px-3 py-2 bg-cyber-surface border border-cyber-border rounded-lg text-cyber-text placeholder-cyber-muted focus:outline-none focus:ring-2 focus:ring-neon-cyan/50 focus:border-neon-cyan resize-none"
                  rows={3}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleSaveProfile}
                  disabled={isSaving}
                  leftIcon={<Save className="h-4 w-4" />}
                >
                  {isSaving ? 'Salvando...' : 'Salvar Alterações'}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={isSaving}
                >
                  Cancelar
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Informações da Conta */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Estatísticas */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <User className="h-5 w-5 text-neon-cyan" />
              Informações da Conta
            </h3>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-cyber-muted">ID do Usuário:</span>
                <span className="text-cyber-text font-mono text-sm">{profile.id}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-cyber-muted">Função:</span>
                <span className="text-neon-cyan font-medium">{profile.role}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-cyber-muted">Conta criada:</span>
                <span className="text-cyber-text">
                  {new Date(profile.createdAt).toLocaleDateString('pt-BR')}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-cyber-muted">Último acesso:</span>
                <span className="text-cyber-text">
                  {new Date(profile.lastLogin).toLocaleString('pt-BR')}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Segurança */}
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-bold text-cyber-text mb-4 flex items-center gap-2">
              <Key className="h-5 w-5 text-neon-magenta" />
              Segurança
            </h3>
            
            <div className="space-y-4">
              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<Key className="h-4 w-4" />}
              >
                Alterar Senha
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<Mail className="h-4 w-4" />}
              >
                Alterar Email
              </Button>
              
              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<Calendar className="h-4 w-4" />}
              >
                Histórico de Sessões
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UsersManager;
