/**
 * @fileoverview Página de Debug Dashboard - Fase 3 do Sistema de Logging
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { DebugDashboard } from '../../components/debug/DebugDashboard';
import { useAuth } from '../../contexts/AuthContext';

// ============================================================================
// PÁGINA
// ============================================================================

/**
 * Página do Dashboard de Debug
 */
export const DebugPage: React.FC = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  // Verificar autenticação
  useEffect(() => {
    if (!loading && !user) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-cyber-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-neon-cyan mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando...</p>
        </div>
      </div>
    );
  }

  // Não autenticado
  if (!user) {
    return null;
  }

  return <DebugDashboard />;
};

export default DebugPage;
