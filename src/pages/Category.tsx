/**
 * @fileoverview Página de Categoria do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { But<PERSON>, Card, CardContent, Input } from '../components/ui';
import { usePosts } from '../hooks/usePosts';
import { supabase } from '../lib/supabaseClient';
import type { Category, Post } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface CategoryPageProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de categoria com posts filtrados
 */
export const CategoryPage: React.FC<CategoryPageProps> = ({ className }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [category, setCategory] = useState<Category | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'views' | 'likes'>('date');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 9;

  // Hooks do Supabase
  const { posts: allPosts, loading: postsLoading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });

  // Filtrar posts por categoria
  const posts = useMemo(() => {
    if (!slug || !allPosts.length) return [];
    return allPosts.filter((post: Post) => post.category.slug === slug);
  }, [allPosts, slug]);

  const loading = postsLoading;

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  // Carrega dados da categoria do Supabase
  useEffect(() => {
    const loadCategory = async () => {
      if (!slug) return;

      try {
        const { data: categoryData, error } = await supabase
          .from('categories')
          .select('*')
          .eq('slug', slug)
          .single();

        if (error) {
          console.error('Erro ao carregar categoria:', error);
          return;
        }

        if (categoryData) {
          setCategory({
            id: categoryData.id,
            name: categoryData.name,
            slug: categoryData.slug,
            description: categoryData.description || '',
            color: categoryData.color || '#00ffff',
            postsCount: posts.length,
          });
        }
      } catch (error) {
        console.error('Erro ao carregar categoria:', error);
      }
    };

    loadCategory();
  }, [slug, posts.length]);

  // Debug: Log dos dados carregados
  console.log('📂 [CategoryPage] Dados carregados:', {
    slug,
    postsCount: posts.length,
    category: category?.name,
    loading,
  });

  // Filtra e ordena posts
  const filteredPosts = posts
    .filter(
      (post) =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'views':
          return b.views - a.views;
        case 'likes':
          return b.likes - a.likes;
        case 'date':
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
    });

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando filtros mudam
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, sortBy]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando categoria...</p>
        </div>
      </div>
    );
  }

  if (!category) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-cyber-text mb-4">404</h1>
          <p className="text-cyber-muted mb-6">Categoria não encontrada</p>
          <Button>Voltar ao Blog</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-8', className)}>
      {/* Breadcrumb */}
      <nav>
        <div className="flex items-center space-x-2 text-sm text-cyber-muted">
          <a href="/" className="hover:text-neon-cyan transition-colors">
            Home
          </a>
          <span>/</span>
          <a href="/blog" className="hover:text-neon-cyan transition-colors">
            Blog
          </a>
          <span>/</span>
          <span className="text-cyber-text">Categorias</span>
          <span>/</span>
          <span className="text-cyber-text">{category.name}</span>
        </div>
      </nav>

      {/* Category Header */}
      <header className="text-center">
        <div className="mb-6">
          <span
            className="inline-block px-6 py-3 rounded-full text-2xl font-bold"
            style={{
              backgroundColor: `${category.color}20`,
              color: category.color,
              border: `2px solid ${category.color}30`,
            }}>
            {category.name}
          </span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-4">
          {category.name}
        </h1>

        <p className="text-xl text-cyber-muted max-w-2xl mx-auto mb-6">
          {category.description}
        </p>

        <div className="flex items-center justify-center space-x-6 text-sm text-cyber-muted">
          <div className="flex items-center space-x-2">
            <span>📝</span>
            <span>
              {posts.length} post{posts.length !== 1 ? 's' : ''}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span>👁️</span>
            <span>
              {posts.reduce((acc, post) => acc + post.views, 0)} visualizações
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span>❤️</span>
            <span>
              {posts.reduce((acc, post) => acc + post.likes, 0)} curtidas
            </span>
          </div>
        </div>
      </header>

      {/* Filters */}
      <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          {/* Search */}
          <div className="flex-1 w-full lg:w-auto">
            <Input
              type="search"
              placeholder={`Buscar em ${category.name}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              }
              fullWidth
            />
          </div>

          {/* Sort */}
          <div className="flex items-center space-x-4">
            <span className="text-sm text-cyber-muted">Ordenar por:</span>
            <div className="flex space-x-2">
              {[
                { key: 'date', label: 'Data', icon: '📅' },
                { key: 'views', label: 'Views', icon: '👁️' },
                { key: 'likes', label: 'Likes', icon: '❤️' },
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setSortBy(option.key as any)}
                  className={clsx(
                    'px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                    sortBy === option.key
                      ? 'bg-neon-cyan text-cyber-bg'
                      : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                  )}>
                  <span>{option.icon}</span>
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Results Info */}
        <div className="mt-4 text-sm text-cyber-muted">
          {filteredPosts.length === 0
            ? 'Nenhum post encontrado'
            : `${filteredPosts.length} post${
                filteredPosts.length !== 1 ? 's' : ''
              } encontrado${filteredPosts.length !== 1 ? 's' : ''}`}
        </div>
      </section>

      {/* Posts Grid */}
      {paginatedPosts.length > 0 ? (
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paginatedPosts.map((post) => (
              <Card
                key={post.id}
                className="group cursor-pointer"
                hoverable
                onClick={() => handlePostClick(post)}>
                <div className="aspect-video bg-gradient-cyber rounded-t-lg mb-4 relative overflow-hidden">
                  <img
                    src={post.thumbnail}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute bottom-3 right-3 bg-cyber-bg/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-cyber-muted">
                    {post.readTime} min
                  </div>
                  {post.featured && (
                    <div className="absolute top-3 right-3">
                      <span className="bg-neon-yellow text-cyber-bg px-2 py-1 rounded text-xs font-bold">
                        ⭐ DESTAQUE
                      </span>
                    </div>
                  )}
                </div>

                <CardContent>
                  <h3 className="text-lg font-bold text-cyber-text mb-2 group-hover:text-neon-cyan transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-cyber-muted text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-6 h-6 rounded-full"
                      />
                      <span className="text-xs text-cyber-muted">
                        {post.author.name}
                      </span>
                    </div>
                    <span className="text-xs text-cyber-muted">
                      {new Date(post.date).toLocaleDateString('pt-BR')}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-xs text-cyber-muted">
                    <div className="flex items-center space-x-3">
                      <span className="flex items-center space-x-1">
                        <span>👁️</span>
                        <span>{post.views}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span>❤️</span>
                        <span>{post.likes}</span>
                      </span>
                    </div>
                    <Button size="xs" variant="secondary">
                      Ler mais →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      ) : (
        <section className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-cyber-text mb-2">
            Nenhum post encontrado
          </h3>
          <p className="text-cyber-muted mb-6">
            Tente ajustar os filtros ou buscar por outros termos
          </p>
          <Button variant="secondary" onClick={() => setSearchQuery('')}>
            Limpar Busca
          </Button>
        </section>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <section className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}>
              ← Anterior
            </Button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={clsx(
                  'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                  currentPage === page
                    ? 'bg-neon-cyan text-cyber-bg'
                    : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                )}>
                {page}
              </button>
            ))}

            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}>
              Próxima →
            </Button>
          </div>
        </section>
      )}

      {/* Related Categories */}
      <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
        <h2 className="text-xl font-bold text-cyber-text mb-4">
          📂 Outras Categorias
        </h2>
        <div className="flex flex-wrap gap-3">
          {[
            {
              name: 'Inteligência Artificial',
              slug: 'inteligencia-artificial',
            },
            { name: 'Frontend', slug: 'frontend' },
            { name: 'DevOps', slug: 'devops' },
            { name: 'Backend', slug: 'backend' },
            { name: 'Mobile', slug: 'mobile' },
          ].map((category, index) => (
            <a
              key={index}
              href={`/categories/${category.slug}`}
              className="px-4 py-2 rounded-lg bg-cyber-bg text-cyber-muted hover:text-neon-cyan border border-cyber-border hover:border-neon-cyan transition-all duration-200">
              {category.name}
            </a>
          ))}
        </div>
      </section>
    </div>
  );
};

export default CategoryPage;
