/**
 * @fileoverview Página de Tag do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { <PERSON><PERSON>, Card, CardContent, Input } from '../components/ui';
import AuthorAvatar from '../components/ui/AuthorAvatar';
import { usePosts } from '../hooks/usePosts';
import { useTags } from '../hooks/useTags';
import type { Post } from '../types';

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Normaliza string para slug consistente
 */
const normalizeSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-') // Remove hífens duplicados
    .trim();
};

// ============================================================================
// INTERFACES
// ============================================================================

interface TagPageProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de tag com posts relacionados
 */
export const TagPage: React.FC<TagPageProps> = ({ className }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 6;

  // Hooks do Supabase
  const { posts: allPosts, loading: postsLoading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });
  const { tags, loading: tagsLoading } = useTags();

  // Filtrar posts por tag
  const posts = useMemo(() => {
    if (!slug || !allPosts.length) return [];

    // Converte slug para nome da tag para comparação
    const currentTag = tags.find((t: any) => t.slug === slug);
    const tagName = currentTag?.name;

    if (!tagName) return [];

    return allPosts.filter((post: Post) => {
      if (!post.tags || !Array.isArray(post.tags)) return false;

      return post.tags.some((tag: any) => {
        // Se tag é string (nome), compara diretamente e por slug normalizado
        if (typeof tag === 'string') {
          return tag === tagName || normalizeSlug(tag) === slug;
        }
        // Se tag é objeto, compara slug ou nome
        return (
          tag.slug === slug ||
          tag.name === tagName ||
          normalizeSlug(tag.name || '') === slug
        );
      });
    });
  }, [allPosts, slug, tags]);

  // Tag atual
  const currentTag = tags.find((t: any) => t.slug === slug);
  const loading = postsLoading || tagsLoading;

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  // Debug: Log dos dados carregados
  console.log('🏷️ [TagPage] Dados carregados:', {
    slug,
    postsCount: posts.length,
    allPostsCount: allPosts.length,
    tagsCount: tags.length,
    currentTag: currentTag?.name,
    loading,
    firstPostTags: allPosts[0]?.tags,
    tagsStructure: allPosts.slice(0, 2).map((post) => ({
      title: post.title,
      tags: post.tags,
      tagsType: typeof post.tags?.[0],
    })),
  });

  // Filtra posts baseado na busca
  const filteredPosts = posts.filter(
    (post) =>
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando busca muda
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando tag...</p>
        </div>
      </div>
    );
  }

  if (!currentTag && !loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-cyber-text mb-4">404</h1>
          <p className="text-cyber-muted mb-6">Tag não encontrada</p>
          <Button onClick={() => navigate('/blog')}>Voltar ao Blog</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-8', className)}>
      {/* Breadcrumb */}
      <nav>
        <div className="flex items-center space-x-2 text-sm text-cyber-muted">
          <a href="/" className="hover:text-neon-cyan transition-colors">
            Home
          </a>
          <span>/</span>
          <a href="/blog" className="hover:text-neon-cyan transition-colors">
            Blog
          </a>
          <span>/</span>
          <span className="text-cyber-text">Tags</span>
          <span>/</span>
          <span className="text-cyber-text">#{currentTag?.name || slug}</span>
        </div>
      </nav>

      {/* Tag Header */}
      <header className="text-center">
        <div className="mb-6">
          <span className="inline-block px-6 py-3 rounded-full text-2xl font-bold bg-neon-purple/20 text-neon-purple border-2 border-neon-purple/30">
            #{currentTag?.name || slug}
          </span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-4">
          Posts sobre #{currentTag?.name || slug}
        </h1>

        <p className="text-xl text-cyber-muted max-w-2xl mx-auto mb-6">
          Explore todos os artigos relacionados à tag{' '}
          <strong>#{currentTag?.name || slug}</strong>
        </p>

        <div className="flex items-center justify-center space-x-6 text-sm text-cyber-muted">
          <div className="flex items-center space-x-2">
            <span>📝</span>
            <span>
              {posts.length} post{posts.length !== 1 ? 's' : ''}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span>👁️</span>
            <span>
              {posts.reduce((acc, post) => acc + post.views, 0)} visualizações
            </span>
          </div>
        </div>
      </header>

      {/* Search */}
      <section className="max-w-md mx-auto">
        <Input
          type="search"
          placeholder={`Buscar em #${currentTag?.name || slug}...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          leftIcon={
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          }
          fullWidth
        />

        {filteredPosts.length !== posts.length && (
          <div className="mt-2 text-sm text-cyber-muted text-center">
            {filteredPosts.length} de {posts.length} posts
          </div>
        )}
      </section>

      {/* Posts Grid */}
      {paginatedPosts.length > 0 ? (
        <section>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {paginatedPosts.map((post) => (
              <Card
                key={post.id}
                className="group cursor-pointer"
                hoverable
                onClick={() => handlePostClick(post)}>
                <div className="aspect-video bg-gradient-cyber rounded-t-lg mb-4 relative overflow-hidden">
                  <img
                    src={post.thumbnail}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-3 left-3">
                    <span
                      className="px-2 py-1 rounded text-xs font-medium"
                      style={{
                        backgroundColor: `${post.category.color}20`,
                        color: post.category.color,
                        border: `1px solid ${post.category.color}30`,
                      }}>
                      {post.category.name}
                    </span>
                  </div>
                  <div className="absolute bottom-3 right-3 bg-cyber-bg/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-cyber-muted">
                    {post.readTime} min
                  </div>
                </div>

                <CardContent>
                  <h3 className="text-xl font-bold text-cyber-text mb-3 group-hover:text-neon-cyan transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-cyber-muted mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Tags do post */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((postTag) => (
                      <a
                        key={postTag.id}
                        href={`/tags/${postTag.slug}`}
                        className={clsx(
                          'px-2 py-1 rounded text-xs border transition-colors',
                          postTag.slug === slug
                            ? 'bg-neon-purple/20 text-neon-purple border-neon-purple/30'
                            : 'bg-cyber-bg text-cyber-muted border-cyber-border hover:text-neon-cyan hover:border-neon-cyan'
                        )}>
                        #{postTag.name}
                      </a>
                    ))}
                    {post.tags.length > 3 && (
                      <span className="px-2 py-1 rounded text-xs bg-cyber-bg text-cyber-muted border border-cyber-border">
                        +{post.tags.length - 3}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AuthorAvatar
                        avatar={post.author.avatar}
                        name={post.author.name}
                        size="md"
                      />
                      <div>
                        <p className="text-sm font-medium text-cyber-text">
                          {post.author.name}
                        </p>
                        <p className="text-xs text-cyber-muted">
                          {new Date(post.date).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-xs text-cyber-muted">
                      <span className="flex items-center space-x-1">
                        <span>👁️</span>
                        <span>{post.views}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span>❤️</span>
                        <span>{post.likes}</span>
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      ) : (
        <section className="text-center py-12">
          <div className="text-6xl mb-4">🏷️</div>
          <h3 className="text-xl font-semibold text-cyber-text mb-2">
            Nenhum post encontrado
          </h3>
          <p className="text-cyber-muted mb-6">
            Tente buscar por outros termos ou explore outras tags
          </p>
          <Button variant="secondary" onClick={() => setSearchQuery('')}>
            Limpar Busca
          </Button>
        </section>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <section className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}>
              ← Anterior
            </Button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={clsx(
                  'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                  currentPage === page
                    ? 'bg-neon-purple text-cyber-bg'
                    : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                )}>
                {page}
              </button>
            ))}

            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}>
              Próxima →
            </Button>
          </div>
        </section>
      )}

      {/* Tag Cloud */}
      <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
        <h2 className="text-xl font-bold text-cyber-text mb-4">
          🏷️ Outras Tags Populares
        </h2>
        <div className="flex flex-wrap gap-3">
          {tags
            .filter((t) => t.slug !== slug)
            .slice(0, 15)
            .map((otherTag) => (
              <a
                key={otherTag.id}
                href={`/tags/${otherTag.slug}`}
                className="px-3 py-2 rounded-lg bg-cyber-bg text-cyber-muted hover:text-neon-purple border border-cyber-border hover:border-neon-purple transition-all duration-200">
                #{otherTag.name}
              </a>
            ))}
        </div>
      </section>
    </div>
  );
};

export default TagPage;
