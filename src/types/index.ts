/**
 * @fileoverview Tipos TypeScript para o Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

// ============================================================================
// TIPOS PRINCIPAIS
// ============================================================================

/**
 * Representa um post do blog
 */
export interface Post {
  id: string;
  title: string;
  slug: string;
  author: Author;
  date: string;
  category: Category;
  tags: Tag[];
  thumbnail: string;
  excerpt: string;
  content: string;
  featured: boolean;
  published: boolean;
  views: number;
  likes: number;
  readTime: number;
  seo: SEOData;
  createdAt: string;
  updatedAt: string;
}

/**
 * Representa um autor
 */
export interface Author {
  id: string;
  name: string;
  email: string;
  avatar: string;
  bio: string;
  role: UserRole;
  socialLinks: SocialLinks;
  postsCount: number;
  joinedAt: string;
}

/**
 * Representa uma categoria
 */
export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  postsCount: number;
}

/**
 * Representa uma tag
 */
export interface Tag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  postsCount: number;
}

/**
 * Dados de SEO para posts
 */
export interface SEOData {
  title: string;
  description: string;
  keywords: string;
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: 'website' | 'article';
  twitterCard?: 'summary' | 'summary_large_image';
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
}

/**
 * Links sociais
 */
export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  instagram?: string;
}

// ============================================================================
// TIPOS DE AUTENTICAÇÃO
// ============================================================================

/**
 * Roles de usuário
 */
export type UserRole = 'admin' | 'editor' | 'writer' | 'reader';

/**
 * Usuário autenticado
 */
export interface User {
  id: string;
  email: string;
  name: string;
  avatar: string;
  role: UserRole;
  permissions: Permission[];
  preferences: UserPreferences;
  lastLogin?: string;
  createdAt: string;
  bio?: string;
}

/**
 * Permissões do usuário
 */
export interface Permission {
  resource: string;
  actions: string[];
}

/**
 * Preferências do usuário
 */
export interface UserPreferences {
  theme: 'dark' | 'light' | 'auto';
  language: 'pt' | 'en' | 'es';
  notifications: NotificationSettings;
}

/**
 * Configurações de notificação
 */
export interface NotificationSettings {
  email: boolean;
  push: boolean;
  comments: boolean;
  mentions: boolean;
}

// ============================================================================
// TIPOS DE UI/UX
// ============================================================================

/**
 * Variantes de componentes
 */
export type ComponentVariant =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'outline';

/**
 * Tamanhos de componentes
 */
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Props base para componentes
 */
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  variant?: ComponentVariant;
  size?: ComponentSize;
  disabled?: boolean;
}

/**
 * Estado de loading
 */
export interface LoadingState {
  isLoading: boolean;
  error?: string;
  data?: any;
}

// ============================================================================
// TIPOS DE NAVEGAÇÃO
// ============================================================================

/**
 * Item de menu
 */
export interface MenuItem {
  id: string;
  label: string;
  href: string;
  icon?: string | React.ReactNode;
  children?: MenuItem[];
  external?: boolean;
  badge?: string;
}

/**
 * Breadcrumb item
 */
export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

// ============================================================================
// TIPOS DE DASHBOARD
// ============================================================================

/**
 * Métricas do dashboard
 */
export interface DashboardMetrics {
  totalPosts: number;
  totalViews: number;
  totalUsers: number;
  totalComments: number;
  growthRate: number;
  popularPosts: Post[];
  recentActivity: Activity[];
}

/**
 * Atividade do sistema
 */
export interface Activity {
  id: string;
  type: 'post_created' | 'post_updated' | 'user_registered' | 'comment_added';
  description: string;
  user: Author;
  timestamp: string;
  metadata?: Record<string, any>;
}

// ============================================================================
// TIPOS DE API
// ============================================================================

/**
 * Resposta padrão da API
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: PaginationInfo;
}

/**
 * Informações de paginação
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Filtros de busca
 */
export interface SearchFilters {
  query?: string;
  category?: string;
  tags?: string[];
  author?: string;
  dateFrom?: string;
  dateTo?: string;
  featured?: boolean;
  published?: boolean;
}

// ============================================================================
// TIPOS DE CONFIGURAÇÃO
// ============================================================================

/**
 * Configurações do site
 */
export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  logo: string;
  favicon: string;
  socialLinks: SocialLinks;
  analytics: AnalyticsConfig;
  seo: GlobalSEOConfig;
}

/**
 * Configurações de analytics
 */
export interface AnalyticsConfig {
  googleAnalytics?: string;
  googleTagManager?: string;
  facebookPixel?: string;
}

/**
 * Configurações globais de SEO
 */
export interface GlobalSEOConfig {
  defaultTitle: string;
  titleTemplate: string;
  defaultDescription: string;
  defaultKeywords: string[];
  defaultOgImage: string;
}

// ============================================================================
// TIPOS DE AUTENTICAÇÃO (EXTENSÕES)
// ============================================================================

/**
 * Status de autenticação
 */
export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated';

/**
 * Dados de login
 */
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Dados de registro
 */
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

/**
 * Context de autenticação
 */
export interface AuthContextType {
  user: User | null;
  status: AuthStatus;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  hasPermission: (requiredRole: UserRole) => boolean;
}

/**
 * Configuração de permissões por role
 */
export interface RolePermissions {
  [key: string]: {
    canRead: boolean;
    canWrite: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canManageUsers: boolean;
    canManageSettings: boolean;
  };
}

// ============================================================================
// EXPORTS
// ============================================================================

// Todos os tipos são exportados individualmente acima
// Não é necessário um export default para tipos TypeScript
