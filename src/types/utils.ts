// 🎯 Tipos utilitários e helpers TypeScript

// ===== UTILITY TYPES =====

// Torna todas as propriedades opcionais exceto as especificadas
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

// Torna todas as propriedades obrigatórias exceto as especificadas
export type RequiredExcept<T, K extends keyof T> = Required<T> & Partial<Pick<T, K>>;

// Extrai tipos de arrays
export type ArrayElement<T> = T extends (infer U)[] ? U : never;

// Extrai tipos de Promises
export type PromiseType<T> = T extends Promise<infer U> ? U : never;

// Extrai tipos de funções
export type ReturnTypeOf<T> = T extends (...args: any[]) => infer R ? R : never;

// Cria um tipo com propriedades aninhadas opcionais
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Cria um tipo com propriedades aninhadas obrigatórias
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// Remove propriedades null/undefined
export type NonNullable<T> = T extends null | undefined ? never : T;

// Cria união de valores de um objeto
export type ValueOf<T> = T[keyof T];

// Cria união de chaves de um objeto
export type KeyOf<T> = keyof T;

// ===== STRING UTILITIES =====

// Converte string para camelCase type-level
export type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`
  ? `${P1}${Uppercase<P2>}${CamelCase<P3>}`
  : S;

// Converte string para snake_case type-level
export type SnakeCase<S extends string> = S extends `${infer T}${infer U}`
  ? `${T extends Capitalize<T> ? '_' : ''}${Lowercase<T>}${SnakeCase<U>}`
  : S;

// ===== OBJECT UTILITIES =====

// Pega apenas propriedades de um tipo específico
export type PickByType<T, U> = {
  [K in keyof T as T[K] extends U ? K : never]: T[K];
};

// Remove propriedades de um tipo específico
export type OmitByType<T, U> = {
  [K in keyof T as T[K] extends U ? never : K]: T[K];
};

// Cria um tipo com propriedades renomeadas
export type Rename<T, R extends Record<keyof T, string>> = {
  [K in keyof R]: T[K extends keyof T ? K : never];
};

// ===== FUNCTION UTILITIES =====

// Extrai parâmetros de uma função
export type Parameters<T> = T extends (...args: infer P) => any ? P : never;

// Cria tipo de função com parâmetros específicos
export type FunctionWithParams<P extends readonly unknown[], R = void> = (...args: P) => R;

// Cria tipo de função async
export type AsyncFunction<P extends readonly unknown[] = [], R = void> = (...args: P) => Promise<R>;

// ===== CONDITIONAL TYPES =====

// Verifica se um tipo é uma função
export type IsFunction<T> = T extends (...args: any[]) => any ? true : false;

// Verifica se um tipo é um array
export type IsArray<T> = T extends any[] ? true : false;

// Verifica se um tipo é um objeto
export type IsObject<T> = T extends object ? (T extends any[] ? false : true) : false;

// ===== BRANDED TYPES =====

// Cria tipos "branded" para maior type safety
export type Brand<T, B> = T & { __brand: B };

// Exemplos de branded types
export type UserId = Brand<string, 'UserId'>;
export type PostId = Brand<string, 'PostId'>;
export type ProjectId = Brand<string, 'ProjectId'>;
export type Email = Brand<string, 'Email'>;
export type URL = Brand<string, 'URL'>;
export type Timestamp = Brand<number, 'Timestamp'>;

// ===== EVENT TYPES =====

// Tipos para eventos customizados
export interface CustomEvent<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  source?: string;
}

// Handler para eventos
export type EventHandler<T = any> = (event: CustomEvent<T>) => void;

// Mapa de eventos
export type EventMap = Record<string, any>;

// Listener de eventos tipado
export type EventListener<T extends EventMap, K extends keyof T> = (
  event: CustomEvent<T[K]>
) => void;

// ===== FORM UTILITIES =====

// Extrai tipos de campos de formulário
export type FormFields<T> = {
  [K in keyof T]: {
    value: T[K];
    error?: string;
    touched?: boolean;
    disabled?: boolean;
  };
};

// Valores de formulário
export type FormValues<T> = {
  [K in keyof T]: T[K];
};

// Erros de formulário
export type FormErrors<T> = {
  [K in keyof T]?: string;
};

// ===== API UTILITIES =====

// Wrapper para respostas de API
export type ApiResult<T, E = string> = { success: true; data: T } | { success: false; error: E };

// Status de requisição
export type RequestStatus = 'idle' | 'loading' | 'success' | 'error';

// Estado de dados assíncronos
export type AsyncData<T, E = string> = {
  data: T | null;
  status: RequestStatus;
  error: E | null;
};

// ===== COMPONENT UTILITIES =====

// Props de componente React
export type ComponentProps<T> = T extends React.ComponentType<infer P> ? P : never;

// Ref de componente React
export type ComponentRef<T> = T extends React.ComponentType<any> ? React.ComponentRef<T> : never;

// Props com ref
export type PropsWithRef<T, R = HTMLElement> = T & {
  ref?: React.Ref<R>;
};

// Props com children
export type PropsWithChildren<T = {}> = T & {
  children?: React.ReactNode;
};

// ===== THEME UTILITIES =====

// Valores de tema
export type ThemeValue<T> = T | ((theme: any) => T);

// Propriedades de estilo
export type StyleProps = {
  [K in keyof React.CSSProperties]?: ThemeValue<React.CSSProperties[K]>;
};

// Variantes de componente
export type ComponentVariant<T extends string> = {
  [K in T]: StyleProps;
};

// ===== VALIDATION UTILITIES =====

// Schema de validação
export type ValidationSchema<T> = {
  [K in keyof T]?: (value: T[K]) => string | undefined;
};

// Resultado de validação
export type ValidationResult<T> = {
  isValid: boolean;
  errors: FormErrors<T>;
};

// ===== ROUTER UTILITIES =====

// Parâmetros de rota
export type RouteParams<T extends string> = T extends `${string}:${infer P}/${infer R}`
  ? { [K in P]: string } & RouteParams<R>
  : T extends `${string}:${infer P}`
    ? { [K in P]: string }
    : {};

// Query parameters
export type QueryParams = Record<string, string | string[] | undefined>;

// ===== STORAGE UTILITIES =====

// Chaves de localStorage
export type StorageKey = string;

// Valor de storage serializado
export type StorageValue<T> = {
  value: T;
  timestamp: number;
  ttl?: number;
};

// ===== HELPER FUNCTIONS TYPES =====

// Função de comparação
export type CompareFn<T> = (a: T, b: T) => number;

// Função de filtro
export type FilterFn<T> = (item: T, index: number, array: T[]) => boolean;

// Função de mapeamento
export type MapFn<T, U> = (item: T, index: number, array: T[]) => U;

// Função de redução
export type ReduceFn<T, U> = (acc: U, item: T, index: number, array: T[]) => U;

// ===== EXPORT UTILITIES =====

// Re-export de tipos úteis do React
export type {
  ReactNode,
  ReactElement,
  ComponentType,
  FC,
  PropsWithChildren as ReactPropsWithChildren,
} from 'react';

// Re-export de tipos úteis do DOM
export type {
  HTMLAttributes,
  ButtonHTMLAttributes,
  InputHTMLAttributes,
  FormHTMLAttributes,
} from 'react';
