// Tipos específicos para integração com Supabase
// Baseados na estrutura da tabela posts

export interface SupabasePost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string | null;
  featured_image?: string | null;
  thumbnail?: string | null; // Campo adicional para thumbnail
  featured?: boolean | null; // Campo para post em destaque
  status: 'draft' | 'published';
  language: 'pt' | 'en';
  tags?: string[] | null; // Array de strings (LEGACY - será removido)
  reading_time?: number | null;
  views?: number | null;
  likes?: number | null;
  author_id?: string | null;
  category_id?: string | null; // FK para categoria principal
  created_at: string;
  updated_at: string;
  published_at?: string | null;
}

// Alias para compatibilidade (remover gradualmente)
export interface SupabaseDevlog extends SupabasePost {}

export interface SupabaseCategory {
  id: string;
  name: string;
  slug: string;
  description?: string | null;
  color?: string | null;
  created_at: string;
}

export interface SupabasePostCategory {
  post_id: string;
  category_id: string;
}

// Tipos para queries e filtros
export interface PostFilters {
  status?: 'draft' | 'published';
  language?: 'pt' | 'en';
  author_id?: string;
  limit?: number;
  offset?: number;
  search?: string;
  tags?: string[];
}

// Alias para compatibilidade (remover gradualmente)
export interface DevlogFilters extends PostFilters {}

export interface PostWithCategories extends SupabasePost {
  categories?: SupabaseCategory[];
}

// Alias para compatibilidade (remover gradualmente)
export interface DevlogWithCategories extends PostWithCategories {}

// Tipos para respostas da API
export interface SupabaseResponse<T> {
  data: T | null;
  error: any | null;
  count?: number | null;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Tipos para criação/atualização
export interface CreatePostData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  thumbnail?: string;
  featured?: boolean;
  status?: 'draft' | 'published';
  language?: 'pt' | 'en';
  tags?: string[]; // LEGACY - será removido
  reading_time?: number;
  author_id?: string;
  category_id?: string; // FK para categoria principal
  published_at?: string;
}

export interface UpdatePostData extends Partial<CreatePostData> {
  id: string;
  updated_at?: string;
}

// Aliases para compatibilidade (remover gradualmente)
export interface CreateDevlogData extends CreatePostData {}
export interface UpdateDevlogData extends UpdatePostData {}

// Utilitários para conversão de tipos
export const parsePostTags = (
  tags: string[] | string | null | undefined
): string[] => {
  if (!tags) return [];

  // Se já é um array, retorna diretamente
  if (Array.isArray(tags)) {
    return tags;
  }

  // Se é string, tenta fazer parse como JSON primeiro
  try {
    return JSON.parse(tags);
  } catch {
    // Se falhar, assume que é string separada por vírgula
    return tags
      .split(',')
      .map((tag) => tag.trim())
      .filter(Boolean);
  }
};

export const stringifyPostTags = (tags: string[]): string => {
  return JSON.stringify(tags);
};

// Aliases para compatibilidade (remover gradualmente)
export const parseDevlogTags = parsePostTags;
export const stringifyDevlogTags = stringifyPostTags;

// Função para converter SupabasePost para o tipo Post usado no frontend
export const convertSupabasePostToPost = (post: SupabasePost): any => {
  return {
    id: post.id,
    title: post.title,
    slug: post.slug,
    content: post.content,
    excerpt: post.excerpt,
    featured_image: post.featured_image,
    status: post.status,
    language: post.language,
    tags: parsePostTags(post.tags),
    reading_time: post.reading_time,
    views: post.views,
    likes: post.likes,
    author_id: post.author_id,
    created_at: post.created_at,
    updated_at: post.updated_at,
    published_at: post.published_at,
    // Campos adicionais para compatibilidade
    category: 'blog', // Categoria padrão para posts
    type: 'post',
  };
};

// Alias para compatibilidade (remover gradualmente)
export const convertSupabaseDevlogToPost = convertSupabasePostToPost;
