// API Service para comunicação com Backend Rust via Nginx Proxy
// Em produção: usa proxy nginx (/api/* → backend:3001)
// Em desenvolvimento: pode usar direto ou proxy

import type { ApiResponse, Post, User } from '../types';

// ===== INTERFACES LOCAIS =====
// TODO: Mover para types quando implementado
interface PostsQuery {
  status?: string;
  category?: string;
  tag?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

interface PostFormData {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  published?: boolean;
  featured?: boolean;
  category_id?: string;
  tags?: string[];
}
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: string;
  signal?: AbortSignal;
}

interface SearchFilters {
  type?: 'posts' | 'projects' | 'users';
  category?: string;
  author?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

interface UserRegistrationData {
  name: string;
  email: string;
  password: string;
  confirmPassword?: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  post_count: number;
  created_at: string;
  updated_at: string;
}

interface SearchResult {
  id: string;
  type: 'post' | 'project' | 'user';
  title: string;
  excerpt: string;
  url: string;
  score: number;
  highlighted?: string[];
}

// ===== CONFIGURAÇÃO =====
const API_BASE_URL =
  (import.meta as any).env?.VITE_API_URL || 'https://api.blueprintblog.tech';

// ===== CLASSE PRINCIPAL =====
class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  /**
   * Método genérico para fazer requisições HTTP
   * @param endpoint - Endpoint da API
   * @param options - Opções da requisição
   * @returns Promise com a resposta da API
   */
  private async request<T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // Adicionar token de autenticação se existir
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // ===== HEALTH CHECK =====
  /**
   * Verifica se a API está funcionando
   * @returns Promise com status da API
   */
  async healthCheck(): Promise<
    ApiResponse<{ status: string; timestamp: string }>
  > {
    return this.request('/health');
  }

  // ===== POSTS =====
  /**
   * Obtém lista de posts com filtros opcionais
   * @param params - Parâmetros de query
   * @returns Promise com lista de posts
   */
  async getPosts(params: PostsQuery = {}): Promise<ApiResponse<Post[]>> {
    const queryString = new URLSearchParams(
      Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null) {
          acc[key] = String(value);
        }
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    return this.request(`/api/posts${queryString ? `?${queryString}` : ''}`);
  }

  /**
   * Obtém um post específico por ID
   * @param id - ID do post
   * @returns Promise com o post
   */
  async getPost(id: string): Promise<ApiResponse<Post>> {
    return this.request(`/api/posts/${id}`);
  }

  /**
   * Obtém um post específico por slug
   * @param slug - Slug do post
   * @returns Promise com o post
   */
  async getPostBySlug(slug: string): Promise<ApiResponse<Post>> {
    return this.request(`/api/posts/slug/${slug}`);
  }

  /**
   * Cria um novo post
   * @param postData - Dados do post
   * @returns Promise com o post criado
   */
  async createPost(postData: PostFormData): Promise<ApiResponse<Post>> {
    return this.request('/api/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
  }

  /**
   * Atualiza um post existente
   * @param id - ID do post
   * @param postData - Dados atualizados do post
   * @returns Promise com o post atualizado
   */
  async updatePost(
    id: string,
    postData: Partial<PostFormData>
  ): Promise<ApiResponse<Post>> {
    return this.request(`/api/posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    });
  }

  /**
   * Deleta um post
   * @param id - ID do post
   * @returns Promise com confirmação
   */
  async deletePost(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/posts/${id}`, {
      method: 'DELETE',
    });
  }

  /**
   * Incrementa o contador de visualizações de um post
   * @param id - ID do post
   * @returns Promise com confirmação
   */
  async incrementPostViews(id: string): Promise<ApiResponse<void>> {
    return this.request(`/api/posts/${id}/views`, {
      method: 'POST',
    });
  }

  // ===== USERS =====
  /**
   * Obtém lista de usuários
   * @returns Promise com lista de usuários
   */
  async getUsers(): Promise<ApiResponse<User[]>> {
    return this.request('/api/users');
  }

  /**
   * Obtém um usuário específico por ID
   * @param id - ID do usuário
   * @returns Promise com o usuário
   */
  async getUser(id: string): Promise<ApiResponse<User>> {
    return this.request(`/api/users/${id}`);
  }

  /**
   * Obtém dados do usuário atual
   * @returns Promise com o usuário atual
   */
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request('/api/users/me');
  }

  // ===== AUTH =====
  /**
   * Faz login do usuário
   * @param email - Email do usuário
   * @param password - Senha do usuário
   * @returns Promise com dados de autenticação
   */
  async login(
    email: string,
    password: string
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  /**
   * Registra um novo usuário
   * @param userData - Dados do usuário
   * @returns Promise com o usuário criado
   */
  async register(userData: UserRegistrationData): Promise<ApiResponse<User>> {
    return this.request('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  /**
   * Faz logout do usuário
   * @returns Promise com confirmação
   */
  async logout(): Promise<ApiResponse<void>> {
    return this.request('/api/auth/logout', {
      method: 'POST',
    });
  }

  /**
   * Atualiza o token de autenticação
   * @returns Promise com novo token
   */
  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return this.request('/api/auth/refresh', {
      method: 'POST',
    });
  }

  // ===== CATEGORIES =====
  /**
   * Obtém lista de categorias
   * @returns Promise com lista de categorias
   */
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request('/api/categories');
  }

  /**
   * Obtém uma categoria específica
   * @param id - ID da categoria
   * @returns Promise com a categoria
   */
  async getCategory(id: string): Promise<ApiResponse<Category>> {
    return this.request(`/api/categories/${id}`);
  }

  // ===== SEARCH =====
  /**
   * Realiza busca no conteúdo
   * @param query - Termo de busca
   * @param filters - Filtros opcionais
   * @returns Promise com resultados da busca
   */
  async search(
    query: string,
    filters: SearchFilters = {}
  ): Promise<ApiResponse<SearchResult[]>> {
    const params = { q: query, ...filters };
    const queryString = new URLSearchParams(
      Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null) {
          acc[key] = String(value);
        }
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    return this.request(`/api/search?${queryString}`);
  }

  // ===== UTILITY METHODS =====
  /**
   * Define o token de autenticação
   * @param token - Token JWT
   */
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  /**
   * Remove o token de autenticação
   */
  clearAuthToken(): void {
    localStorage.removeItem('auth_token');
  }

  /**
   * Verifica se o usuário está autenticado
   * @returns True se há token válido
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  /**
   * Obtém o token atual
   * @returns Token ou null
   */
  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }
}

// ===== INSTÂNCIA SINGLETON =====
export const apiService = new ApiService();
export default apiService;

// ===== EXPORTS ADICIONAIS =====
export type {
  Category,
  RequestOptions,
  SearchFilters,
  SearchResult,
  UserRegistrationData,
};
