import { supabase } from '../lib/supabaseClient';
import type {
  CreateDevlogData,
  DevlogFilters,
  SupabaseDevlog,
  SupabaseResponse,
  UpdateDevlogData,
} from '../types/supabase';

// ===== POSTS SERVICE (usando tabela posts) =====
export class SupabasePostsService {
  /**
   * Busca posts com filtros opcionais
   */
  async getPosts(
    filters: DevlogFilters = {}
  ): Promise<SupabaseResponse<SupabaseDevlog[]>> {
    try {
      let query = supabase.from('posts').select('*');

      // Aplicar filtros
      if (filters.language) {
        query = query.eq('language', filters.language);
      }

      if (filters.author_id) {
        query = query.eq('author_id', filters.author_id);
      }

      if (filters.search) {
        query = query.or(
          `title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`
        );
      }

      // Ordenação padrão: mais recentes primeiro
      query = query.order('created_at', { ascending: false });

      // Paginação
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(
          filters.offset,
          filters.offset + (filters.limit || 10) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Erro ao buscar posts:', error);
        return { data: null, error };
      }

      return { data: data || [], error: null, count };
    } catch (error) {
      console.error('Erro inesperado ao buscar posts:', error);
      return { data: null, error };
    }
  }

  /**
   * Busca um post específico por slug
   */
  async getPostBySlug(slug: string): Promise<SupabaseResponse<SupabaseDevlog>> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) {
        console.error('Erro ao buscar post por slug:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar post por slug:', error);
      return { data: null, error };
    }
  }

  /**
   * Busca um post específico por ID
   */
  async getPostById(id: string): Promise<SupabaseResponse<SupabaseDevlog>> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Erro ao buscar post por ID:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar post por ID:', error);
      return { data: null, error };
    }
  }

  /**
   * Cria um novo post
   */
  async createPost(
    postData: CreateDevlogData
  ): Promise<SupabaseResponse<SupabaseDevlog>> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .insert([
          {
            ...postData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar post:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao criar post:', error);
      return { data: null, error };
    }
  }

  /**
   * Atualiza um post existente
   */
  async updatePost(
    postData: UpdateDevlogData
  ): Promise<SupabaseResponse<SupabaseDevlog>> {
    try {
      const { id, ...updateData } = postData;

      const { data, error } = await supabase
        .from('posts')
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar post:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar post:', error);
      return { data: null, error };
    }
  }

  /**
   * Deleta um post
   */
  async deletePost(id: string): Promise<SupabaseResponse<null>> {
    try {
      const { error } = await supabase.from('posts').delete().eq('id', id);

      if (error) {
        console.error('Erro ao deletar post:', error);
        return { data: null, error };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao deletar post:', error);
      return { data: null, error };
    }
  }

  /**
   * Incrementa o contador de visualizações
   */
  async incrementViews(id: string): Promise<SupabaseResponse<null>> {
    try {
      // Temporariamente desabilitado até criar a função no Supabase
      console.log('📊 View incrementada para post:', id);
      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao incrementar visualizações:', error);
      return { data: null, error };
    }
  }

  /**
   * Remove destaque de todos os posts (para garantir apenas um em destaque)
   */
  async clearAllFeatured(): Promise<SupabaseResponse<null>> {
    try {
      const { error } = await supabase
        .from('posts')
        .update({ featured: false })
        .eq('featured', true);

      if (error) {
        console.error('Erro ao remover destaques:', error);
        return { data: null, error };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao remover destaques:', error);
      return { data: null, error };
    }
  }
}

// ===== PROJECTS SERVICE (para devlogs) =====
export class SupabaseProjectsService {
  /**
   * Busca projetos (devlogs) com filtros opcionais
   */
  async getProjects(
    filters: DevlogFilters = {}
  ): Promise<SupabaseResponse<SupabaseDevlog[]>> {
    try {
      let query = supabase.from('projects').select('*');

      // Aplicar filtros
      if (filters.language) {
        query = query.eq('language', filters.language);
      }

      if (filters.author_id) {
        query = query.eq('author_id', filters.author_id);
      }

      if (filters.search) {
        query = query.or(
          `title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`
        );
      }

      // Ordenação padrão: mais recentes primeiro
      query = query.order('created_at', { ascending: false });

      // Paginação
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(
          filters.offset,
          filters.offset + (filters.limit || 10) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Erro ao buscar projetos:', error);
        return { data: null, error };
      }

      return { data: data || [], error: null, count };
    } catch (error) {
      console.error('Erro inesperado ao buscar projetos:', error);
      return { data: null, error };
    }
  }

  /**
   * Busca um projeto específico por slug
   */
  async getProjectBySlug(
    slug: string
  ): Promise<SupabaseResponse<SupabaseDevlog>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) {
        console.error('Erro ao buscar projeto por slug:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar projeto por slug:', error);
      return { data: null, error };
    }
  }
}

// Instâncias dos services
export const supabasePostsService = new SupabasePostsService();
export const supabaseProjectsService = new SupabaseProjectsService();
