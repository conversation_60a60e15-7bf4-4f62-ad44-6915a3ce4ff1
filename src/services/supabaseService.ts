import { supabase } from '../lib/supabaseClient';
import type { SupabaseCategory, SupabaseResponse } from '../types/supabase';

// ===== CATEGORIES SERVICE =====
// Removido SupabaseDevlogService - não há mais tabela devlogs

export class SupabaseCategoryService {
  /**
   * Busca todas as categorias
   */
  async getCategories(): Promise<SupabaseResponse<SupabaseCategory[]>> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Erro ao buscar categorias:', error);
        return { data: null, error };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar categorias:', error);
      return { data: null, error };
    }
  }

  /**
   * Busca uma categoria por slug
   */
  async getCategoryBySlug(
    slug: string
  ): Promise<SupabaseResponse<SupabaseCategory>> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('slug', slug)
        .single();

      if (error) {
        console.error('Erro ao buscar categoria por slug:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar categoria por slug:', error);
      return { data: null, error };
    }
  }

  /**
   * Cria uma nova categoria
   */
  async createCategory(categoryData: {
    name: string;
    slug: string;
    description?: string;
    color?: string;
  }): Promise<SupabaseResponse<SupabaseCategory>> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert([
          {
            ...categoryData,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar categoria:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao criar categoria:', error);
      return { data: null, error };
    }
  }

  /**
   * Atualiza uma categoria existente
   */
  async updateCategory(
    id: string,
    categoryData: {
      name?: string;
      slug?: string;
      description?: string;
      color?: string;
    }
  ): Promise<SupabaseResponse<SupabaseCategory>> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .update(categoryData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar categoria:', error);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar categoria:', error);
      return { data: null, error };
    }
  }

  /**
   * Deleta uma categoria
   */
  async deleteCategory(id: string): Promise<SupabaseResponse<null>> {
    try {
      const { error } = await supabase.from('categories').delete().eq('id', id);

      if (error) {
        console.error('Erro ao deletar categoria:', error);
        return { data: null, error };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao deletar categoria:', error);
      return { data: null, error };
    }
  }

  /**
   * Conta posts por categoria
   */
  async getCategoryPostsCount(
    categoryId: string
  ): Promise<SupabaseResponse<number>> {
    try {
      const { count, error } = await supabase
        .from('post_categories')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', categoryId);

      if (error) {
        console.error('Erro ao contar posts da categoria:', error);
        return { data: null, error };
      }

      return { data: count || 0, error: null };
    } catch (error) {
      console.error('Erro inesperado ao contar posts da categoria:', error);
      return { data: null, error };
    }
  }
}

// Instâncias dos services
export const supabaseCategoryService = new SupabaseCategoryService();
