import type { Session, User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabaseClient';
import { ApiLogger } from '../utils/api-logger';

// ===== INTERFACES =====
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'admin'; // Sempre admin para usuário único
  created_at?: string;
  updated_at?: string;
  // Propriedades essenciais
  avatar: string;
  bio: string;
  lastLogin: string;
  createdAt: string; // Alias para created_at
  preferences: {
    theme: 'dark' | 'light' | 'auto';
    language: 'pt' | 'en' | 'es';
    notifications: {
      email: boolean;
      push: boolean;
      comments: boolean;
      mentions: boolean;
    };
  };
}

export interface AuthResult {
  user: AuthUser | null;
  error: string | null;
}

export interface SessionResult {
  session: Session | null;
  user: AuthUser | null;
  error: string | null;
}

// ===== SUPABASE AUTH SERVICE =====
export class SupabaseAuthService {
  /**
   * Fazer login com email e senha
   */
  async signIn(email: string, password: string): Promise<AuthResult> {
    ApiLogger.logRequest('supabase/auth/signin', 'POST', { email });
    const startTime = Date.now();

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      const duration = Date.now() - startTime;

      if (error) {
        ApiLogger.logResponse('supabase/auth/signin', 400, null, duration);
        ApiLogger.logError('supabase/auth/signin', new Error(error.message));
        return { user: null, error: this.getErrorMessage(error.message) };
      }

      if (data.user) {
        ApiLogger.logResponse('supabase/auth/signin', 200, null, duration);
        const authUser = this.mapSupabaseUserToAuthUser(data.user);
        return { user: authUser, error: null };
      }

      ApiLogger.logResponse('supabase/auth/signin', 500, null, duration);
      return { user: null, error: 'Erro inesperado no login' };
    } catch (error) {
      const duration = Date.now() - startTime;
      ApiLogger.logResponse('supabase/auth/signin', 500, null, duration);
      ApiLogger.logError('supabase/auth/signin', error as Error);
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Registrar novo usuário
   */
  async signUp(
    email: string,
    password: string,
    metadata: { name?: string } = {}
  ): Promise<AuthResult> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: metadata.name || '',
          },
        },
      });

      if (error) {
        console.error('Erro no registro:', error);
        return { user: null, error: this.getErrorMessage(error.message) };
      }

      if (data.user) {
        const authUser = this.mapSupabaseUserToAuthUser(data.user);
        return { user: authUser, error: null };
      }

      return { user: null, error: 'Erro inesperado no registro' };
    } catch (error) {
      console.error('Erro inesperado no registro:', error);
      return {
        user: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Fazer logout
   */
  async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Erro no logout:', error);
        return { error: this.getErrorMessage(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Erro inesperado no logout:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Obter sessão atual
   */
  async getSession(): Promise<SessionResult> {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        // Não logar erro se for apenas "Auth session missing" - é normal quando não logado
        if (!error.message.includes('Auth session missing')) {
          console.error('Erro ao obter sessão:', error);
        }
        return { session: null, user: null, error: null }; // Retornar null em vez de erro
      }

      if (session?.user) {
        const authUser = this.mapSupabaseUserToAuthUser(session.user);
        return { session, user: authUser, error: null };
      }

      return { session: null, user: null, error: null };
    } catch (error) {
      // Não logar erro se for relacionado a sessão ausente
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      if (
        !errorMessage.includes('Auth session missing') &&
        !errorMessage.includes('session missing')
      ) {
        console.error('Erro inesperado ao obter sessão:', error);
      }
      return {
        session: null,
        user: null,
        error: null, // Retornar null em vez de erro para não quebrar a aplicação
      };
    }
  }

  /**
   * Obter usuário atual
   */
  async getCurrentUser(): Promise<AuthResult> {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error) {
        // Não logar erro se for apenas "Auth session missing" - é normal quando não logado
        if (
          !error.message.includes('Auth session missing') &&
          !error.message.includes('session missing')
        ) {
          console.error('Erro ao obter usuário atual:', error);
        }
        return { user: null, error: null }; // Retornar null em vez de erro
      }

      if (user) {
        const authUser = this.mapSupabaseUserToAuthUser(user);
        return { user: authUser, error: null };
      }

      return { user: null, error: null };
    } catch (error) {
      // Não logar erro se for relacionado a sessão ausente
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      if (
        !errorMessage.includes('Auth session missing') &&
        !errorMessage.includes('session missing')
      ) {
        console.error('Erro inesperado ao obter usuário atual:', error);
      }
      return {
        user: null,
        error: null, // Retornar null em vez de erro para não quebrar a aplicação
      };
    }
  }

  /**
   * Resetar senha
   */
  async resetPassword(email: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('Erro ao resetar senha:', error);
        return { error: this.getErrorMessage(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Erro inesperado ao resetar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Atualizar senha
   */
  async updatePassword(newPassword: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        console.error('Erro ao atualizar senha:', error);
        return { error: this.getErrorMessage(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar senha:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Atualizar dados do usuário
   */
  async updateUser(
    userData: Partial<AuthUser>
  ): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          name: userData.name,
          avatar: userData.avatar,
          bio: userData.bio,
          theme: userData.preferences?.theme,
          language: userData.preferences?.language,
          notifications: userData.preferences?.notifications,
        },
      });

      if (error) {
        console.error('Erro ao atualizar usuário:', error);
        return { error: this.getErrorMessage(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar usuário:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Verificar se está autenticado
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      // Se houver erro relacionado a sessão ausente, é normal - retornar false
      if (
        error &&
        (error.message.includes('Auth session missing') ||
          error.message.includes('session missing'))
      ) {
        return false;
      }

      // Se houver outro tipo de erro, logar
      if (error) {
        console.error('Erro ao verificar autenticação:', error);
        return false;
      }

      return !!session?.user;
    } catch (error) {
      // Não logar erro se for relacionado a sessão ausente
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      if (
        !errorMessage.includes('Auth session missing') &&
        !errorMessage.includes('session missing')
      ) {
        console.error('Erro ao verificar autenticação:', error);
      }
      return false;
    }
  }

  /**
   * Escutar mudanças de autenticação
   */
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange((event, session) => {
      // Só logar eventos importantes (não INITIAL_SESSION)
      if (event !== 'INITIAL_SESSION') {
        console.log(
          '🔐 Auth state changed:',
          event,
          session?.user?.email || 'no user'
        );
      }

      if (session?.user) {
        const authUser = this.mapSupabaseUserToAuthUser(session.user);
        callback(authUser);
      } else {
        callback(null);
      }
    });
  }

  // ===== MÉTODOS PRIVADOS =====

  /**
   * Mapear usuário do Supabase para AuthUser
   */
  private mapSupabaseUserToAuthUser(user: User): AuthUser {
    const name =
      user.user_metadata?.name || user.email?.split('@')[0] || 'Admin';
    const theme =
      (user.user_metadata?.theme as 'dark' | 'light' | 'auto') || 'dark';
    const language =
      (user.user_metadata?.language as 'pt' | 'en' | 'es') || 'pt';

    return {
      id: user.id,
      email: user.email || '',
      name,
      role: 'admin', // Sempre admin para sistema de usuário único
      created_at: user.created_at,
      updated_at: user.updated_at,
      // Propriedades essenciais
      avatar: user.user_metadata?.avatar || 'blueprint-logo', // Usar logo Blueprint como padrão
      bio:
        user.user_metadata?.bio ||
        `Criador de conteúdo técnico e inovação digital`,
      lastLogin:
        user.last_sign_in_at || user.created_at || new Date().toISOString(),
      createdAt: user.created_at || new Date().toISOString(),
      preferences: {
        theme,
        language,
        notifications: {
          email: user.user_metadata?.notifications?.email ?? true,
          push: user.user_metadata?.notifications?.push ?? false,
          comments: user.user_metadata?.notifications?.comments ?? true,
          mentions: user.user_metadata?.notifications?.mentions ?? true,
        },
      },
    };
  }

  /**
   * Traduzir mensagens de erro do Supabase
   */
  private getErrorMessage(error: string): string {
    const errorMessages: Record<string, string> = {
      'Invalid login credentials': 'Email ou senha incorretos',
      'Email not confirmed':
        'Email não confirmado. Verifique sua caixa de entrada.',
      'User already registered': 'Usuário já cadastrado',
      'Password should be at least 6 characters':
        'A senha deve ter pelo menos 6 caracteres',
      'Unable to validate email address: invalid format':
        'Formato de email inválido',
      signup_disabled: 'Cadastro desabilitado',
      email_address_invalid: 'Endereço de email inválido',
      password_too_short: 'Senha muito curta',
    };

    return errorMessages[error] || error;
  }
}

// Instância do service
export const supabaseAuthService = new SupabaseAuthService();
