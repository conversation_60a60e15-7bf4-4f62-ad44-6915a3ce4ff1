import type { Tag } from '../hooks/useTags';
import { supabase } from '../lib/supabaseClient';

export interface CreateTagData {
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  is_active?: boolean;
}

export interface UpdateTagData extends Partial<CreateTagData> {
  id: string;
}

export interface TagsResponse<T> {
  data: T | null;
  error: string | null;
}

export class TagsService {
  /**
   * Busca todas as tags (incluindo inativas para admin)
   */
  async getAllTags(): Promise<TagsResponse<Tag[]>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        console.error('Erro ao buscar tags:', error);
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar tags:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Busca uma tag por ID
   */
  async getTagById(id: string): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Erro ao buscar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Cria uma nova tag
   */
  async createTag(tagData: CreateTagData): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert([
          {
            ...tagData,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao criar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Atualiza uma tag existente
   */
  async updateTag(tagData: UpdateTagData): Promise<TagsResponse<Tag>> {
    try {
      const { id, ...updateData } = tagData;

      const { data, error } = await supabase
        .from('tags')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Deleta uma tag (soft delete)
   */
  async deleteTag(id: string): Promise<TagsResponse<null>> {
    try {
      const { error } = await supabase
        .from('tags')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('Erro ao deletar tag:', error);
        return { data: null, error: error.message };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao deletar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Reativa uma tag
   */
  async reactivateTag(id: string): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .update({ is_active: true })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao reativar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao reativar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Busca estatísticas de uso das tags
   */
  async getTagStats(): Promise<
    TagsResponse<Array<{ tag_id: string; post_count: number }>>
  > {
    try {
      console.log('🔍 [TagsService] Iniciando busca de estatísticas...');

      // Primeiro, tentar buscar da tabela post_tags (sistema novo)
      const { data: relationData, error: relationError } = await supabase
        .from('post_tags')
        .select('tag_id');

      console.log('📊 [TagsService] Dados da tabela post_tags:', {
        count: relationData?.length || 0,
        error: relationError?.message,
        data: relationData,
      });

      if (!relationError && relationData && relationData.length > 0) {
        // Se há dados na tabela post_tags, usar o sistema novo
        console.log('✅ [TagsService] Usando sistema novo (post_tags)');

        const tagCounts = relationData.reduce(
          (acc: Record<string, number>, item) => {
            acc[item.tag_id] = (acc[item.tag_id] || 0) + 1;
            return acc;
          },
          {}
        );

        const stats = Object.entries(tagCounts).map(([tag_id, post_count]) => ({
          tag_id,
          post_count: post_count as number,
        }));

        console.log('📈 [TagsService] Estatísticas (sistema novo):', stats);
        return { data: stats, error: null };
      }

      // Se não há dados na post_tags, usar o campo tags dos posts (sistema legado)
      console.log('🔄 [TagsService] Usando sistema legado (campo tags)');

      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('id, tags')
        .eq('status', 'published')
        .not('tags', 'is', null);

      console.log('📝 [TagsService] Posts encontrados:', {
        count: posts?.length || 0,
        error: postsError?.message,
        sample: posts?.slice(0, 2),
      });

      if (postsError) {
        return { data: null, error: postsError.message };
      }

      // Buscar todas as tags para fazer o mapeamento
      const { data: allTags, error: tagsError } = await supabase
        .from('tags')
        .select('id, name, slug');

      console.log('🏷️ [TagsService] Tags disponíveis:', {
        count: allTags?.length || 0,
        error: tagsError?.message,
        tags: allTags?.map((t) => ({ name: t.name, slug: t.slug })),
      });

      if (tagsError) {
        return { data: null, error: tagsError.message };
      }

      // Contar posts por tag usando o campo tags
      const tagCounts: Record<string, number> = {};

      posts?.forEach((post) => {
        if (!post.tags) return;

        let postTags: string[] = [];

        try {
          // Se já é um array, usar diretamente
          if (Array.isArray(post.tags)) {
            postTags = post.tags;
          }
          // Se é string, tentar parse como JSON primeiro
          else if (typeof post.tags === 'string') {
            try {
              const parsed = JSON.parse(post.tags);
              postTags = Array.isArray(parsed) ? parsed : [parsed];
            } catch {
              // Se falhar, assumir formato separado por vírgula
              postTags = post.tags
                .split(',')
                .map((tag: string) => tag.trim())
                .filter(Boolean);
            }
          }
        } catch (error) {
          console.warn('Erro ao processar tags do post:', post.id, error);
          return;
        }

        postTags.forEach((tagName) => {
          if (!tagName || typeof tagName !== 'string') return;

          // Encontrar a tag correspondente
          const matchingTag = allTags?.find(
            (tag) =>
              tag.name.toLowerCase() === tagName.toLowerCase() ||
              tag.slug.toLowerCase() ===
                tagName.toLowerCase().replace(/\s+/g, '-')
          );

          if (matchingTag) {
            tagCounts[matchingTag.id] = (tagCounts[matchingTag.id] || 0) + 1;
          }
        });
      });

      const stats = Object.entries(tagCounts).map(([tag_id, post_count]) => ({
        tag_id,
        post_count: post_count as number,
      }));

      console.log('📈 [TagsService] Estatísticas finais (sistema legado):', {
        totalStats: stats.length,
        stats: stats,
      });

      return { data: stats, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar estatísticas:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Verifica se um slug já existe
   */
  async checkSlugExists(
    slug: string,
    excludeId?: string
  ): Promise<TagsResponse<boolean>> {
    try {
      let query = supabase.from('tags').select('id').eq('slug', slug);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Erro ao verificar slug:', error);
        return { data: null, error: error.message };
      }

      return { data: (data?.length || 0) > 0, error: null };
    } catch (error) {
      console.error('Erro inesperado ao verificar slug:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }
}

// Instância singleton
export const tagsService = new TagsService();
