/**
 * @fileoverview Serviço para operações CRUD de tags no Supabase
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { supabase } from '../lib/supabaseClient';
import type { Tag } from '../hooks/useTags';

// ============================================================================
// INTERFACES
// ============================================================================

export interface CreateTagData {
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
}

export interface UpdateTagData extends Partial<CreateTagData> {
  is_active?: boolean;
}

export interface TagWithPostsCount extends Tag {
  postsCount: number;
}

// ============================================================================
// SERVIÇO PRINCIPAL
// ============================================================================

export class SupabaseTagsService {
  /**
   * Lista todas as tags com contagem de posts
   */
  static async getAllTags(): Promise<{ data: TagWithPostsCount[] | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select(`
          *,
          post_tags(count)
        `)
        .order('name', { ascending: true });

      if (error) {
        return { data: null, error: error.message };
      }

      // Transformar dados para incluir contagem de posts
      const tagsWithCount: TagWithPostsCount[] = (data || []).map(tag => ({
        ...tag,
        postsCount: tag.post_tags?.[0]?.count || 0
      }));

      return { data: tagsWithCount, error: null };

    } catch (error) {
      console.error('Erro ao buscar tags:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Busca tag por ID
   */
  static async getTagById(id: string): Promise<{ data: Tag | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };

    } catch (error) {
      console.error('Erro ao buscar tag:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Cria nova tag
   */
  static async createTag(tagData: CreateTagData): Promise<{ data: Tag | null; error: string | null }> {
    try {
      // Verificar se slug já existe
      const { data: existingTag } = await supabase
        .from('tags')
        .select('id')
        .eq('slug', tagData.slug)
        .single();

      if (existingTag) {
        return { data: null, error: 'Já existe uma tag com este slug' };
      }

      const { data, error } = await supabase
        .from('tags')
        .insert([{
          ...tagData,
          is_active: true,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };

    } catch (error) {
      console.error('Erro ao criar tag:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Atualiza tag existente
   */
  static async updateTag(id: string, tagData: UpdateTagData): Promise<{ data: Tag | null; error: string | null }> {
    try {
      // Se está atualizando slug, verificar se não existe outro com mesmo slug
      if (tagData.slug) {
        const { data: existingTag } = await supabase
          .from('tags')
          .select('id')
          .eq('slug', tagData.slug)
          .neq('id', id)
          .single();

        if (existingTag) {
          return { data: null, error: 'Já existe uma tag com este slug' };
        }
      }

      const { data, error } = await supabase
        .from('tags')
        .update(tagData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };

    } catch (error) {
      console.error('Erro ao atualizar tag:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Deleta tag (apenas se não estiver sendo usada)
   */
  static async deleteTag(id: string): Promise<{ error: string | null }> {
    try {
      // Verificar se tag está sendo usada em posts
      const { data: postTags, error: checkError } = await supabase
        .from('post_tags')
        .select('id')
        .eq('tag_id', id)
        .limit(1);

      if (checkError) {
        return { error: checkError.message };
      }

      if (postTags && postTags.length > 0) {
        return { error: 'Não é possível deletar tag que está sendo usada em posts' };
      }

      const { error } = await supabase
        .from('tags')
        .delete()
        .eq('id', id);

      if (error) {
        return { error: error.message };
      }

      return { error: null };

    } catch (error) {
      console.error('Erro ao deletar tag:', error);
      return { 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Desativa tag (soft delete)
   */
  static async deactivateTag(id: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase
        .from('tags')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        return { error: error.message };
      }

      return { error: null };

    } catch (error) {
      console.error('Erro ao desativar tag:', error);
      return { 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Busca tags por termo
   */
  static async searchTags(searchTerm: string): Promise<{ data: TagWithPostsCount[] | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select(`
          *,
          post_tags(count)
        `)
        .or(`name.ilike.%${searchTerm}%,slug.ilike.%${searchTerm}%`)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        return { data: null, error: error.message };
      }

      // Transformar dados para incluir contagem de posts
      const tagsWithCount: TagWithPostsCount[] = (data || []).map(tag => ({
        ...tag,
        postsCount: tag.post_tags?.[0]?.count || 0
      }));

      return { data: tagsWithCount, error: null };

    } catch (error) {
      console.error('Erro ao buscar tags:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      };
    }
  }

  /**
   * Gera slug único baseado no nome
   */
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }
}

export default SupabaseTagsService;
