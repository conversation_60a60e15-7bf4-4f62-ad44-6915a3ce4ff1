import { supabase } from '../lib/supabaseClient';

// ===== INTERFACES =====
interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?:
    | 'always'
    | 'hourly'
    | 'daily'
    | 'weekly'
    | 'monthly'
    | 'yearly'
    | 'never';
  priority?: number;
}

interface SitemapData {
  urls: SitemapUrl[];
  lastGenerated: string;
}

// ===== CONFIGURAÇÕES =====
const SITE_URL = 'https://blueprintblog.tech'; // Substitua pela URL real do seu site
const STATIC_PAGES: SitemapUrl[] = [
  {
    loc: '/',
    changefreq: 'weekly',
    priority: 1.0,
  },
  {
    loc: '/blog',
    changefreq: 'daily',
    priority: 0.9,
  },
  {
    loc: '/projects',
    changefreq: 'monthly',
    priority: 0.8,
  },
  {
    loc: '/devlog',
    changefreq: 'weekly',
    priority: 0.8,
  },
  {
    loc: '/about',
    changefreq: 'monthly',
    priority: 0.7,
  },
  {
    loc: '/contact',
    changefreq: 'monthly',
    priority: 0.6,
  },
];

// ===== FUNÇÕES UTILITÁRIAS =====
const formatDate = (date: string | Date): string => {
  return new Date(date).toISOString().split('T')[0];
};

const buildFullUrl = (path: string): string => {
  return `${SITE_URL}${path}`;
};

// ===== BUSCAR POSTS DINÂMICOS =====
const fetchPublishedPosts = async (): Promise<SitemapUrl[]> => {
  try {
    const { data: posts, error } = await supabase
      .from('posts')
      .select('slug, updated_at, published_at, language')
      .eq('status', 'published')
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('❌ [Sitemap] Erro ao buscar posts:', error);
      return [];
    }

    return (posts || []).map((post) => ({
      loc: `/blog/${post.slug}`,
      lastmod: formatDate(post.updated_at || post.published_at),
      changefreq: 'monthly' as const,
      priority: 0.8,
    }));
  } catch (error) {
    console.error('❌ [Sitemap] Erro inesperado ao buscar posts:', error);
    return [];
  }
};

// ===== GERAR DADOS DO SITEMAP =====
export const generateSitemapData = async (): Promise<SitemapData> => {
  try {
    console.log('🗺️ [Sitemap] Gerando dados do sitemap...');

    // Buscar posts dinâmicos
    const dynamicPosts = await fetchPublishedPosts();

    // Combinar páginas estáticas com posts dinâmicos
    const allUrls: SitemapUrl[] = [
      ...STATIC_PAGES.map((page) => ({
        ...page,
        loc: buildFullUrl(page.loc),
        lastmod: formatDate(new Date()),
      })),
      ...dynamicPosts.map((post) => ({
        ...post,
        loc: buildFullUrl(post.loc),
      })),
    ];

    console.log(`✅ [Sitemap] ${allUrls.length} URLs geradas`);

    return {
      urls: allUrls,
      lastGenerated: new Date().toISOString(),
    };
  } catch (error) {
    console.error('❌ [Sitemap] Erro ao gerar dados:', error);
    return {
      urls: STATIC_PAGES.map((page) => ({
        ...page,
        loc: buildFullUrl(page.loc),
        lastmod: formatDate(new Date()),
      })),
      lastGenerated: new Date().toISOString(),
    };
  }
};

// ===== GERAR XML DO SITEMAP =====
export const generateSitemapXML = async (): Promise<string> => {
  try {
    const sitemapData = await generateSitemapData();

    const xmlUrls = sitemapData.urls
      .map((url) => {
        let xmlUrl = `  <url>\n    <loc>${url.loc}</loc>`;

        if (url.lastmod) {
          xmlUrl += `\n    <lastmod>${url.lastmod}</lastmod>`;
        }

        if (url.changefreq) {
          xmlUrl += `\n    <changefreq>${url.changefreq}</changefreq>`;
        }

        if (url.priority !== undefined) {
          xmlUrl += `\n    <priority>${url.priority.toFixed(1)}</priority>`;
        }

        xmlUrl += '\n  </url>';
        return xmlUrl;
      })
      .join('\n');

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${xmlUrls}
</urlset>`;

    console.log('✅ [Sitemap] XML gerado com sucesso');
    return xml;
  } catch (error) {
    console.error('❌ [Sitemap] Erro ao gerar XML:', error);

    // Fallback: sitemap mínimo com páginas estáticas
    const fallbackXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${SITE_URL}/</loc>
    <lastmod>${formatDate(new Date())}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${SITE_URL}/blog</loc>
    <lastmod>${formatDate(new Date())}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>`;

    return fallbackXml;
  }
};

// ===== CACHE SIMPLES =====
let cachedSitemap: { xml: string; timestamp: number } | null = null;
const CACHE_DURATION = 1000 * 60 * 60; // 1 hora

export const getCachedSitemapXML = async (): Promise<string> => {
  const now = Date.now();

  // Verificar se cache é válido
  if (cachedSitemap && now - cachedSitemap.timestamp < CACHE_DURATION) {
    console.log('📋 [Sitemap] Usando cache');
    return cachedSitemap.xml;
  }

  // Gerar novo sitemap
  console.log('🔄 [Sitemap] Gerando novo sitemap');
  const xml = await generateSitemapXML();

  // Atualizar cache
  cachedSitemap = {
    xml,
    timestamp: now,
  };

  return xml;
};

// ===== EXPORTS =====
export type { SitemapData, SitemapUrl };
