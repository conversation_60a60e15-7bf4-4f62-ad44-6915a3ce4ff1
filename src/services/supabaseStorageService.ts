/**
 * @fileoverview Serviço para gerenciar uploads e mídia no Supabase Storage
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { supabase } from '../lib/supabaseClient';

// ============================================================================
// INTERFACES
// ============================================================================

export interface UploadResult {
  data: {
    url: string;
    path: string;
    fullPath: string;
  } | null;
  error: string | null;
}

export interface MediaFile {
  id: string;
  name: string;
  path: string;
  url: string;
  size: number;
  type: string;
  folder: string;
  createdAt: string;
  updatedAt: string;
}

export interface UploadOptions {
  folder?: 'posts' | 'thumbnails' | 'avatars' | 'categories' | 'temp';
  maxSize?: number; // em bytes
  allowedTypes?: string[];
  generateThumbnail?: boolean;
}

// ============================================================================
// CONSTANTES
// ============================================================================

const BUCKET_NAME = 'media';
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

// ============================================================================
// SERVIÇO PRINCIPAL
// ============================================================================

export class SupabaseStorageService {
  /**
   * Faz upload de um arquivo para o Supabase Storage
   */
  async uploadFile(
    file: File, 
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      const {
        folder = 'temp',
        maxSize = MAX_FILE_SIZE,
        allowedTypes = ALLOWED_IMAGE_TYPES,
        generateThumbnail = false
      } = options;

      // Validações
      const validation = this.validateFile(file, maxSize, allowedTypes);
      if (validation.error) {
        return { data: null, error: validation.error };
      }

      // Gerar nome único para o arquivo
      const fileName = this.generateFileName(file.name);
      const filePath = `${folder}/${fileName}`;

      // Upload para o Supabase Storage
      const { data, error } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Erro no upload:', error);
        return { 
          data: null, 
          error: `Erro no upload: ${error.message}` 
        };
      }

      // Obter URL pública
      const { data: urlData } = supabase.storage
        .from(BUCKET_NAME)
        .getPublicUrl(filePath);

      if (!urlData?.publicUrl) {
        return { 
          data: null, 
          error: 'Erro ao obter URL pública do arquivo' 
        };
      }

      // Gerar thumbnail se solicitado
      if (generateThumbnail && file.type.startsWith('image/')) {
        await this.generateThumbnail(file, filePath);
      }

      return {
        data: {
          url: urlData.publicUrl,
          path: filePath,
          fullPath: data.path
        },
        error: null
      };

    } catch (error) {
      console.error('Erro inesperado no upload:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Lista arquivos de uma pasta
   */
  async listFiles(folder: string = ''): Promise<{
    data: MediaFile[] | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase.storage
        .from(BUCKET_NAME)
        .list(folder, {
          limit: 100,
          offset: 0,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        return { data: null, error: error.message };
      }

      const files: MediaFile[] = (data || []).map(file => ({
        id: file.id || file.name,
        name: file.name,
        path: folder ? `${folder}/${file.name}` : file.name,
        url: this.getPublicUrl(folder ? `${folder}/${file.name}` : file.name),
        size: file.metadata?.size || 0,
        type: file.metadata?.mimetype || 'unknown',
        folder: folder,
        createdAt: file.created_at || new Date().toISOString(),
        updatedAt: file.updated_at || new Date().toISOString(),
      }));

      return { data: files, error: null };

    } catch (error) {
      console.error('Erro ao listar arquivos:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Deleta um arquivo
   */
  async deleteFile(filePath: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.storage
        .from(BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        return { error: error.message };
      }

      return { error: null };

    } catch (error) {
      console.error('Erro ao deletar arquivo:', error);
      return {
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Obtém URL pública de um arquivo
   */
  getPublicUrl(filePath: string): string {
    const { data } = supabase.storage
      .from(BUCKET_NAME)
      .getPublicUrl(filePath);
    
    return data?.publicUrl || '';
  }

  /**
   * Valida arquivo antes do upload
   */
  private validateFile(
    file: File, 
    maxSize: number, 
    allowedTypes: string[]
  ): { error: string | null } {
    // Verificar tamanho
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return { 
        error: `Arquivo muito grande. Tamanho máximo: ${maxSizeMB}MB` 
      };
    }

    // Verificar tipo
    if (!allowedTypes.includes(file.type)) {
      return { 
        error: `Tipo de arquivo não permitido. Tipos aceitos: ${allowedTypes.join(', ')}` 
      };
    }

    return { error: null };
  }

  /**
   * Gera nome único para arquivo
   */
  private generateFileName(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split('.').pop();
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    const sanitizedName = nameWithoutExt
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .substring(0, 20);
    
    return `${sanitizedName}-${timestamp}-${random}.${extension}`;
  }

  /**
   * Gera thumbnail (implementação básica)
   */
  private async generateThumbnail(file: File, originalPath: string): Promise<void> {
    try {
      // Implementação básica - pode ser expandida com canvas/sharp
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      return new Promise((resolve) => {
        img.onload = async () => {
          // Redimensionar para thumbnail (300x200)
          canvas.width = 300;
          canvas.height = 200;
          
          if (ctx) {
            ctx.drawImage(img, 0, 0, 300, 200);
            
            canvas.toBlob(async (blob) => {
              if (blob) {
                const thumbnailPath = originalPath.replace(/^([^/]+)\//, '$1/thumbnails/');
                await supabase.storage
                  .from(BUCKET_NAME)
                  .upload(thumbnailPath, blob);
              }
              resolve();
            }, 'image/jpeg', 0.8);
          } else {
            resolve();
          }
        };
        
        img.src = URL.createObjectURL(file);
      });
    } catch (error) {
      console.error('Erro ao gerar thumbnail:', error);
    }
  }
}

// ============================================================================
// INSTÂNCIA SINGLETON
// ============================================================================

export const supabaseStorageService = new SupabaseStorageService();
export default supabaseStorageService;
