import { supabase } from '../lib/supabaseClient';

export interface PostMetrics {
  post_id: string;
  views: number;
  likes: number;
  shares: number;
  reading_time_avg: number;
  last_viewed: string;
}

export interface PopularPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  featured_image?: string;
  author_id?: string;
  tags?: string[];
  published_at: string;
  views: number;
  reading_time?: number;
}

/**
 * Serviço para analytics e métricas de posts
 */
export class AnalyticsService {
  /**
   * Incrementa o contador de visualizações de um post
   */
  static async incrementPostView(
    postId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Primeiro busca o valor atual
      const { data: currentPost, error: fetchError } = await supabase
        .from('posts')
        .select('views')
        .eq('id', postId)
        .single();

      if (fetchError) {
        console.error('Erro ao buscar post atual:', fetchError);
        return { success: false, error: fetchError.message };
      }

      // Incrementa o valor
      const newViews = (currentPost?.views || 0) + 1;

      // Atualiza com o novo valor
      const { error } = await supabase
        .from('posts')
        .update({ views: newViews })
        .eq('id', postId);

      if (error) {
        console.error('Erro ao incrementar visualização:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Erro ao incrementar visualização:', error);
      return { success: false, error: 'Erro interno' };
    }
  }

  /**
   * Incrementa o contador de visualizações por slug
   */
  static async incrementPostViewBySlug(
    slug: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Primeiro busca o valor atual
      const { data: currentPost, error: fetchError } = await supabase
        .from('posts')
        .select('views')
        .eq('slug', slug)
        .single();

      if (fetchError) {
        console.error('Erro ao buscar post atual por slug:', fetchError);
        return { success: false, error: fetchError.message };
      }

      // Incrementa o valor
      const newViews = (currentPost?.views || 0) + 1;

      // Atualiza com o novo valor
      const { error } = await supabase
        .from('posts')
        .update({ views: newViews })
        .eq('slug', slug);

      if (error) {
        console.error('Erro ao incrementar visualização por slug:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Erro ao incrementar visualização por slug:', error);
      return { success: false, error: 'Erro interno' };
    }
  }

  /**
   * Busca os posts mais populares baseado em visualizações
   */
  static async getPopularPosts(
    limit: number = 5
  ): Promise<{ data: PopularPost[] | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(
          `
          id,
          title,
          slug,
          excerpt,
          featured_image,
          author_id,
          tags,
          published_at,
          views,
          reading_time
        `
        )
        .eq('status', 'published')
        .not('views', 'is', null)
        .gt('views', 0)
        .order('views', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Erro ao buscar posts populares:', error);
        return { data: null, error: error.message };
      }

      // Processar tags se necessário
      const processedData =
        data?.map((post) => ({
          ...post,
          tags:
            typeof post.tags === 'string'
              ? post.tags.split(',').map((tag) => tag.trim())
              : post.tags || [],
          views: post.views || 0,
        })) || [];

      return { data: processedData, error: null };
    } catch (error) {
      console.error('Erro ao buscar posts populares:', error);
      return { data: null, error: 'Erro interno' };
    }
  }

  /**
   * Busca métricas de um post específico
   */
  static async getPostMetrics(
    postId: string
  ): Promise<{ data: PostMetrics | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select('id, views, likes, updated_at')
        .eq('id', postId)
        .single();

      if (error) {
        console.error('Erro ao buscar métricas do post:', error);
        return { data: null, error: error.message };
      }

      const metrics: PostMetrics = {
        post_id: data.id,
        views: data.views || 0,
        likes: data.likes || 0,
        shares: 0, // TODO: implementar tracking de shares
        reading_time_avg: 0, // TODO: implementar tracking de tempo de leitura
        last_viewed: data.updated_at,
      };

      return { data: metrics, error: null };
    } catch (error) {
      console.error('Erro ao buscar métricas do post:', error);
      return { data: null, error: 'Erro interno' };
    }
  }

  /**
   * Busca estatísticas gerais do blog
   */
  static async getBlogStats(): Promise<{
    data: {
      totalPosts: number;
      totalViews: number;
      avgViewsPerPost: number;
      mostPopularPost?: PopularPost;
    } | null;
    error: string | null;
  }> {
    try {
      // Buscar estatísticas gerais
      const { data: stats, error: statsError } = await supabase
        .from('posts')
        .select('views')
        .eq('status', 'published');

      if (statsError) {
        return { data: null, error: statsError.message };
      }

      // Buscar post mais popular
      const { data: popularPosts } = await this.getPopularPosts(1);

      const totalPosts = stats.length;
      const totalViews = stats.reduce(
        (sum, post) => sum + (post.views || 0),
        0
      );
      const avgViewsPerPost =
        totalPosts > 0 ? Math.round(totalViews / totalPosts) : 0;

      return {
        data: {
          totalPosts,
          totalViews,
          avgViewsPerPost,
          mostPopularPost: popularPosts?.[0],
        },
        error: null,
      };
    } catch (error) {
      console.error('Erro ao buscar estatísticas do blog:', error);
      return { data: null, error: 'Erro interno' };
    }
  }
}

export default AnalyticsService;
