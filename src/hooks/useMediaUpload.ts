/**
 * @fileoverview Hook para gerenciar uploads de mídia
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useState } from 'react';
import {
  supabaseStorageService,
  type MediaFile,
  type UploadOptions,
} from '../services/supabaseStorageService';

// ============================================================================
// INTERFACES
// ============================================================================

interface UploadState {
  uploading: boolean;
  progress: number;
  error: string | null;
  uploadedFile: {
    url: string;
    path: string;
  } | null;
}

interface MediaState {
  files: MediaFile[];
  loading: boolean;
  error: string | null;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

export const useMediaUpload = () => {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    error: null,
    uploadedFile: null,
  });

  const [mediaState, setMediaState] = useState<MediaState>({
    files: [],
    loading: false,
    error: null,
  });

  /**
   * Faz upload de um arquivo
   */
  const uploadFile = async (file: File, options?: UploadOptions) => {
    setUploadState({
      uploading: true,
      progress: 0,
      error: null,
      uploadedFile: null,
    });

    try {
      // Simular progresso mais realista
      const progressInterval = setInterval(() => {
        setUploadState((prev) => ({
          ...prev,
          progress: Math.min(prev.progress + Math.random() * 15 + 5, 85),
        }));
      }, 150);

      const result = await supabaseStorageService.uploadFile(file, options);

      clearInterval(progressInterval);

      if (result.error) {
        setUploadState({
          uploading: false,
          progress: 0,
          error: result.error,
          uploadedFile: null,
        });
        return { success: false, error: result.error };
      }

      // Progresso completo
      setUploadState({
        uploading: false,
        progress: 100,
        error: null,
        uploadedFile: result.data,
      });

      // Aguardar um pouco para garantir que o arquivo foi processado
      await new Promise((resolve) => setTimeout(resolve, 500));

      return { success: true, data: result.data };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      setUploadState({
        uploading: false,
        progress: 0,
        error: errorMessage,
        uploadedFile: null,
      });
      return { success: false, error: errorMessage };
    }
  };

  /**
   * Lista arquivos de uma pasta
   */
  const listFiles = async (folder: string = '') => {
    setMediaState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await supabaseStorageService.listFiles(folder);

      if (result.error) {
        setMediaState({
          files: [],
          loading: false,
          error: result.error,
        });
        return { success: false, error: result.error };
      }

      setMediaState({
        files: result.data || [],
        loading: false,
        error: null,
      });

      return { success: true, data: result.data };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      setMediaState({
        files: [],
        loading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  /**
   * Deleta um arquivo
   */
  const deleteFile = async (filePath: string) => {
    try {
      const result = await supabaseStorageService.deleteFile(filePath);

      if (result.error) {
        return { success: false, error: result.error };
      }

      // Atualizar lista local removendo o arquivo
      setMediaState((prev) => ({
        ...prev,
        files: prev.files.filter((file) => file.path !== filePath),
      }));

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Erro desconhecido';
      return { success: false, error: errorMessage };
    }
  };

  /**
   * Obtém URL pública de um arquivo
   */
  const getPublicUrl = (filePath: string) => {
    return supabaseStorageService.getPublicUrl(filePath);
  };

  /**
   * Reset do estado de upload
   */
  const resetUpload = () => {
    setUploadState({
      uploading: false,
      progress: 0,
      error: null,
      uploadedFile: null,
    });
  };

  /**
   * Reset do estado de mídia
   */
  const resetMedia = () => {
    setMediaState({
      files: [],
      loading: false,
      error: null,
    });
  };

  return {
    // Estados
    uploading: uploadState.uploading,
    uploadProgress: uploadState.progress,
    uploadError: uploadState.error,
    uploadedFile: uploadState.uploadedFile,

    files: mediaState.files,
    loadingFiles: mediaState.loading,
    filesError: mediaState.error,

    // Funções
    uploadFile,
    listFiles,
    deleteFile,
    getPublicUrl,
    resetUpload,
    resetMedia,
  };
};

export default useMediaUpload;
