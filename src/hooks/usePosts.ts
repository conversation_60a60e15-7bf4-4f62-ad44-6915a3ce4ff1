import { useCallback, useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';
import type { Post } from '../types';
import { ComponentLogger } from '../utils/component-logger';
import { logger } from '../utils/logger';

// ===== FUNÇÕES UTILITÁRIAS =====

/**
 * Carrega tags de um post usando estrutura normalizada (post_tags)
 */
const loadPostTags = async (postId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('post_tags')
      .select(
        `
        tags (
          id,
          name,
          slug,
          color,
          description
        )
      `
      )
      .eq('post_id', postId);

    if (error) {
      console.error('Erro ao carregar tags do post:', error);
      return [];
    }

    return (data || []).map((item: any) => ({
      id: item.tags.id,
      name: item.tags.name,
      slug: item.tags.slug,
      color: item.tags.color || '#3B82F6',
      postsCount: 0, // TODO: Implementar contagem se necessário
    }));
  } catch (error) {
    console.error('Erro inesperado ao carregar tags:', error);
    return [];
  }
};

/**
 * Carrega categorias de um post usando estrutura normalizada (post_categories)
 */
const loadPostCategories = async (postId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('post_categories')
      .select(
        `
        categories (
          id,
          name,
          slug,
          color,
          description
        )
      `
      )
      .eq('post_id', postId);

    if (error) {
      console.error('Erro ao carregar categorias do post:', error);
      return [];
    }

    return (data || []).map((item: any) => ({
      id: item.categories.id,
      name: item.categories.name,
      slug: item.categories.slug,
      description: item.categories.description || '',
      color: item.categories.color || '#6B7280',
      postsCount: 0, // TODO: Implementar contagem se necessário
    }));
  } catch (error) {
    console.error('Erro inesperado ao carregar categorias:', error);
    return [];
  }
};
// TODO: Adicionar tipo PostsQuery quando necessário

// ===== INTERFACES =====
// TODO: Mover para types quando PostsQuery estiver disponível
interface PostsQuery {
  status?: string;
  language?: string;
  limit?: number | null;
  category?: string;
  tag?: string;
  search?: string;
}

interface UsePostsOptions extends PostsQuery {
  orderBy?: 'published_at' | 'created_at' | 'updated_at' | 'views' | 'title';
  orderDirection?: 'asc' | 'desc';
}

interface UsePostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
  createPost: (
    postData: any
  ) => Promise<{ data: Post | null; error: string | null }>;
  updatePost: (
    id: string,
    postData: any
  ) => Promise<{ data: Post | null; error: string | null }>;
  deletePost: (id: string) => Promise<{ error: string | null }>;
  getPostBySlug: (
    slug: string
  ) => Promise<{ data: Post | null; error: string | null }>;
  refetch: () => Promise<void>;
}

interface UseFeaturedPostsReturn {
  posts: Post[];
  loading: boolean;
  error: string | null;
}

// ===== HOOK PRINCIPAL =====
export const usePosts = (options: UsePostsOptions = {}): UsePostsReturn => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Log do hook mount
  ComponentLogger.logMount('usePosts', { options });

  const {
    status,
    language, // ✅ Sem valor padrão - permite undefined para mostrar todos os idiomas
    limit = null,
    orderBy = 'updated_at',
    orderDirection = 'desc',
  } = options;

  useEffect(() => {
    fetchPosts();
  }, [status, language, limit, orderBy, orderDirection]);

  const fetchPosts = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      logger.info('Iniciando busca de posts', { options });

      // Buscar posts do Supabase (mesma abordagem do v1 - sem JOIN)
      let query = supabase.from('posts').select('*');

      // Filtrar por status se especificado
      if (status) {
        query = query.eq('status', status);
      }
      // Se não especificar status, mostra todos os posts (draft + published)

      // Ordenação
      if (orderBy) {
        query = query.order(orderBy, { ascending: orderDirection === 'asc' });
      }

      // Limite
      if (limit) {
        query = query.limit(limit);
      }

      const { data: postsData, error: postsError } = await query;

      if (postsError) {
        logger.error('Erro ao buscar posts do Supabase', {
          error: postsError.message,
          options,
        });
        throw new Error(postsError.message || 'Erro ao buscar posts');
      }

      logger.debug('Posts encontrados no Supabase', {
        count: postsData?.length || 0,
        options,
      });

      // Buscar profiles e categories separadamente para fazer JOIN manual
      const { data: profilesData } = await supabase
        .from('profiles')
        .select('*');

      const { data: categoriesData } = await supabase
        .from('categories')
        .select('*');

      // Mapear dados do Supabase para o formato esperado pelo frontend v2
      const posts = (await Promise.all(
        (postsData || []).map(async (post: any) => {
          // Buscar autor e categoria pelos IDs
          const author = profilesData?.find((p) => p.id === post.author_id);
          const category = categoriesData?.find(
            (c) => c.id === post.category_id
          );

          // Carregar tags e categorias usando estrutura normalizada
          const tags = await loadPostTags(post.id);
          const categories = await loadPostCategories(post.id);

          // Usar categoria principal (category_id) ou primeira das categorias normalizadas
          const primaryCategory = category ||
            categories[0] || {
              id: 'default',
              name: 'Geral',
              slug: 'geral',
              description: '',
              color: '#00ffff',
              postsCount: 0,
            };

          console.log('🔄 [usePosts] Processando post:', {
            title: post.title,
            author: author?.name || 'Sem autor',
            category: primaryCategory.name,
            tagsCount: tags.length,
          });

          return {
            id: post.id,
            title: post.title,
            slug: post.slug,
            content: post.content,
            excerpt: post.excerpt || '',
            // Compatibilidade v1/v2: thumbnail ou featured_image
            thumbnail:
              post.thumbnail ||
              post.featured_image ||
              'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
            featured: post.featured || false,
            published: post.status === 'published',
            // Compatibilidade v1/v2: reading_time ou read_time
            readTime: post.reading_time || post.read_time || 5,
            views: post.views || 0,
            likes: post.likes || 0,
            date: post.published_at || post.created_at,
            createdAt: post.created_at,
            updatedAt: post.updated_at,
            // Mapear autor usando JOIN manual
            author: {
              id:
                author?.id ||
                post.author_id ||
                '00000000-0000-0000-0000-000000000001',
              name: author?.name || 'Blueprint',
              email: author?.email || '<EMAIL>',
              avatar: 'blueprint-logo', // Identificador especial para usar o componente BlueprintAvatar
              bio:
                author?.bio || 'Criador de conteúdo técnico e inovação digital',
              role: (author?.role as any) || 'admin',
              socialLinks: {},
              postsCount: 0,
              joinedAt: author?.created_at || post.created_at,
            },
            // Usar categoria principal
            category: primaryCategory,
            // Usar tags da estrutura normalizada
            tags: tags,
            // SEO padrão
            seo: {
              title: post.title,
              description: post.excerpt || '',
              keywords: '',
              ogImage: post.thumbnail || '',
              ogTitle: post.title,
              ogDescription: post.excerpt || '',
              twitterCard: 'summary_large_image' as const,
            },
          };
        })
      )) as Post[];

      console.log('✅ [usePosts] Posts processados:', {
        total: posts.length,
        posts: posts.map((p) => ({
          title: p.title,
          author: p.author.name,
          category: p.category.name,
        })),
      });

      setPosts(posts);
    } catch (err) {
      console.error('Erro ao buscar posts:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const createPost = async (
    postData: any
  ): Promise<{ data: Post | null; error: string | null }> => {
    try {
      logger.info('Iniciando criação de post', {
        title: postData.title,
        status: postData.status,
      });

      // Verificar autenticação
      const {
        data: { session },
        error: authError,
      } = await supabase.auth.getSession();

      if (authError || !session) {
        return { data: null, error: 'Usuário não autenticado' };
      }

      const { data, error } = await supabase
        .from('posts')
        .insert([
          {
            ...postData,
            author_id: session.user.id, // ✅ SEMPRE incluir author_id
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        logger.error('Erro ao criar post no Supabase', {
          error: error.message,
          postData: { title: postData.title, status: postData.status },
        });
        return { data: null, error: error.message || 'Erro ao criar post' };
      }

      // Adicionar o novo post ao estado local
      if (data) {
        const newPost = data as Post;
        setPosts((prev) => [newPost, ...prev]);

        logger.info('Post criado com sucesso', {
          postId: newPost.id,
          title: newPost.title,
        });

        return { data: newPost, error: null };
      }

      return { data: null, error: 'Erro ao criar post' };
    } catch (err) {
      logger.error('Erro inesperado ao criar post', {
        error: err instanceof Error ? err.message : 'Erro desconhecido',
        postData: { title: postData.title, status: postData.status },
      });
      return {
        data: null,
        error: err instanceof Error ? err.message : 'Erro desconhecido',
      };
    }
  };

  const updatePost = async (
    id: string,
    postData: any
  ): Promise<{ data: Post | null; error: string | null }> => {
    try {
      logger.info('Iniciando atualização de post', {
        postId: id,
        title: postData.title,
        status: postData.status,
      });

      // Preparar dados para update
      const updateData: any = { updated_at: new Date().toISOString() };

      // Adicionar apenas campos definidos
      if (postData.title !== undefined) updateData.title = postData.title;
      if (postData.content !== undefined) updateData.content = postData.content;
      if (postData.excerpt !== undefined) updateData.excerpt = postData.excerpt;
      if (postData.status !== undefined) updateData.status = postData.status;
      if (postData.language !== undefined)
        updateData.language = postData.language;
      if (postData.published_at !== undefined)
        updateData.published_at = postData.published_at;
      if (postData.tags !== undefined) {
        updateData.tags = Array.isArray(postData.tags) ? postData.tags : [];
      }

      // Executar update
      const { data, error } = await supabase
        .from('posts')
        .update(updateData)
        .eq('id', id)
        .select()
        .maybeSingle();

      if (error) {
        logger.error('Erro ao atualizar post no Supabase', {
          error: error.message,
          postId: id,
          postData: { title: postData.title, status: postData.status },
        });
        return { data: null, error: error.message };
      }

      // Atualizar estado local
      const updatedPost = data as Post;
      setPosts((prev) =>
        prev.map((post) => (post.id === id ? updatedPost : post))
      );

      logger.info('Post atualizado com sucesso', {
        postId: id,
        title: updatedPost.title,
      });

      return { data: updatedPost, error: null };
    } catch (error) {
      logger.error('Erro inesperado ao atualizar post', {
        error: error instanceof Error ? error.message : 'Erro inesperado',
        postId: id,
        postData: { title: postData.title, status: postData.status },
      });
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro inesperado',
      };
    }
  };

  const deletePost = async (id: string): Promise<{ error: string | null }> => {
    try {
      logger.info('Iniciando exclusão de post', { postId: id });

      const { error } = await supabase.from('posts').delete().eq('id', id);

      if (error) {
        logger.error('Erro ao deletar post no Supabase', {
          error: error.message,
          postId: id,
        });
        return { error: error.message || 'Erro ao deletar post' };
      }

      // Remover o post do estado local
      setPosts((prev) => prev.filter((post) => post.id !== id));

      logger.info('Post deletado com sucesso', { postId: id });

      return { error: null };
    } catch (err) {
      logger.error('Erro inesperado ao deletar post', {
        error: err instanceof Error ? err.message : 'Erro desconhecido',
        postId: id,
      });
      return {
        error: err instanceof Error ? err.message : 'Erro desconhecido',
      };
    }
  };

  const getPostBySlug = useCallback(
    async (
      slug: string
    ): Promise<{ data: Post | null; error: string | null }> => {
      try {
        logger.info('Buscando post por slug', { slug });

        const { data, error } = await supabase
          .from('posts')
          .select('*')
          .eq('slug', slug)
          .maybeSingle();

        if (error) {
          logger.error('Erro ao buscar post por slug no Supabase', {
            error: error.message,
            slug,
          });
          throw new Error(error.message || 'Post não encontrado');
        }

        if (!data) {
          logger.warn('Post não encontrado por slug', { slug });
          return { data: null, error: 'Post não encontrado' };
        }

        // Buscar profiles e categories separadamente para fazer JOIN manual
        const { data: profilesData } = await supabase
          .from('profiles')
          .select('*');

        const { data: categoriesData } = await supabase
          .from('categories')
          .select('*');

        // Buscar autor e categoria pelos IDs
        const author = profilesData?.find((p) => p.id === data.author_id);
        const category = categoriesData?.find((c) => c.id === data.category_id);

        // Carregar tags e categorias usando estrutura normalizada
        const tags = await loadPostTags(data.id);
        const categories = await loadPostCategories(data.id);

        // Usar categoria principal (category_id) ou primeira das categorias normalizadas
        const primaryCategory = category ||
          categories[0] || {
            id: 'default',
            name: 'Geral',
            slug: 'geral',
            description: '',
            color: '#00ffff',
            postsCount: 0,
          };

        console.log('🔄 [getPostBySlug] Processando post:', {
          title: data.title,
          author_id: data.author_id,
          category_id: data.category_id,
          author: author?.name || 'Sem autor',
          category: primaryCategory.name,
          tagsCount: tags.length,
        });

        // Mapear dados do Supabase para o formato esperado pelo frontend v2
        const post: Post = {
          id: data.id,
          title: data.title,
          slug: data.slug,
          content: data.content,
          excerpt: data.excerpt || '',
          thumbnail:
            data.thumbnail ||
            data.featured_image ||
            'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
          featured: data.featured || false,
          published: data.status === 'published',
          readTime: data.reading_time || data.read_time || 5,
          views: data.views || 0,
          likes: data.likes || 0,
          date: data.published_at || data.created_at,
          createdAt: data.created_at,
          updatedAt: data.updated_at,
          // Mapear autor usando JOIN manual
          author: {
            id:
              author?.id ||
              data.author_id ||
              '00000000-0000-0000-0000-000000000001',
            name: author?.name || 'Blueprint',
            email: author?.email || '<EMAIL>',
            avatar: 'blueprint-logo', // Identificador especial para usar o componente BlueprintAvatar
            bio:
              author?.bio || 'Criador de conteúdo técnico e inovação digital',
            role: (author?.role as any) || 'admin',
            socialLinks: {},
            postsCount: 0,
            joinedAt: author?.created_at || data.created_at,
          },
          // Usar categoria principal
          category: primaryCategory,
          // Usar tags da estrutura normalizada
          tags: tags,
          // SEO padrão
          seo: {
            title: data.title,
            description: data.excerpt || '',
            keywords: '',
            ogImage: data.thumbnail || '',
            ogTitle: data.title,
            ogDescription: data.excerpt || '',
            twitterCard: 'summary_large_image' as const,
          },
        };

        logger.info('Post encontrado por slug', {
          slug,
          postId: post.id,
          title: post.title,
        });

        return { data: post, error: null };
      } catch (err) {
        logger.error('Erro inesperado ao buscar post por slug', {
          error: err instanceof Error ? err.message : 'Erro desconhecido',
          slug,
        });
        return {
          data: null,
          error: err instanceof Error ? err.message : 'Erro desconhecido',
        };
      }
    },
    []
  );

  return {
    posts,
    loading,
    error,
    createPost,
    updatePost,
    deletePost,
    getPostBySlug,
    refetch: fetchPosts,
  };
};

// ===== HOOK PARA POSTS EM DESTAQUE =====
export const useFeaturedPosts = (limit: number = 3): UseFeaturedPostsReturn => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Log do hook mount
  ComponentLogger.logMount('useFeaturedPosts', { limit });

  useEffect(() => {
    const fetchFeaturedPosts = async (): Promise<void> => {
      try {
        setLoading(true);
        setError(null);

        logger.info('Buscando posts em destaque', {
          limit,
          status: 'published',
        });

        // Buscar posts (mesma abordagem do usePosts principal)
        const { data: postsData, error: postsError } = await supabase
          .from('posts')
          .select('*')
          .eq('status', 'published')
          .order('published_at', { ascending: false })
          .limit(limit);

        if (postsError) {
          logger.error('Erro ao buscar posts em destaque', {
            error: postsError.message,
            limit,
          });
          throw new Error(
            postsError.message || 'Erro ao buscar posts em destaque'
          );
        }

        logger.debug('Posts em destaque encontrados', {
          count: postsData?.length || 0,
          limit,
        });

        // Buscar profiles e categories separadamente
        // Buscar dados auxiliares (não utilizados no momento)
        // const { data: profilesData } = await supabase
        //   .from('profiles')
        //   .select('*');

        // const { data: categoriesData } = await supabase
        //   .from('categories')
        //   .select('*');

        // 🔍 DEBUG: Log do resultado
        console.log('⭐ [useFeaturedPosts] Resultado:', {
          totalPosts: postsData?.length || 0,
          posts:
            postsData?.map((p) => ({
              title: p.title,
              status: p.status,
              published_at: p.published_at,
            })) || [],
        });

        if (error) {
          throw new Error(
            typeof error === 'string'
              ? error
              : 'Erro ao buscar posts em destaque'
          );
        }

        // Mapear posts usando a mesma lógica do usePosts principal
        const posts = (await Promise.all(
          (postsData || []).map(async (post: any) => {
            // Carregar tags usando estrutura normalizada
            const tags = await loadPostTags(post.id);

            return {
              id: post.id,
              title: post.title,
              slug: post.slug,
              content: post.content,
              excerpt: post.excerpt || '',
              thumbnail:
                post.thumbnail ||
                post.featured_image ||
                'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
              featured: post.featured || false,
              published: post.status === 'published',
              readTime: post.reading_time || post.read_time || 5,
              views: post.views || 0,
              likes: post.likes || 0,
              date: post.published_at || post.created_at,
              createdAt: post.created_at,
              updatedAt: post.updated_at,
              author: {
                id:
                  post.profiles?.id ||
                  post.author_id ||
                  '00000000-0000-0000-0000-000000000001',
                name: post.profiles?.name || post.author_name || 'Blueprint',
                email:
                  post.profiles?.email ||
                  post.author_email ||
                  '<EMAIL>',
                avatar: 'blueprint-logo', // Identificador especial para usar o componente BlueprintAvatar
                bio:
                  post.profiles?.bio ||
                  'Criador de conteúdo técnico e inovação digital',
                role: (post.profiles?.role as any) || 'admin',
                socialLinks: {},
                postsCount: 0,
                joinedAt: post.profiles?.created_at || post.created_at,
              },
              category: {
                id: post.categories?.id || 'default',
                name: post.categories?.name || 'Geral',
                slug: post.categories?.slug || 'geral',
                description: post.categories?.description || '',
                color: post.categories?.color || '#00ffff',
                icon: post.categories?.icon || '📁',
                postsCount: 0,
              },
              tags: tags,
              seo: {
                title: post.title,
                description: post.excerpt || '',
                keywords: '',
                ogImage: post.thumbnail || '',
                ogTitle: post.title,
                ogDescription: post.excerpt || '',
                twitterCard: 'summary_large_image' as const,
              },
            };
          })
        )) as Post[];

        logger.info('Posts em destaque processados com sucesso', {
          count: posts.length,
          limit,
        });

        setPosts(posts);
      } catch (err) {
        logger.error('Erro inesperado ao buscar posts em destaque', {
          error: err instanceof Error ? err.message : 'Erro desconhecido',
          limit,
        });
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedPosts();
  }, [limit]);

  return { posts, loading, error };
};

// ===== EXPORTS DE TIPOS =====
export type { UseFeaturedPostsReturn, UsePostsOptions, UsePostsReturn };
