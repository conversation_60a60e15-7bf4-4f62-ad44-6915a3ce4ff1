import { useEffect, useState } from 'react';
import {
  AnalyticsService,
  type PopularPost,
} from '../services/analyticsService';

/**
 * Hook para gerenciar analytics de posts
 */
export const usePostAnalytics = () => {
  /**
   * Incrementa visualização de um post
   */
  const incrementView = async (postId: string) => {
    try {
      const result = await AnalyticsService.incrementPostView(postId);
      if (!result.success) {
        console.error('Erro ao incrementar visualização:', result.error);
      }
      return result;
    } catch (error) {
      console.error('Erro ao incrementar visualização:', error);
      return { success: false, error: 'Erro interno' };
    }
  };

  /**
   * Incrementa visualização por slug
   */
  const incrementViewBySlug = async (slug: string) => {
    try {
      const result = await AnalyticsService.incrementPostViewBySlug(slug);
      if (!result.success) {
        console.error(
          'Erro ao incrementar visualização por slug:',
          result.error
        );
      }
      return result;
    } catch (error) {
      console.error('Erro ao incrementar visualização por slug:', error);
      return { success: false, error: 'Erro interno' };
    }
  };

  return {
    incrementView,
    incrementViewBySlug,
  };
};

/**
 * Hook para buscar posts populares
 */
export const usePopularPosts = (limit: number = 5) => {
  const [popularPosts, setPopularPosts] = useState<PopularPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPopularPosts = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await AnalyticsService.getPopularPosts(limit);

        if (error) {
          setError(error);
          setPopularPosts([]);
        } else {
          setPopularPosts(data || []);
        }
      } catch (err) {
        setError('Erro ao carregar posts populares');
        setPopularPosts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularPosts();
  }, [limit]);

  /**
   * Recarrega os posts populares
   */
  const refetch = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await AnalyticsService.getPopularPosts(limit);

      if (error) {
        setError(error);
        setPopularPosts([]);
      } else {
        setPopularPosts(data || []);
      }
    } catch (err) {
      setError('Erro ao carregar posts populares');
      setPopularPosts([]);
    } finally {
      setLoading(false);
    }
  };

  return {
    popularPosts,
    loading,
    error,
    refetch,
  };
};

/**
 * Hook para estatísticas gerais do blog
 */
export const useBlogStats = () => {
  const [stats, setStats] = useState<{
    totalPosts: number;
    totalViews: number;
    avgViewsPerPost: number;
    mostPopularPost?: PopularPost;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await AnalyticsService.getBlogStats();

        if (error) {
          setError(error);
          setStats(null);
        } else {
          setStats(data);
        }
      } catch (err) {
        setError('Erro ao carregar estatísticas');
        setStats(null);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  /**
   * Recarrega as estatísticas
   */
  const refetch = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await AnalyticsService.getBlogStats();

      if (error) {
        setError(error);
        setStats(null);
      } else {
        setStats(data);
      }
    } catch (err) {
      setError('Erro ao carregar estatísticas');
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  return {
    stats,
    loading,
    error,
    refetch,
  };
};
