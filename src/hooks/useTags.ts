import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

// Tipos
export interface Tag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  created_at: string;
  is_active: boolean;
}

export interface UseTagsReturn {
  tags: Tag[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Hook principal para tags
export const useTags = (): UseTagsReturn => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTags = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        throw new Error(error.message || 'Erro ao buscar tags');
      }

      setTags(data || []);
    } catch (err) {
      console.error('Erro ao buscar tags:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  return {
    tags,
    loading,
    error,
    refetch: fetchTags,
  };
};

// Hook para tags de um post específico
export const usePostTags = (postId: string | undefined) => {
  const [postTags, setPostTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPostTags = async (): Promise<void> => {
    if (!postId) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('post_tags')
        .select(`
          tag_id,
          tags (
            id,
            name,
            slug,
            description,
            color,
            icon,
            created_at,
            is_active
          )
        `)
        .eq('post_id', postId);

      if (error) {
        throw new Error(error.message || 'Erro ao buscar tags do post');
      }

      const tags = data?.map((item: any) => item.tags).filter(Boolean) || [];
      setPostTags(tags);
    } catch (err) {
      console.error('Erro ao buscar tags do post:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  const updatePostTags = async (tagIds: string[]): Promise<{ error: string | null }> => {
    if (!postId) {
      return { error: 'ID do post não fornecido' };
    }

    try {
      // Remover tags existentes
      const { error: deleteError } = await supabase
        .from('post_tags')
        .delete()
        .eq('post_id', postId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Adicionar novas tags
      if (tagIds.length > 0) {
        const postTagsData = tagIds.map(tagId => ({
          post_id: postId,
          tag_id: tagId
        }));

        const { error: insertError } = await supabase
          .from('post_tags')
          .insert(postTagsData);

        if (insertError) {
          throw new Error(insertError.message);
        }
      }

      // Recarregar tags do post
      await fetchPostTags();
      
      return { error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('Erro ao atualizar tags do post:', err);
      return { error: errorMessage };
    }
  };

  useEffect(() => {
    fetchPostTags();
  }, [postId]);

  return {
    postTags,
    loading,
    error,
    updatePostTags,
    refetch: fetchPostTags,
  };
};

// Hook para buscar posts por tag
export const usePostsByTag = (tagSlug: string | undefined) => {
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPostsByTag = async (): Promise<void> => {
    if (!tagSlug) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('post_tags')
        .select(`
          post_id,
          posts (
            id,
            title,
            slug,
            excerpt,
            featured_image,
            status,
            language,
            published_at,
            created_at,
            updated_at,
            author_id
          ),
          tags!inner (
            slug
          )
        `)
        .eq('tags.slug', tagSlug)
        .eq('posts.status', 'published')
        .order('posts.published_at', { ascending: false });

      if (error) {
        throw new Error(error.message || 'Erro ao buscar posts por tag');
      }

      const posts = data?.map((item: any) => item.posts).filter(Boolean) || [];
      setPosts(posts);
    } catch (err) {
      console.error('Erro ao buscar posts por tag:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPostsByTag();
  }, [tagSlug]);

  return {
    posts,
    loading,
    error,
    refetch: fetchPostsByTag,
  };
};

export default useTags;
