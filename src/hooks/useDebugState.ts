/**
 * @fileoverview Hook para debug de estado com logging automático
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ComponentLogger } from '../utils/component-logger';

/**
 * Hook que funciona como useState mas com logging automático de mudanças de estado
 * Útil para debug e monitoramento de componentes
 */
export function useDebugState<T>(
  initialState: T,
  componentName: string,
  stateName: string
): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [state, setState] = useState(initialState);

  const setDebugState = useCallback(
    (newState: React.SetStateAction<T>) => {
      setState((prevState) => {
        const nextState =
          typeof newState === 'function'
            ? (newState as Function)(prevState)
            : newState;

        // Log da mudança de estado
        ComponentLogger.logStateChange(
          `${componentName}.${stateName}`,
          prevState,
          nextState
        );

        return nextState;
      });
    },
    [componentName, stateName]
  );

  return [state, setDebugState];
}

/**
 * Hook para debug de efeitos colaterais
 * Loga quando um useEffect é executado
 */
export function useDebugEffect(
  effect: React.EffectCallback,
  deps: React.DependencyList | undefined,
  componentName: string,
  effectName: string
): void {
  const debugEffect = useCallback(
    () => {
      ComponentLogger.logMount(`${componentName}.${effectName}Effect`);
      const cleanup = effect();

      return () => {
        ComponentLogger.logUnmount(`${componentName}.${effectName}Effect`);
        if (cleanup) cleanup();
      };
    },
    deps ? [...deps, componentName, effectName] : [componentName, effectName]
  );

  useEffect(debugEffect, deps);
}

/**
 * Hook para debug de performance
 * Mede o tempo de renderização de um componente
 */
export function useDebugPerformance(componentName: string) {
  const startTime = useRef<number>(Date.now());

  useEffect(() => {
    const renderTime = Date.now() - startTime.current;
    ComponentLogger.logMount(componentName, { renderTime });

    return () => {
      ComponentLogger.logUnmount(componentName);
    };
  }, [componentName]);

  // Atualizar tempo de início a cada render
  startTime.current = Date.now();
}
