import { useEffect, useState } from 'react';
import { supabaseCategoryService } from '../services/supabaseService';
import type { Category } from '../types';
import type { SupabaseCategory } from '../types/supabase';

/**
 * Hook para gerenciar categorias com integração ao Supabase
 */
export const useCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Converte SupabaseCategory para Category
   */
  const convertSupabaseCategory = async (supabaseCategory: SupabaseCategory): Promise<Category> => {
    // Busca contagem de posts para esta categoria
    const postsCountResponse = await supabaseCategoryService.getCategoryPostsCount(supabaseCategory.id);
    const postsCount = postsCountResponse.data || 0;

    return {
      id: supabaseCategory.id,
      name: supabaseCategory.name,
      slug: supabaseCategory.slug,
      description: supabaseCategory.description || '',
      color: supabaseCategory.color || '#00ffff',
      postsCount,
    };
  };

  /**
   * Carrega todas as categorias
   */
  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await supabaseCategoryService.getCategories();

      if (response.error) {
        setError('Erro ao carregar categorias');
        console.error('Erro ao carregar categorias:', response.error);
        return;
      }

      // Converte categorias do Supabase para o formato esperado
      const convertedCategories = await Promise.all(
        (response.data || []).map(convertSupabaseCategory)
      );

      setCategories(convertedCategories);
    } catch (err) {
      setError('Erro inesperado ao carregar categorias');
      console.error('Erro inesperado:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Cria uma nova categoria
   */
  const createCategory = async (categoryData: {
    name: string;
    slug: string;
    description?: string;
    color?: string;
  }): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await supabaseCategoryService.createCategory(categoryData);

      if (response.error) {
        console.error('Erro ao criar categoria:', response.error);
        return { success: false, error: 'Erro ao criar categoria' };
      }

      if (response.data) {
        const newCategory = await convertSupabaseCategory(response.data);
        setCategories(prev => [...prev, newCategory]);
      }

      return { success: true };
    } catch (err) {
      console.error('Erro inesperado ao criar categoria:', err);
      return { success: false, error: 'Erro inesperado' };
    }
  };

  /**
   * Atualiza uma categoria existente
   */
  const updateCategory = async (
    id: string,
    categoryData: {
      name?: string;
      slug?: string;
      description?: string;
      color?: string;
    }
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await supabaseCategoryService.updateCategory(id, categoryData);

      if (response.error) {
        console.error('Erro ao atualizar categoria:', response.error);
        return { success: false, error: 'Erro ao atualizar categoria' };
      }

      if (response.data) {
        const updatedCategory = await convertSupabaseCategory(response.data);
        setCategories(prev =>
          prev.map(cat => (cat.id === id ? updatedCategory : cat))
        );
      }

      return { success: true };
    } catch (err) {
      console.error('Erro inesperado ao atualizar categoria:', err);
      return { success: false, error: 'Erro inesperado' };
    }
  };

  /**
   * Deleta uma categoria
   */
  const deleteCategory = async (id: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // Verifica se a categoria tem posts antes de deletar
      const category = categories.find(c => c.id === id);
      if (category && category.postsCount > 0) {
        return {
          success: false,
          error: `Não é possível deletar a categoria "${category.name}" pois ela possui ${category.postsCount} posts.`
        };
      }

      const response = await supabaseCategoryService.deleteCategory(id);

      if (response.error) {
        console.error('Erro ao deletar categoria:', response.error);
        return { success: false, error: 'Erro ao deletar categoria' };
      }

      setCategories(prev => prev.filter(cat => cat.id !== id));
      return { success: true };
    } catch (err) {
      console.error('Erro inesperado ao deletar categoria:', err);
      return { success: false, error: 'Erro inesperado' };
    }
  };

  /**
   * Recarrega as categorias
   */
  const refetch = () => {
    loadCategories();
  };

  // Carrega categorias na inicialização
  useEffect(() => {
    loadCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    createCategory,
    updateCategory,
    deleteCategory,
    refetch,
  };
};

export default useCategories;
