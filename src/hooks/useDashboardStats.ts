/**
 * @fileoverview Hook para estatísticas do dashboard
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

// ============================================================================
// INTERFACES
// ============================================================================

export interface DashboardStats {
  postsCount: number;
  categoriesCount: number;
  tagsCount: number;
  mediaCount: number;
  usersCount: number;
  totalViews: number;
  publishedPosts: number;
  draftPosts: number;
}

interface UseDashboardStatsReturn {
  stats: DashboardStats | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para buscar estatísticas do dashboard
 */
export const useDashboardStats = (): UseDashboardStatsReturn => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Busca todas as estatísticas
   */
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Buscar contagem de posts
      const { count: postsCount, error: postsError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true });

      if (postsError) throw postsError;

      // Buscar contagem de posts publicados
      const { count: publishedCount, error: publishedError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'published');

      if (publishedError) throw publishedError;

      // Buscar contagem de rascunhos
      const { count: draftCount, error: draftError } = await supabase
        .from('posts')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'draft');

      if (draftError) throw draftError;

      // Buscar contagem de categorias
      const { count: categoriesCount, error: categoriesError } = await supabase
        .from('categories')
        .select('*', { count: 'exact', head: true });

      if (categoriesError) throw categoriesError;

      // Buscar contagem de tags
      const { count: tagsCount, error: tagsError } = await supabase
        .from('tags')
        .select('*', { count: 'exact', head: true });

      if (tagsError) throw tagsError;

      // Buscar total de visualizações
      const { data: viewsData, error: viewsError } = await supabase
        .from('posts')
        .select('views')
        .eq('status', 'published');

      if (viewsError) throw viewsError;

      const totalViews = viewsData?.reduce((sum, post) => sum + (post.views || 0), 0) || 0;

      // Buscar contagem de arquivos de mídia (simulado - ajustar conforme estrutura real)
      let mediaCount = 0;
      try {
        const { data: mediaData, error: mediaError } = await supabase.storage
          .from('media')
          .list('', { limit: 1000 });

        if (!mediaError && mediaData) {
          mediaCount = mediaData.length;
        }
      } catch (mediaError) {
        // Se não conseguir acessar storage, usar valor padrão
        console.warn('Não foi possível acessar contagem de mídia:', mediaError);
      }

      // Usuários (sempre 1 para sistema de usuário único)
      const usersCount = 1;

      const dashboardStats: DashboardStats = {
        postsCount: postsCount || 0,
        categoriesCount: categoriesCount || 0,
        tagsCount: tagsCount || 0,
        mediaCount,
        usersCount,
        totalViews,
        publishedPosts: publishedCount || 0,
        draftPosts: draftCount || 0,
      };

      setStats(dashboardStats);

    } catch (err) {
      console.error('Erro ao buscar estatísticas do dashboard:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Refetch manual das estatísticas
   */
  const refetch = async () => {
    await fetchStats();
  };

  // Buscar dados na montagem do componente
  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    refetch,
  };
};

// ============================================================================
// HOOK PARA ESTATÍSTICAS ESPECÍFICAS
// ============================================================================

/**
 * Hook simplificado para contadores específicos
 */
export const useCounters = () => {
  const { stats, loading, error } = useDashboardStats();

  return {
    counters: {
      posts: stats?.postsCount || 0,
      published: stats?.publishedPosts || 0,
      drafts: stats?.draftPosts || 0,
      categories: stats?.categoriesCount || 0,
      tags: stats?.tagsCount || 0,
      media: stats?.mediaCount || 0,
      users: stats?.usersCount || 1,
      views: stats?.totalViews || 0,
    },
    loading,
    error,
  };
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Formata números para exibição
 */
export const formatCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  }
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

/**
 * Calcula porcentagem de crescimento
 */
export const calculateGrowth = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
};

export default useDashboardStats;
