import DOMPurify from 'dompurify';
import { useMemo } from 'react';

// ===== INTERFACES =====
interface SanitizationConfig {
  allowHtml?: boolean;
  allowLinks?: boolean;
  allowImages?: boolean;
  maxImageSize?: number;
  trustedDomains?: string[];
  strictMode?: boolean; // Bloqueia conteúdo ao invés de sanitizar
  isLegacyContent?: boolean; // Novo: identifica conteúdo legado (já publicado)
}

interface SanitizedContent {
  content: string;
  warnings: string[];
  isClean: boolean;
  isBlocked: boolean; // Novo: indica se conteúdo foi bloqueado
  blockReason?: string; // Novo: motivo do bloqueio
}

// ===== CONFIGURAÇÕES DE SEGURANÇA =====
const DEFAULT_CONFIG: SanitizationConfig = {
  allowHtml: false,
  allowLinks: true,
  allowImages: true,
  maxImageSize: 2048, // 2MB
  strictMode: true, // Novo: modo rigoroso por padrão
  trustedDomains: [
    'github.com',
    'githubusercontent.com',
    'imgur.com',
    'unsplash.com',
    'pexels.com',
    'pixabay.com',
    'cloudinary.com',
    'vercel.app',
    'netlify.app',
  ],
};

// ===== HOOK PRINCIPAL =====
export const useSanitizedMarkdown = (
  content: string,
  config: SanitizationConfig = {}
): SanitizedContent => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return useMemo(() => {
    const warnings: string[] = [];
    let sanitizedContent = content;
    let isClean = true;
    let isBlocked = false;
    let blockReason: string | undefined;

    try {
      // ===== MODO STRICT: DETECTA E BLOQUEIA CONTEÚDO PERIGOSO (APENAS PARA CONTEÚDO NOVO) =====
      if (finalConfig.strictMode && !finalConfig.isLegacyContent) {
        const dangerousPatterns = [
          {
            pattern: /javascript:/gi,
            reason: 'Links com JavaScript detectados',
          },
          {
            pattern: /data:text\/html/gi,
            reason: 'Data URLs com HTML detectados',
          },
          { pattern: /vbscript:/gi, reason: 'Scripts VBScript detectados' },
          {
            pattern: /on\w+\s*=/gi,
            reason: 'Eventos JavaScript inline detectados',
          },
          {
            pattern: /<script[\s\S]*?<\/script>/gi,
            reason: 'Tags script detectadas',
          },
          {
            pattern: /<iframe[\s\S]*?<\/iframe>/gi,
            reason: 'Tags iframe detectadas',
          },
          {
            pattern: /<object[\s\S]*?<\/object>/gi,
            reason: 'Tags object detectadas',
          },
          { pattern: /<embed[\s\S]*?>/gi, reason: 'Tags embed detectadas' },
          {
            pattern: /<form[\s\S]*?<\/form>/gi,
            reason: 'Tags form detectadas',
          },
        ];

        for (const { pattern, reason } of dangerousPatterns) {
          if (pattern.test(content)) {
            isBlocked = true;
            blockReason = reason;
            break;
          }
        }

        // Se bloqueado, retorna conteúdo de erro
        if (isBlocked) {
          return {
            content: '',
            warnings: [blockReason!],
            isClean: false,
            isBlocked: true,
            blockReason,
          };
        }
      }
      // 1. Sanitização básica com DOMPurify
      const dompurifyConfig = {
        ALLOWED_TAGS: finalConfig.allowHtml
          ? [
              'p',
              'br',
              'strong',
              'em',
              'u',
              'code',
              'pre',
              'blockquote',
              'h1',
              'h2',
              'h3',
              'h4',
              'h5',
              'h6',
              'ul',
              'ol',
              'li',
            ]
          : [],
        ALLOWED_ATTR: finalConfig.allowHtml ? ['class', 'id'] : [],
        ALLOW_DATA_ATTR: false,
        ALLOW_UNKNOWN_PROTOCOLS: false,
        SANITIZE_DOM: true,
        KEEP_CONTENT: true,
        FORBID_TAGS: [
          'script',
          'object',
          'embed',
          'iframe',
          'form',
          'input',
          'button',
          'style',
          'link',
          'meta',
        ],
        FORBID_ATTR: [
          'onclick',
          'onload',
          'onerror',
          'onmouseover',
          'onmouseout',
          'onmousemove',
          'onmousedown',
          'onmouseup',
          'onfocus',
          'onblur',
          'onkeydown',
          'onkeyup',
          'onkeypress',
          'onsubmit',
          'onreset',
          'onselect',
          'onchange',
          'onscroll',
          'onresize',
          'onabort',
          'oncanplay',
          'oncanplaythrough',
          'ondurationchange',
          'onemptied',
          'onended',
          'onloadeddata',
          'onloadedmetadata',
          'onloadstart',
          'onpause',
          'onplay',
          'onplaying',
          'onprogress',
          'onratechange',
          'onseeked',
          'onseeking',
          'onstalled',
          'onsuspend',
          'ontimeupdate',
          'onvolumechange',
          'onwaiting',
          'ondrag',
          'ondragend',
          'ondragenter',
          'ondragleave',
          'ondragover',
          'ondragstart',
          'ondrop',
          'onwheel',
        ],
      };

      // Aplicar DOMPurify se HTML for permitido
      if (finalConfig.allowHtml) {
        const cleaned = DOMPurify.sanitize(sanitizedContent, dompurifyConfig);
        if (cleaned !== sanitizedContent) {
          warnings.push('HTML potencialmente perigoso foi removido');
          isClean = false;
        }
        sanitizedContent = cleaned;
      }

      // 2. Validação de links
      if (finalConfig.allowLinks) {
        const linkRegex = /\[([^\]]*)\]\(([^)]+)\)/g;
        sanitizedContent = sanitizedContent.replace(
          linkRegex,
          (match, text, url) => {
            const cleanUrl = validateUrl(url, finalConfig.trustedDomains || []);
            if (cleanUrl !== url) {
              warnings.push(`Link suspeito removido: ${url}`);
              isClean = false;
              return text; // Remove o link, mantém apenas o texto
            }
            return match;
          }
        );
      }

      // 3. Validação de imagens
      if (finalConfig.allowImages) {
        const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
        sanitizedContent = sanitizedContent.replace(
          imageRegex,
          (match, alt, src) => {
            const cleanSrc = validateImageUrl(
              src,
              finalConfig.trustedDomains || []
            );
            if (cleanSrc !== src) {
              warnings.push(`Imagem suspeita removida: ${src}`);
              isClean = false;
              return `[Imagem removida: ${alt}]`;
            }
            return match;
          }
        );
      }

      // 4. Remover scripts inline e outros elementos perigosos
      const dangerousPatterns = [
        /javascript:/gi,
        /data:text\/html/gi,
        /vbscript:/gi,
        /on\w+\s*=/gi,
        /<script[\s\S]*?<\/script>/gi,
        /<iframe[\s\S]*?<\/iframe>/gi,
        /<object[\s\S]*?<\/object>/gi,
        /<embed[\s\S]*?>/gi,
        /<form[\s\S]*?<\/form>/gi,
      ];

      dangerousPatterns.forEach((pattern) => {
        if (pattern.test(sanitizedContent)) {
          sanitizedContent = sanitizedContent.replace(pattern, '');
          warnings.push('Conteúdo potencialmente perigoso foi removido');
          isClean = false;
        }
      });

      // 5. Limitar tamanho do conteúdo
      const maxLength = 50000; // 50KB
      if (sanitizedContent.length > maxLength) {
        sanitizedContent =
          sanitizedContent.substring(0, maxLength) +
          '\n\n[Conteúdo truncado por segurança]';
        warnings.push('Conteúdo truncado por exceder limite de tamanho');
        isClean = false;
      }

      return {
        content: sanitizedContent,
        warnings,
        isClean,
        isBlocked: false,
        blockReason: undefined,
      };
    } catch (error) {
      console.error('Erro na sanitização:', error);
      return {
        content: 'Erro ao processar conteúdo',
        warnings: ['Erro interno de sanitização'],
        isClean: false,
        isBlocked: true,
        blockReason: 'Erro interno de processamento',
      };
    }
  }, [content, finalConfig]);
};

// ===== FUNÇÕES AUXILIARES =====

/**
 * Valida e limpa URLs
 */
function validateUrl(url: string, trustedDomains: string[]): string {
  try {
    // Remover espaços e caracteres perigosos
    const cleanUrl = url.trim();

    // Bloquear protocolos perigosos
    const dangerousProtocols = [
      'javascript:',
      'data:',
      'vbscript:',
      'file:',
      'ftp:',
    ];
    if (
      dangerousProtocols.some((protocol) =>
        cleanUrl.toLowerCase().startsWith(protocol)
      )
    ) {
      return '';
    }

    // Se for URL relativa, permitir
    if (
      cleanUrl.startsWith('/') ||
      cleanUrl.startsWith('./') ||
      cleanUrl.startsWith('../')
    ) {
      return cleanUrl;
    }

    // Se for URL absoluta, validar domínio
    if (cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')) {
      const urlObj = new URL(cleanUrl);

      // Verificar se o domínio é confiável
      const isValidDomain = trustedDomains.some(
        (domain) =>
          urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      );

      if (!isValidDomain) {
        return ''; // Remove URLs de domínios não confiáveis
      }

      return cleanUrl;
    }

    // URLs sem protocolo - assumir https
    if (cleanUrl.includes('.')) {
      return validateUrl('https://' + cleanUrl, trustedDomains);
    }

    return cleanUrl;
  } catch (error) {
    return ''; // Remove URLs malformadas
  }
}

/**
 * Valida URLs de imagens
 */
function validateImageUrl(src: string, trustedDomains: string[]): string {
  const validUrl = validateUrl(src, trustedDomains);

  if (!validUrl) return '';

  // Verificar extensões de imagem válidas
  const validExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.svg',
    '.bmp',
  ];
  const hasValidExtension = validExtensions.some((ext) =>
    validUrl.toLowerCase().includes(ext)
  );

  // Se não tem extensão válida, mas é de domínio confiável, permitir (pode ser URL dinâmica)
  if (!hasValidExtension) {
    try {
      const urlObj = new URL(validUrl);
      const isFromTrustedDomain = trustedDomains.some(
        (domain) =>
          urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      );

      if (!isFromTrustedDomain) {
        return '';
      }
    } catch {
      return '';
    }
  }

  return validUrl;
}

export default useSanitizedMarkdown;
