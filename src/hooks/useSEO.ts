import { useMemo } from 'react';
import { useLocation } from 'react-router';
import type { Category, Post, SEOData, Tag } from '../types';
import { truncateText } from '../utils/seoUtils';

interface UseSEOOptions {
  post?: Post;
  category?: Category;
  tag?: Tag;
  customTitle?: string;
  customDescription?: string;
  customKeywords?: string[];
  customImage?: string;
  pageType?: 'home' | 'blog' | 'post' | 'category' | 'tag' | 'page';
}

const useSEO = (options: UseSEOOptions = {}): SEOData => {
  const location = useLocation();
  const {
    post,
    category,
    tag,
    customTitle,
    customDescription,
    customKeywords,
    customImage,
    pageType = 'page',
  } = options;

  const seoData = useMemo((): SEOData => {
    const siteUrl = 'https://blueprintblog.tech';
    const siteName = 'Blueprint Blog';
    const defaultDescription =
      'Blueprint Blog - Tecnologia, desenvolvimento e inovação. Artigos sobre programação, design e tendências tech.';
    const defaultKeywords = [
      'tecnologia',
      'programação',
      'desenvolvimento',
      'design',
      'inovação',
      'tech',
      'blueprint',
    ];
    const defaultImage = '/images/og-default.jpg';

    // Construir URL canonical
    const canonical = `${siteUrl}${location.pathname}`;

    // SEO para Posts
    if (post && pageType === 'post') {
      const postKeywords = [
        ...defaultKeywords,
        post.category?.name.toLowerCase() || '',
        ...(post.tags?.map((tag) => tag.name.toLowerCase()) || []),
      ].filter(Boolean);

      return {
        title: `${post.title} | ${siteName}`,
        description: post.excerpt || truncateText(post.content, 160),
        keywords: postKeywords.join(', '),
        canonical,
        ogTitle: post.title,
        ogDescription: post.excerpt || truncateText(post.content, 160),
        ogImage: post.thumbnail || defaultImage,
        ogType: 'article',
        twitterCard: 'summary_large_image',
        author: post.author?.name || 'Blueprint Blog',
        publishedTime: post.createdAt,
        modifiedTime: post.updatedAt,
        section: post.category?.name,
        tags: post.tags?.map((tag) => tag.name),
        locale: 'pt_BR',
        siteName,
      };
    }

    // SEO para Categorias
    if (category && pageType === 'category') {
      return {
        title: `${category.name} | ${siteName}`,
        description:
          category.description ||
          `Artigos sobre ${category.name.toLowerCase()} no Blueprint Blog. Descubra conteúdos exclusivos e atualizados.`,
        keywords: [category.name.toLowerCase(), ...defaultKeywords].join(', '),
        canonical,
        ogTitle: `${category.name} | ${siteName}`,
        ogDescription:
          category.description ||
          `Artigos sobre ${category.name.toLowerCase()}`,
        ogImage: defaultImage,
        ogType: 'website',
        locale: 'pt_BR',
        siteName,
      };
    }

    // SEO para Tags
    if (tag && pageType === 'tag') {
      return {
        title: `#${tag.name} | ${siteName}`,
        description:
          tag.description ||
          `Posts marcados com #${tag.name.toLowerCase()} no Blueprint Blog. Explore conteúdos relacionados.`,
        keywords: [tag.name.toLowerCase(), ...defaultKeywords].join(', '),
        canonical,
        ogTitle: `#${tag.name} | ${siteName}`,
        ogDescription:
          tag.description || `Posts marcados com #${tag.name.toLowerCase()}`,
        ogImage: defaultImage,
        ogType: 'website',
        locale: 'pt_BR',
        siteName,
      };
    }

    // SEO para Blog (listagem)
    if (pageType === 'blog') {
      return {
        title: `Blog | ${siteName}`,
        description:
          'Explore todos os artigos do Blueprint Blog. Tecnologia, programação, design e inovação em um só lugar.',
        keywords: ['blog', 'artigos', ...defaultKeywords].join(', '),
        canonical,
        ogTitle: `Blog | ${siteName}`,
        ogDescription: 'Explore todos os artigos do Blueprint Blog',
        ogImage: defaultImage,
        ogType: 'website',
        locale: 'pt_BR',
        siteName,
      };
    }

    // SEO para Home
    if (pageType === 'home') {
      return {
        title: `${siteName} - Tecnologia, Desenvolvimento e Inovação`,
        description: defaultDescription,
        keywords: defaultKeywords.join(', '),
        canonical,
        ogTitle: siteName,
        ogDescription: defaultDescription,
        ogImage: defaultImage,
        ogType: 'website',
        locale: 'pt_BR',
        siteName,
      };
    }

    // SEO Customizado ou Fallback
    return {
      title: customTitle ? `${customTitle} | ${siteName}` : siteName,
      description: customDescription || defaultDescription,
      keywords: customKeywords?.join(', ') || defaultKeywords.join(', '),
      canonical,
      ogTitle: customTitle || siteName,
      ogDescription: customDescription || defaultDescription,
      ogImage: customImage || defaultImage,
      ogType: 'website',
      locale: 'pt_BR',
      siteName,
    };
  }, [
    location.pathname,
    post,
    category,
    tag,
    customTitle,
    customDescription,
    customKeywords,
    customImage,
    pageType,
  ]);

  return seoData;
};

export default useSEO;
