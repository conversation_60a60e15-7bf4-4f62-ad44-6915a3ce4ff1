import { useEffect } from 'react';
import { useLocation } from 'react-router';

// ===== INTERFACES =====
interface WebVitalMetric {
  value: number;
  id: string;
  name: string;
  delta: number;
  entries?: PerformanceEntry[];
}

interface AnalyticsEventParameters {
  event_category?: string;
  event_label?: string;
  value?: number;
  non_interaction?: boolean;
  [key: string]: any;
}

interface UseAnalyticsReturn {
  trackEvent: (
    eventName: string,
    parameters?: AnalyticsEventParameters
  ) => void;
  trackClick: (elementName: string, category?: string) => void;
  trackDownload: (fileName: string) => void;
  trackOutboundLink: (url: string) => void;
  trackSearch: (searchTerm: string) => void;
  trackError: (errorMessage: string, errorLocation: string) => void;
}

// ===== DECLARAÇÕES GLOBAIS =====
declare global {
  interface Window {
    gtag?: (
      command: string,
      targetId: string | AnalyticsEventParameters,
      config?: AnalyticsEventParameters
    ) => void;
  }
}

// ===== FUNÇÕES UTILITÁRIAS =====

/**
 * Função para medir Core Web Vitals
 */
const measureWebVitals = (): void => {
  // Só executa no browser
  if (typeof window === 'undefined') return;

  // Importa dinamicamente a biblioteca web-vitals
  import('web-vitals')
    .then((webVitals: any) => {
      // Na versão 5.x, as funções são named exports, não default exports
      const { onCLS, onFID, onFCP, onLCP, onTTFB } = webVitals;
      // Cumulative Layout Shift
      onCLS((metric: WebVitalMetric) => {
        sendToAnalytics('CLS', metric);
      });

      // First Input Delay (Note: FID foi substituído por INP na v5)
      if (onFID) {
        onFID((metric: WebVitalMetric) => {
          sendToAnalytics('FID', metric);
        });
      }

      // First Contentful Paint
      onFCP((metric: WebVitalMetric) => {
        sendToAnalytics('FCP', metric);
      });

      // Largest Contentful Paint
      onLCP((metric: WebVitalMetric) => {
        sendToAnalytics('LCP', metric);
      });

      // Time to First Byte
      onTTFB((metric: WebVitalMetric) => {
        sendToAnalytics('TTFB', metric);
      });
    })
    .catch((error: Error) => {
      console.warn('Erro ao carregar web-vitals:', error);
    });
};

/**
 * Função para enviar dados para analytics
 * @param metricName - Nome da métrica
 * @param metric - Dados da métrica
 */
const sendToAnalytics = (metricName: string, metric: WebVitalMetric): void => {
  // Google Analytics 4
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metricName, {
      event_category: 'Web Vitals',
      value: Math.round(
        metricName === 'CLS' ? metric.value * 1000 : metric.value
      ),
      event_label: metric.id,
      non_interaction: true,
    });
  }
};

// ===== HOOKS =====

/**
 * Hook principal para analytics
 * @returns Funções para tracking de eventos
 */
export const useAnalytics = (): UseAnalyticsReturn => {
  const location = useLocation();

  // Track page views
  useEffect(() => {
    // Google Analytics 4
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'G-4MLRCV03LK', {
        page_path: location.pathname + location.search,
      });
    }
  }, [location]);

  // Medir Web Vitals apenas uma vez
  useEffect(() => {
    measureWebVitals();
  }, []);

  // Funções para tracking de eventos
  const trackEvent = (
    eventName: string,
    parameters: AnalyticsEventParameters = {}
  ): void => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, parameters);
    }
  };

  const trackClick = (elementName: string, category: string = 'UI'): void => {
    trackEvent('click', {
      event_category: category,
      event_label: elementName,
    });
  };

  const trackDownload = (fileName: string): void => {
    trackEvent('file_download', {
      event_category: 'Downloads',
      event_label: fileName,
    });
  };

  const trackOutboundLink = (url: string): void => {
    trackEvent('click', {
      event_category: 'Outbound Links',
      event_label: url,
    });
  };

  const trackSearch = (searchTerm: string): void => {
    trackEvent('search', {
      search_term: searchTerm,
    });
  };

  const trackError = (errorMessage: string, errorLocation: string): void => {
    trackEvent('exception', {
      description: errorMessage,
      fatal: false,
      event_category: 'JavaScript Errors',
      event_label: errorLocation,
    });
  };

  return {
    trackEvent,
    trackClick,
    trackDownload,
    trackOutboundLink,
    trackSearch,
    trackError,
  };
};

/**
 * Hook para performance monitoring
 */
export const usePerformance = () => {
  useEffect(() => {
    // Medir tempo de carregamento da página
    const measurePageLoad = (): void => {
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType(
          'navigation'
        )[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.loadEventStart;

          // Enviar para analytics
          if (window.gtag) {
            window.gtag('event', 'timing_complete', {
              name: 'page_load',
              value: Math.round(loadTime),
            });
          }
        }
      }
    };

    // Aguardar o carregamento completo
    if (typeof document !== 'undefined') {
      if (document.readyState === 'complete') {
        measurePageLoad();
      } else {
        const handleLoad = (): void => {
          measurePageLoad();
        };

        window.addEventListener('load', handleLoad);
        return () => window.removeEventListener('load', handleLoad);
      }
    }

    // Return explícito para satisfazer TypeScript
    return;
  }, []);
};

// ===== EXPORTS =====
export default useAnalytics;

// ===== EXPORTS DE TIPOS =====
export type { AnalyticsEventParameters, UseAnalyticsReturn, WebVitalMetric };
