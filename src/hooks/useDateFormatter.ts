/**
 * @fileoverview Hook para formatação de datas reutilizável
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect, useState } from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

interface DateFormatterOptions {
  locale?: string;
  autoUpdate?: boolean;
  updateInterval?: number;
}

interface DateFormatterReturn {
  currentDate: Date;
  formatPostDate: (date: string | Date) => string;
  formatTopbarDate: () => string;
  formatRelativeTime: (date: string | Date) => string;
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook para formatação de datas com suporte a internacionalização
 * Reutiliza a lógica já implementada no projeto
 */
export const useDateFormatter = ({
  locale = 'pt-BR',
  autoUpdate = false,
  updateInterval = 60000, // 1 minuto
}: DateFormatterOptions = {}): DateFormatterReturn => {
  const [currentDate, setCurrentDate] = useState(new Date());

  // Atualização automática da data (opcional)
  useEffect(() => {
    if (!autoUpdate) return;

    const interval = setInterval(() => {
      setCurrentDate(new Date());
    }, updateInterval);

    return () => clearInterval(interval);
  }, [autoUpdate, updateInterval]);

  /**
   * Formata data de posts (reutiliza lógica existente)
   * Usado em: Dashboard.tsx, FeaturedPosts.tsx, PostSlider.tsx
   */
  const formatPostDate = (date: string | Date): string => {
    return new Date(date).toLocaleDateString(locale);
  };

  /**
   * Formata data para topbar (formato completo)
   * Ex: "Wednesday, June 14, 2025" (EN) ou "Quarta-feira, 14 de junho de 2025" (PT)
   */
  const formatTopbarDate = (): string => {
    return currentDate.toLocaleDateString(locale, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  /**
   * Formata tempo relativo (para atividades recentes)
   * Ex: "2 minutos atrás", "Agora mesmo"
   */
  const formatRelativeTime = (date: string | Date): string => {
    const now = currentDate.getTime();
    const targetTime = new Date(date).getTime();
    const diffInMinutes = Math.floor((now - targetTime) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora mesmo';
    if (diffInMinutes < 60) return `${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''} atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hora${diffInHours > 1 ? 's' : ''} atrás`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} dia${diffInDays > 1 ? 's' : ''} atrás`;
    
    // Para períodos maiores, usa formatação padrão
    return formatPostDate(date);
  };

  return {
    currentDate,
    formatPostDate,
    formatTopbarDate,
    formatRelativeTime,
  };
};

/**
 * Hook específico para topbar com configurações otimizadas
 */
export const useTopbarDate = (locale = 'pt-BR') => {
  return useDateFormatter({
    locale,
    autoUpdate: true,
    updateInterval: 60000, // Atualiza a cada minuto
  });
};

/**
 * Mapeamento de locales para idiomas
 */
export const LOCALE_MAP = {
  pt: 'pt-BR',
  en: 'en-US',
  es: 'es-ES',
} as const;

export type SupportedLocale = keyof typeof LOCALE_MAP;
