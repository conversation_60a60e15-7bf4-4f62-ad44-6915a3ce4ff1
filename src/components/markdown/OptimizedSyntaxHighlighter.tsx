import { Copy } from 'lucide-react';
import { useState } from 'react';

interface OptimizedSyntaxHighlighterProps {
  children: string;
  language: string;
  className?: string;
}

/**
 * Componente otimizado para syntax highlighting
 * Evita o carregamento massivo de chunks do react-syntax-highlighter
 */
const OptimizedSyntaxHighlighter: React.FC<OptimizedSyntaxHighlighterProps> = ({
  children,
  language,
  className = '',
}) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Mapeamento de cores para diferentes tipos de tokens
  const getTokenColor = (token: string): string => {
    // Keywords
    if (/^(const|let|var|function|class|if|else|for|while|return|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|extends|implements|interface|type|enum|namespace|public|private|protected|static|readonly|abstract)$/.test(token)) {
      return 'text-purple-400';
    }
    
    // Strings
    if (/^["'`].*["'`]$/.test(token)) {
      return 'text-green-400';
    }
    
    // Numbers
    if (/^\d+(\.\d+)?$/.test(token)) {
      return 'text-orange-400';
    }
    
    // Comments
    if (/^(\/\/|\/\*|\*\/|#)/.test(token)) {
      return 'text-gray-500';
    }
    
    // Operators
    if (/^[+\-*/%=<>!&|^~?:;,.]$/.test(token)) {
      return 'text-cyan-400';
    }
    
    // Brackets
    if (/^[(){}\[\]]$/.test(token)) {
      return 'text-yellow-400';
    }
    
    return 'text-gray-300';
  };

  // Tokenização simples para syntax highlighting básico
  const tokenize = (code: string): Array<{ token: string; type: string }> => {
    const tokens: Array<{ token: string; type: string }> = [];
    const regex = /("(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*'|`(?:[^`\\]|\\.)*`|\/\/.*$|\/\*[\s\S]*?\*\/|#.*$|\b\d+(?:\.\d+)?\b|\b[a-zA-Z_$][a-zA-Z0-9_$]*\b|[+\-*/%=<>!&|^~?:;,.()]|[\s]+)/gm;
    
    let match;
    while ((match = regex.exec(code)) !== null) {
      const token = match[0];
      if (token.trim()) {
        tokens.push({ token, type: getTokenColor(token) });
      } else {
        tokens.push({ token, type: 'text-gray-300' });
      }
    }
    
    return tokens;
  };

  const tokens = tokenize(children);

  return (
    <div className={`relative group my-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between bg-adaptive-card border border-adaptive rounded-t-lg px-4 py-2">
        <span className="text-adaptive-secondary text-sm font-medium font-display">
          {language}
        </span>
        <button
          onClick={copyToClipboard}
          className="flex items-center gap-2 text-adaptive-secondary hover:text-adaptive-blue transition-colors text-sm font-display"
          title="Copy code">
          <Copy className="h-4 w-4" />
          {copied ? 'Copied!' : 'Copy'}
        </button>
      </div>

      {/* Code Block */}
      <div className="bg-gray-900 border border-t-0 border-adaptive rounded-b-lg overflow-x-auto">
        <pre className="p-4 text-sm leading-relaxed font-mono">
          <code>
            {tokens.map((token, index) => (
              <span key={index} className={token.type}>
                {token.token}
              </span>
            ))}
          </code>
        </pre>
      </div>
    </div>
  );
};

export default OptimizedSyntaxHighlighter;
