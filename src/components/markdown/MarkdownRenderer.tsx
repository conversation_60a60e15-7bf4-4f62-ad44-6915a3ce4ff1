import { AlertTriangle, ExternalLink, Shield } from 'lucide-react';
import type { Components } from 'react-markdown';
import ReactMarkdown from 'react-markdown';
import remarkFrontmatter from 'remark-frontmatter';
import remarkGfm from 'remark-gfm';
import { useSanitizedMarkdown } from '../../hooks/useSanitizedMarkdown';
import OptimizedSyntaxHighlighter from './OptimizedSyntaxHighlighter';
import SvgRenderer from './SvgRenderer';

// 🎯 TypeScript interfaces
interface MarkdownRendererProps {
  content: string;
  className?: string;
  isLegacyContent?: boolean; // Novo: identifica posts já publicados
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  isLegacyContent = false, // Por padrão, considera conteúdo novo
}) => {
  // 🛡️ Validar conteúdo contra ataques XSS (modo strict)
  const {
    content: sanitizedContent,
    isClean,
    isBlocked,
    blockReason,
  } = useSanitizedMarkdown(content, {
    allowHtml: false,
    allowLinks: true,
    allowImages: true,
    strictMode: false, // Bloqueia conteúdo perigoso ao invés de sanitizar
    isLegacyContent, // Passa a flag de conteúdo legado
    trustedDomains: [
      'github.com',
      'githubusercontent.com',
      'imgur.com',
      'unsplash.com',
      'pexels.com',
      'pixabay.com',
      'cloudinary.com',
      'vercel.app',
      'netlify.app',
      window.location.hostname, // Permitir imagens do próprio domínio
    ],
  });

  const components: Components = {
    // Headings
    h1: ({ children, ...props }) => (
      <h1
        className="text-3xl font-bold text-cyber-text mb-6 mt-8 first:mt-0"
        {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }) => (
      <h2
        className="text-2xl font-semibold text-cyber-text mb-4 mt-6"
        {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }) => (
      <h3
        className="text-xl font-semibold text-cyber-text mb-3 mt-4"
        {...props}>
        {children}
      </h3>
    ),
    h4: ({ children, ...props }) => (
      <h4
        className="text-lg font-semibold text-cyber-text mb-2 mt-3"
        {...props}>
        {children}
      </h4>
    ),

    // Paragraphs
    p: ({ children, ...props }) => (
      <p className="text-cyber-muted leading-relaxed mb-4" {...props}>
        {children}
      </p>
    ),

    // Links
    a: ({ href, children, ...props }) => {
      const isExternal = href?.startsWith('http');
      return (
        <a
          href={href}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          className="text-neon-cyan hover:text-neon-green transition-colors inline-flex items-center gap-1"
          {...props}>
          {children}
          {isExternal && <ExternalLink className="h-3 w-3" />}
        </a>
      );
    },

    // Lists
    ul: ({ children, ...props }) => (
      <ul
        className="list-disc list-inside text-cyber-muted mb-4 space-y-1"
        {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }) => (
      <ol
        className="list-decimal list-inside text-cyber-muted mb-4 space-y-1"
        {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }) => (
      <li className="ml-4" {...props}>
        {children}
      </li>
    ),

    // Blockquote
    blockquote: ({ children, ...props }) => (
      <blockquote
        className="border-l-4 border-neon-cyan pl-4 py-2 my-6 bg-neon-cyan/5 italic text-cyber-text"
        {...props}>
        {children}
      </blockquote>
    ),

    // Code blocks
    code: ({ node, className, children, ...props }: any) => {
      const inline = !className;
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';

      if (!inline && match) {
        // Se for SVG, usar o SvgRenderer
        if (language === 'svg') {
          return (
            <SvgRenderer className={className} {...props}>
              {children}
            </SvgRenderer>
          );
        }

        // Para outras linguagens, usar OptimizedSyntaxHighlighter
        return (
          <OptimizedSyntaxHighlighter language={language} className={className}>
            {String(children).replace(/\n$/, '')}
          </OptimizedSyntaxHighlighter>
        );
      }

      return (
        <code
          className="bg-cyber-card text-cyber-text px-1.5 py-0.5 rounded text-sm border border-cyber-border"
          {...props}>
          {children}
        </code>
      );
    },

    // Images
    img: ({ src, alt, ...props }) => (
      <div className="my-6">
        <img
          src={src}
          alt={alt}
          className="w-full rounded-lg border border-adaptive"
          loading="lazy"
          {...props}
        />
        {alt && (
          <p className="text-center text-sm text-cyber-muted mt-2 italic">
            {alt}
          </p>
        )}
      </div>
    ),

    // Tables
    table: ({ children, ...props }) => (
      <div className="overflow-x-auto my-6">
        <table
          className="w-full border-collapse border border-adaptive"
          {...props}>
          {children}
        </table>
      </div>
    ),
    thead: ({ children, ...props }) => (
      <thead className="bg-cyber-card" {...props}>
        {children}
      </thead>
    ),
    th: ({ children, ...props }) => (
      <th
        className="border border-cyber-border px-4 py-2 text-left font-semibold text-cyber-text"
        {...props}>
        {children}
      </th>
    ),
    td: ({ children, ...props }) => (
      <td
        className="border border-cyber-border px-4 py-2 text-cyber-muted"
        {...props}>
        {children}
      </td>
    ),

    // Horizontal rule
    hr: ({ ...props }) => (
      <hr className="border-cyber-border my-8" {...props} />
    ),

    // Strong and emphasis
    strong: ({ children, ...props }) => (
      <strong className="font-semibold text-neon-cyan" {...props}>
        {children}
      </strong>
    ),
    em: ({ children, ...props }) => (
      <em className="italic text-neon-green" {...props}>
        {children}
      </em>
    ),
  };

  // Se conteúdo foi bloqueado, mostrar erro
  if (isBlocked) {
    return (
      <div className={`prose prose-invert max-w-none ${className}`}>
        <div className="p-6 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-400 mb-2">
            Conteúdo Bloqueado por Segurança
          </h3>
          <p className="text-red-300 text-sm mb-4">
            {blockReason ||
              'Este conteúdo contém elementos potencialmente perigosos'}
          </p>
          <p className="text-red-200 text-xs">
            Por favor, remova os elementos de segurança e tente novamente.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`prose prose-invert max-w-none ${className}`}>
      {/* Indicador de conteúdo seguro - apenas se limpo */}
      {isClean && (
        <div className="mb-4 flex items-center gap-2 text-green-400 text-xs">
          <Shield className="h-3 w-3" />
          <span>Conteúdo verificado e seguro</span>
        </div>
      )}

      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkFrontmatter]}
        components={components}>
        {sanitizedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
