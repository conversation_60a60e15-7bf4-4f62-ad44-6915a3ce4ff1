/**
 * @fileoverview Error Boundary com logging automático para captura de erros
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { Component, type ErrorInfo, type ReactNode } from 'react';
import { ComponentLogger } from '../utils/component-logger';
import { Button } from './ui';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Error Boundary que captura erros JavaScript em qualquer lugar da árvore de componentes
 * e registra informações detalhadas sobre o erro
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Atualiza o state para mostrar a UI de erro
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log do erro com informações detalhadas
    ComponentLogger.logError('ErrorBoundary', error, {
      ...errorInfo,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      stack: error.stack,
    });

    // Callback personalizado se fornecido
    this.props.onError?.(error, errorInfo);

    // Atualizar state com informações do erro
    this.setState({ errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // UI de fallback personalizada ou padrão
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-cyber-bg text-cyber-text p-6">
          <div className="max-w-md w-full bg-cyber-surface border border-cyber-border rounded-lg p-6 text-center">
            <div className="text-6xl mb-4">⚠️</div>

            <h1 className="text-2xl font-bold mb-4 text-neon-cyan">
              Oops! Algo deu errado
            </h1>

            <p className="text-cyber-muted mb-6">
              Ocorreu um erro inesperado. Nossa equipe foi notificada e está
              trabalhando para resolver o problema.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-neon-cyan hover:text-neon-cyan/80 mb-2">
                  Detalhes do erro (desenvolvimento)
                </summary>
                <div className="bg-cyber-bg p-4 rounded border text-xs font-mono overflow-auto max-h-40">
                  <p className="text-red-400 mb-2">
                    <strong>Erro:</strong> {this.state.error.message}
                  </p>
                  {this.state.error.stack && (
                    <pre className="text-cyber-muted whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                  )}
                </div>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={this.handleReset}
                variant="outline"
                className="flex-1">
                Tentar Novamente
              </Button>

              <Button onClick={this.handleReload} className="flex-1">
                Recarregar Página
              </Button>
            </div>

            <p className="text-xs text-cyber-muted mt-4">
              Se o problema persistir, entre em contato com o suporte.
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook para capturar erros em componentes funcionais
 * Útil para componentes que precisam de tratamento de erro específico
 */
export function useErrorHandler() {
  return (error: Error, errorInfo?: any) => {
    ComponentLogger.logError('useErrorHandler', error, {
      ...errorInfo,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    });
  };
}

/**
 * HOC que adiciona Error Boundary a qualquer componente
 */
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const ComponentWithErrorBoundary = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  ComponentWithErrorBoundary.displayName = `withErrorBoundary(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return ComponentWithErrorBoundary;
}

export default ErrorBoundary;
