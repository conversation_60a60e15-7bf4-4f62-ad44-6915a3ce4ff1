/**
 * @fileoverview Notificações de Erro em Tempo Real - Fase 3 do Sistema de Logging
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { AlertTriangle, X } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useState } from 'react';
// import { LogLevel } from '../../utils/logger';
import { useErrorLogs } from '../../hooks/useDebugLogs';

// ============================================================================
// INTERFACES
// ============================================================================

interface ErrorNotificationsProps {
  className?: string;
  maxNotifications?: number;
  autoHideDelay?: number;
}

interface ErrorNotification {
  id: string;
  message: string;
  timestamp: Date;
  context?: any;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Componente para mostrar notificações de erro em tempo real
 */
export const ErrorNotifications: React.FC<ErrorNotificationsProps> = ({
  className,
  maxNotifications = 3,
  autoHideDelay = 10000, // 10 segundos
}) => {
  const { filteredLogs } = useErrorLogs();
  const [notifications, setNotifications] = useState<ErrorNotification[]>([]);
  const [lastLogCount, setLastLogCount] = useState(0);

  // Detectar novos erros
  useEffect(() => {
    const currentLogCount = filteredLogs.length;

    if (currentLogCount > lastLogCount) {
      // Novos logs de erro detectados
      const newLogs = filteredLogs.slice(0, currentLogCount - lastLogCount);

      const newNotifications = newLogs.map((log) => ({
        id: `${log.sessionId}-${log.timestamp.getTime()}`,
        message: log.message,
        timestamp: log.timestamp,
        context: log.context,
      }));

      setNotifications((prev) => {
        const updated = [...newNotifications, ...prev];
        return updated.slice(0, maxNotifications);
      });
    }

    setLastLogCount(currentLogCount);
  }, [filteredLogs, lastLogCount, maxNotifications]);

  // Auto-hide notifications
  useEffect(() => {
    if (notifications.length === 0) return;

    const timer = setTimeout(() => {
      setNotifications((prev) => prev.slice(0, -1));
    }, autoHideDelay);

    return () => clearTimeout(timer);
  }, [notifications, autoHideDelay]);

  // Remover notificação manualmente
  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={clsx('fixed top-4 right-4 z-50 space-y-2', className)}>
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg max-w-sm">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="text-sm font-medium text-red-800">
                    Erro Detectado
                  </h4>
                  <button
                    onClick={() => removeNotification(notification.id)}
                    className="text-red-400 hover:text-red-600 transition-colors">
                    <X className="h-4 w-4" />
                  </button>
                </div>

                <p className="text-sm text-red-700 mb-2 line-clamp-2">
                  {notification.message}
                </p>

                <div className="text-xs text-red-600">
                  {notification.timestamp.toLocaleTimeString()}
                  {notification.context?.component && (
                    <span className="ml-2 px-1.5 py-0.5 bg-red-100 rounded">
                      {notification.context.component}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// ============================================================================
// COMPONENTE DE BADGE DE ERRO
// ============================================================================

/**
 * Badge simples para mostrar contagem de erros
 */
export const ErrorBadge: React.FC<{ className?: string }> = ({ className }) => {
  const { stats } = useErrorLogs();

  if (stats.errors === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      className={clsx(
        'inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium',
        className
      )}>
      <AlertTriangle className="h-3 w-3" />
      {stats.errors} {stats.errors === 1 ? 'erro' : 'erros'}
    </motion.div>
  );
};

// ============================================================================
// COMPONENTE DE STATUS GLOBAL
// ============================================================================

/**
 * Indicador de status global do sistema
 */
export const SystemStatusIndicator: React.FC<{ className?: string }> = ({
  className,
}) => {
  const { stats } = useErrorLogs();

  const errorRate = stats.total > 0 ? (stats.errors / stats.total) * 100 : 0;
  const isHealthy = errorRate < 5;
  const isCritical = errorRate > 20;

  const statusConfig = {
    healthy: {
      color: 'bg-green-500',
      label: 'Sistema Saudável',
      textColor: 'text-green-700',
    },
    warning: {
      color: 'bg-yellow-500',
      label: 'Atenção',
      textColor: 'text-yellow-700',
    },
    critical: {
      color: 'bg-red-500',
      label: 'Crítico',
      textColor: 'text-red-700',
    },
  };

  const status = isCritical ? 'critical' : isHealthy ? 'healthy' : 'warning';
  const config = statusConfig[status];

  return (
    <div className={clsx('flex items-center gap-2', className)}>
      <div
        className={clsx(
          'w-3 h-3 rounded-full',
          config.color,
          !isHealthy && 'animate-pulse'
        )}
      />

      <span className={clsx('text-sm font-medium', config.textColor)}>
        {config.label}
      </span>

      {!isHealthy && (
        <span className="text-xs text-gray-600">
          ({errorRate.toFixed(1)}% erros)
        </span>
      )}
    </div>
  );
};

export default ErrorNotifications;
