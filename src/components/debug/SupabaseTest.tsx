import React, { useEffect, useState } from 'react';
import { supabase } from '../../lib/supabaseClient';

interface SupabaseTestProps {
  className?: string;
}

export const SupabaseTest: React.FC<SupabaseTestProps> = ({ className }) => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('🔍 [SupabaseTest] Testando conexão...');
        
        // Teste 1: Verificar se o cliente está configurado
        if (!supabase) {
          throw new Error('Cliente Supabase não configurado');
        }

        // Teste 2: Buscar posts
        const { data: posts, error: postsError } = await supabase
          .from('posts')
          .select('id, title, slug, status, created_at')
          .limit(5);

        if (postsError) {
          throw new Error(`Erro ao buscar posts: ${postsError.message}`);
        }

        // Teste 3: Buscar categories
        const { data: categories, error: categoriesError } = await supabase
          .from('categories')
          .select('id, name, slug')
          .limit(5);

        if (categoriesError) {
          console.warn('⚠️ Erro ao buscar categories:', categoriesError.message);
        }

        // Teste 4: Buscar profiles
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, name, email')
          .limit(5);

        if (profilesError) {
          console.warn('⚠️ Erro ao buscar profiles:', profilesError.message);
        }

        setData({
          posts: posts || [],
          categories: categories || [],
          profiles: profiles || [],
        });
        setStatus('success');
        
        console.log('✅ [SupabaseTest] Conexão bem-sucedida!', {
          posts: posts?.length || 0,
          categories: categories?.length || 0,
          profiles: profiles?.length || 0,
        });

      } catch (err: any) {
        console.error('❌ [SupabaseTest] Erro na conexão:', err);
        setError(err.message);
        setStatus('error');
      }
    };

    testConnection();
  }, []);

  if (status === 'loading') {
    return (
      <div className={`p-4 bg-gray-900 border border-gray-700 rounded-lg ${className}`}>
        <h3 className="text-lg font-bold text-neon-cyan mb-2">🔍 Testando Supabase...</h3>
        <div className="animate-pulse text-gray-400">Conectando ao banco de dados...</div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className={`p-4 bg-red-900/20 border border-red-500 rounded-lg ${className}`}>
        <h3 className="text-lg font-bold text-red-400 mb-2">❌ Erro na Conexão</h3>
        <p className="text-red-300 text-sm">{error}</p>
        <div className="mt-2 text-xs text-gray-400">
          Verifique as variáveis de ambiente VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 bg-green-900/20 border border-green-500 rounded-lg ${className}`}>
      <h3 className="text-lg font-bold text-green-400 mb-2">✅ Supabase Conectado!</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-300">Posts encontrados:</span>
          <span className="text-neon-cyan font-bold">{data.posts.length}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-300">Categories encontradas:</span>
          <span className="text-neon-cyan font-bold">{data.categories.length}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-300">Profiles encontrados:</span>
          <span className="text-neon-cyan font-bold">{data.profiles.length}</span>
        </div>
      </div>

      {data.posts.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-bold text-neon-cyan mb-2">Posts Recentes:</h4>
          <div className="space-y-1">
            {data.posts.slice(0, 3).map((post: any) => (
              <div key={post.id} className="text-xs text-gray-400">
                • {post.title} ({post.status})
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        Conexão testada em {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
};
