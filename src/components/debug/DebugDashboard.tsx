/**
 * @fileoverview Dashboard de Debug - Fase 3 do Sistema de Logging
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import {
  AlertTriangle,
  Bug,
  Clock,
  Download,
  Filter,
  Info,
  RefreshCw,
  Search,
  Trash2,
  User,
  Zap,
} from 'lucide-react';
import { motion } from 'motion/react';
import { useState } from 'react';
import { useDebugLogs } from '../../hooks/useDebugLogs';
import { LogLevel, type LogEntry } from '../../utils/logger';
import { Button, Card, CardContent, CardHeader } from '../ui';

// ============================================================================
// INTERFACES
// ============================================================================

interface DebugDashboardProps {
  className?: string;
}

interface LogFilters {
  level?: LogLevel;
  search?: string;
  timeRange?: 'all' | '1h' | '24h' | '7d';
  component?: string;
}

// ============================================================================
// COMPONENTES AUXILIARES
// ============================================================================

const LogLevelBadge: React.FC<{ level: LogLevel }> = ({ level }) => {
  const config = {
    [LogLevel.DEBUG]: {
      color: 'text-gray-400',
      bg: 'bg-gray-100',
      label: 'DEBUG',
    },
    [LogLevel.INFO]: {
      color: 'text-blue-600',
      bg: 'bg-blue-100',
      label: 'INFO',
    },
    [LogLevel.WARN]: {
      color: 'text-yellow-600',
      bg: 'bg-yellow-100',
      label: 'WARN',
    },
    [LogLevel.ERROR]: {
      color: 'text-red-600',
      bg: 'bg-red-100',
      label: 'ERROR',
    },
  };

  const { color, bg, label } = config[level];

  return (
    <span
      className={clsx('px-2 py-1 rounded-full text-xs font-medium', color, bg)}>
      {label}
    </span>
  );
};

const LogEntryCard: React.FC<{ entry: LogEntry; index: number }> = ({
  entry,
  index,
}) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
      className="border border-cyber-border rounded-lg p-4 bg-cyber-surface hover:bg-cyber-bg/50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-3 mb-2">
            <LogLevelBadge level={entry.level} />
            <span className="text-sm text-cyber-muted">
              {entry.timestamp.toLocaleTimeString()}
            </span>
            {entry.userId && (
              <span className="text-xs text-cyber-muted flex items-center gap-1">
                <User className="h-3 w-3" />
                {entry.userId.slice(0, 8)}...
              </span>
            )}
          </div>

          <h3 className="text-cyber-text font-medium mb-1 truncate">
            {entry.message}
          </h3>

          {entry.context && (
            <div className="text-sm text-cyber-muted">
              {entry.context.component && (
                <span className="inline-block bg-cyber-bg px-2 py-1 rounded mr-2">
                  {entry.context.component}
                </span>
              )}
              {entry.context.type && (
                <span className="text-xs">{entry.context.type}</span>
              )}
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setExpanded(!expanded)}
          className="ml-2">
          {expanded ? 'Menos' : 'Mais'}
        </Button>
      </div>

      {expanded && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          className="mt-4 pt-4 border-t border-cyber-border">
          <div className="space-y-2 text-sm">
            <div>
              <strong className="text-cyber-text">URL:</strong>
              <span className="text-cyber-muted ml-2">{entry.url}</span>
            </div>

            <div>
              <strong className="text-cyber-text">Session ID:</strong>
              <span className="text-cyber-muted ml-2 font-mono">
                {entry.sessionId}
              </span>
            </div>

            {entry.context && (
              <div>
                <strong className="text-cyber-text">Context:</strong>
                <pre className="mt-2 p-3 bg-cyber-bg rounded text-xs overflow-x-auto">
                  {JSON.stringify(entry.context, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Dashboard de Debug para visualizar logs do sistema
 */
export const DebugDashboard: React.FC<DebugDashboardProps> = ({
  className,
}) => {
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Usar o hook de debug logs
  const {
    filteredLogs,
    stats,
    filters,
    setFilters,
    clearFilters,
    refreshLogs,
    exportLogs,
    clearLogs,
  } = useDebugLogs(autoRefresh);

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cyber-text flex items-center gap-3">
            <Bug className="h-8 w-8 text-neon-cyan" />
            Debug Dashboard
          </h1>
          <p className="text-cyber-muted mt-2">
            Monitoramento e debug do sistema em tempo real
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            leftIcon={
              autoRefresh ? (
                <Zap className="h-4 w-4" />
              ) : (
                <Clock className="h-4 w-4" />
              )
            }>
            {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={refreshLogs}
            leftIcon={<RefreshCw className="h-4 w-4" />}>
            Atualizar
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={exportLogs}
            leftIcon={<Download className="h-4 w-4" />}>
            Exportar
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={clearLogs}
            leftIcon={<Trash2 className="h-4 w-4" />}>
            Limpar
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyber-muted">Total</p>
                <p className="text-2xl font-bold text-cyber-text">
                  {stats.total}
                </p>
              </div>
              <Info className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyber-muted">Errors</p>
                <p className="text-2xl font-bold text-red-500">
                  {stats.errors}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyber-muted">Warnings</p>
                <p className="text-2xl font-bold text-yellow-500">
                  {stats.warnings}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyber-muted">Info</p>
                <p className="text-2xl font-bold text-blue-500">{stats.info}</p>
              </div>
              <Info className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyber-muted">Debug</p>
                <p className="text-2xl font-bold text-gray-500">
                  {stats.debug}
                </p>
              </div>
              <Bug className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-cyber-text flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </h2>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Busca */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-2">
                Buscar
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-cyber-muted" />
                <input
                  type="text"
                  placeholder="Buscar logs..."
                  value={filters.search || ''}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value })
                  }
                  className="w-full pl-10 pr-4 py-2 border border-cyber-border rounded-lg bg-cyber-bg text-cyber-text placeholder-cyber-muted focus:outline-none focus:ring-2 focus:ring-neon-cyan"
                />
              </div>
            </div>

            {/* Nível */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-2">
                Nível
              </label>
              <select
                value={filters.level ?? ''}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    level: e.target.value
                      ? (Number(e.target.value) as LogLevel)
                      : undefined,
                  })
                }
                className="w-full px-3 py-2 border border-cyber-border rounded-lg bg-cyber-bg text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan">
                <option value="">Todos</option>
                <option value={LogLevel.DEBUG}>Debug</option>
                <option value={LogLevel.INFO}>Info</option>
                <option value={LogLevel.WARN}>Warning</option>
                <option value={LogLevel.ERROR}>Error</option>
              </select>
            </div>

            {/* Período */}
            <div>
              <label className="block text-sm font-medium text-cyber-text mb-2">
                Período
              </label>
              <select
                value={filters.timeRange || 'all'}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    timeRange: e.target.value as LogFilters['timeRange'],
                  })
                }
                className="w-full px-3 py-2 border border-cyber-border rounded-lg bg-cyber-bg text-cyber-text focus:outline-none focus:ring-2 focus:ring-neon-cyan">
                <option value="all">Todos</option>
                <option value="1h">Última hora</option>
                <option value="24h">Últimas 24h</option>
                <option value="7d">Últimos 7 dias</option>
              </select>
            </div>

            {/* Limpar filtros */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={clearFilters}
                className="w-full">
                Limpar Filtros
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Logs */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-cyber-text">
              Logs ({filteredLogs.length})
            </h2>
            <div className="text-sm text-cyber-muted">
              {autoRefresh && (
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  Atualizando automaticamente
                </span>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredLogs.length === 0 ? (
            <div className="text-center py-12">
              <Bug className="h-12 w-12 text-cyber-muted mx-auto mb-4" />
              <p className="text-cyber-muted">Nenhum log encontrado</p>
              <p className="text-sm text-cyber-muted mt-2">
                Ajuste os filtros ou aguarde novos logs serem gerados
              </p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredLogs.slice(0, 50).map((entry, index) => (
                <LogEntryCard
                  key={`${entry.sessionId}-${entry.timestamp.getTime()}`}
                  entry={entry}
                  index={index}
                />
              ))}

              {filteredLogs.length > 50 && (
                <div className="text-center py-4 border-t border-cyber-border">
                  <p className="text-sm text-cyber-muted">
                    Mostrando 50 de {filteredLogs.length} logs. Use os filtros
                    para refinar a busca.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugDashboard;
