/**
 * @fileoverview Widget de Logs para Dashboard - Fase 3 do Sistema de Logging
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { AlertTriangle, Bug, Info, TrendingUp } from 'lucide-react';
import { Link } from 'react-router';
import { useLogStats } from '../../hooks/useDebugLogs';
import { Card, CardContent, CardHeader } from '../ui';

// ============================================================================
// INTERFACES
// ============================================================================

interface LogsWidgetProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Widget de estatísticas de logs para o dashboard
 */
export const LogsWidget: React.FC<LogsWidgetProps> = ({ className }) => {
  const stats = useLogStats();

  // Calcular tendência (simplificado - baseado na proporção de erros)
  const errorRate = stats.total > 0 ? (stats.errors / stats.total) * 100 : 0;
  const isHealthy = errorRate < 5; // Menos de 5% de erros é considerado saudável

  return (
    <Card className={clsx('hover:shadow-lg transition-shadow', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-cyber-text flex items-center gap-2">
            <Bug className="h-5 w-5 text-neon-cyan" />
            System Logs
          </h3>

          <div
            className={clsx(
              'flex items-center gap-1 text-sm',
              isHealthy ? 'text-green-500' : 'text-red-500'
            )}>
            <TrendingUp className="h-4 w-4" />
            {isHealthy ? 'Healthy' : 'Issues'}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* Estatísticas principais */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-cyber-text">
                {stats.total}
              </div>
              <div className="text-sm text-cyber-muted">Total Logs</div>
            </div>

            <div className="text-center">
              <div
                className={clsx(
                  'text-2xl font-bold',
                  stats.errors > 0 ? 'text-red-500' : 'text-green-500'
                )}>
                {stats.errors}
              </div>
              <div className="text-sm text-cyber-muted">Errors</div>
            </div>
          </div>

          {/* Breakdown por tipo */}
          <div className="space-y-2">
            {/* Errors */}
            {stats.errors > 0 && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-cyber-text">Errors</span>
                </div>
                <span className="text-red-500 font-medium">{stats.errors}</span>
              </div>
            )}

            {/* Warnings */}
            {stats.warnings > 0 && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span className="text-cyber-text">Warnings</span>
                </div>
                <span className="text-yellow-500 font-medium">
                  {stats.warnings}
                </span>
              </div>
            )}

            {/* Info */}
            {stats.info > 0 && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span className="text-cyber-text">Info</span>
                </div>
                <span className="text-blue-500 font-medium">{stats.info}</span>
              </div>
            )}

            {/* Debug */}
            {stats.debug > 0 && (
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Bug className="h-4 w-4 text-gray-500" />
                  <span className="text-cyber-text">Debug</span>
                </div>
                <span className="text-gray-500 font-medium">{stats.debug}</span>
              </div>
            )}
          </div>

          {/* Status geral */}
          <div
            className={clsx(
              'p-3 rounded-lg text-center text-sm',
              isHealthy
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-red-100 text-red-800 border border-red-200'
            )}>
            {isHealthy ? (
              <>
                <div className="font-medium">Sistema Saudável</div>
                <div className="text-xs mt-1">
                  Taxa de erro: {errorRate.toFixed(1)}%
                </div>
              </>
            ) : (
              <>
                <div className="font-medium">Atenção Necessária</div>
                <div className="text-xs mt-1">
                  Taxa de erro: {errorRate.toFixed(1)}%
                </div>
              </>
            )}
          </div>

          {/* Link para dashboard completo */}
          <Link
            to="/admin/debug"
            className="block w-full text-center py-2 px-4 bg-cyber-surface hover:bg-cyber-bg border border-cyber-border rounded-lg text-cyber-text hover:text-neon-cyan transition-colors text-sm">
            Ver Dashboard Completo
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// COMPONENTE COMPACTO
// ============================================================================

/**
 * Versão compacta do widget para sidebars
 */
export const LogsWidgetCompact: React.FC<LogsWidgetProps> = ({ className }) => {
  const stats = useLogStats();
  const errorRate = stats.total > 0 ? (stats.errors / stats.total) * 100 : 0;
  const isHealthy = errorRate < 5;

  return (
    <div
      className={clsx(
        'p-4 rounded-lg border transition-colors',
        isHealthy
          ? 'bg-green-50 border-green-200 hover:bg-green-100'
          : 'bg-red-50 border-red-200 hover:bg-red-100',
        className
      )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bug
            className={clsx(
              'h-5 w-5',
              isHealthy ? 'text-green-600' : 'text-red-600'
            )}
          />
          <div>
            <div className="text-sm font-medium text-gray-900">System Logs</div>
            <div className="text-xs text-gray-600">
              {stats.total} total, {stats.errors} errors
            </div>
          </div>
        </div>

        <Link
          to="/admin/debug"
          className="text-xs text-blue-600 hover:text-blue-800">
          View →
        </Link>
      </div>
    </div>
  );
};

export default LogsWidget;
