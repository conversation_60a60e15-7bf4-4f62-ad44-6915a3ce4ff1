import { Helmet } from 'react-helmet-async';
import type { Post } from '../../types';

interface JsonLdArticleProps {
  post: Post;
  siteUrl?: string;
}

const JsonLdArticle: React.FC<JsonLdArticleProps> = ({ 
  post, 
  siteUrl = 'https://blueprintblog.tech' 
}) => {
  const articleUrl = `${siteUrl}/post/${post.slug}`;
  const imageUrl = post.thumbnail?.startsWith('http') 
    ? post.thumbnail 
    : `${siteUrl}${post.thumbnail}`;

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": imageUrl,
    "url": articleUrl,
    "datePublished": post.createdAt,
    "dateModified": post.updatedAt,
    "author": {
      "@type": "Person",
      "name": post.author.name,
      "email": post.author.email,
      "url": `${siteUrl}/author/${post.author.id}`,
      "description": post.author.bio,
      "jobTitle": "Desenvolvedor e Criador de Conteúdo",
      "worksFor": {
        "@type": "Organization",
        "name": "Blueprint Blog"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "Blueprint Blog",
      "description": "Blog sobre tecnologia, desenvolvimento e inovação",
      "url": siteUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${siteUrl}/images/logo.png`,
        "width": 200,
        "height": 200
      },
      "sameAs": [
        "https://github.com/blueprintblog",
        "https://twitter.com/blueprintblog",
        "https://linkedin.com/company/blueprintblog"
      ]
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": articleUrl
    },
    "articleSection": post.category.name,
    "keywords": post.tags?.map(tag => tag.name).join(', '),
    "wordCount": Math.ceil(post.content.length / 5), // Estimativa aproximada
    "timeRequired": `PT${post.readTime}M`, // ISO 8601 duration format
    "inLanguage": "pt-BR",
    "isAccessibleForFree": true,
    "genre": "Technology",
    "about": {
      "@type": "Thing",
      "name": post.category.name,
      "description": post.category.description || `Artigos sobre ${post.category.name.toLowerCase()}`
    },
    "mentions": post.tags?.map(tag => ({
      "@type": "Thing",
      "name": tag.name,
      "url": `${siteUrl}/tags/${tag.slug}`
    })),
    "interactionStatistic": [
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/ReadAction",
        "userInteractionCount": post.views
      },
      {
        "@type": "InteractionCounter", 
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": post.likes
      }
    ],
    "potentialAction": {
      "@type": "ReadAction",
      "target": articleUrl
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(jsonLd, null, 2)}
      </script>
    </Helmet>
  );
};

export default JsonLdArticle;
