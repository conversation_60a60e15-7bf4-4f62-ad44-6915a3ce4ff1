import { Helmet } from 'react-helmet-async';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface JsonLdBreadcrumbsProps {
  breadcrumbs: BreadcrumbItem[];
  siteUrl?: string;
}

const JsonLdBreadcrumbs: React.FC<JsonLdBreadcrumbsProps> = ({ 
  breadcrumbs, 
  siteUrl = 'https://blueprintblog.tech' 
}) => {
  // Garantir que as URLs sejam absolutas
  const normalizedBreadcrumbs = breadcrumbs.map(item => ({
    ...item,
    url: item.url.startsWith('http') ? item.url : `${siteUrl}${item.url}`
  }));

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": normalizedBreadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": {
        "@type": "WebPage",
        "@id": item.url,
        "name": item.name,
        "url": item.url
      }
    }))
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(jsonLd, null, 2)}
      </script>
    </Helmet>
  );
};

export default JsonLdBreadcrumbs;
