import { Helmet } from 'react-helmet-async';

interface JsonLdOrganizationProps {
  siteUrl?: string;
}

const JsonLdOrganization: React.FC<JsonLdOrganizationProps> = ({ 
  siteUrl = 'https://blueprintblog.tech' 
}) => {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Blueprint Blog",
    "alternateName": "Blueprint",
    "description": "Blog sobre tecnologia, desenvolvimento e inovação. Artigos sobre programação, design e tendências tech.",
    "url": siteUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${siteUrl}/images/logo.png`,
      "width": 200,
      "height": 200,
      "caption": "Blueprint Blog Logo"
    },
    "image": {
      "@type": "ImageObject",
      "url": `${siteUrl}/images/og-default.jpg`,
      "width": 1200,
      "height": 630,
      "caption": "Blueprint Blog - Tecnologia e Inovação"
    },
    "foundingDate": "2024",
    "founder": {
      "@type": "Person",
      "name": "Blueprint Team",
      "jobTitle": "Desenvolvedores e Criadores de Conteúdo"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["Portuguese", "English"]
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "BR",
      "addressLocality": "Brasil"
    },
    "sameAs": [
      "https://github.com/blueprintblog",
      "https://twitter.com/blueprintblog",
      "https://linkedin.com/company/blueprintblog",
      "https://instagram.com/blueprintblog",
      "https://youtube.com/@blueprintblog"
    ],
    "knowsAbout": [
      "Desenvolvimento Web",
      "Programação",
      "JavaScript",
      "TypeScript",
      "React",
      "Node.js",
      "Design",
      "UX/UI",
      "Tecnologia",
      "Inovação",
      "Inteligência Artificial",
      "Machine Learning",
      "DevOps",
      "Cloud Computing"
    ],
    "publishingPrinciples": `${siteUrl}/about`,
    "diversityPolicy": `${siteUrl}/diversity`,
    "ethicsPolicy": `${siteUrl}/ethics`,
    "masthead": `${siteUrl}/team`,
    "missionCoveragePrioritiesPolicy": `${siteUrl}/mission`,
    "actionableFeedbackPolicy": `${siteUrl}/feedback`,
    "correctionsPolicy": `${siteUrl}/corrections`,
    "ownershipFundingInfo": `${siteUrl}/funding`,
    "unnamedSourcesPolicy": `${siteUrl}/sources`,
    "verificationFactCheckingPolicy": `${siteUrl}/verification`,
    "mainEntity": {
      "@type": "WebSite",
      "name": "Blueprint Blog",
      "url": siteUrl,
      "description": "Blog sobre tecnologia, desenvolvimento e inovação",
      "inLanguage": "pt-BR",
      "isAccessibleForFree": true,
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${siteUrl}/search?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    },
    "department": [
      {
        "@type": "Organization",
        "name": "Desenvolvimento",
        "description": "Equipe responsável pelo desenvolvimento de software e aplicações"
      },
      {
        "@type": "Organization", 
        "name": "Conteúdo",
        "description": "Equipe responsável pela criação e curadoria de conteúdo técnico"
      },
      {
        "@type": "Organization",
        "name": "Design",
        "description": "Equipe responsável pelo design e experiência do usuário"
      }
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Serviços Blueprint Blog",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Artigos Técnicos",
            "description": "Conteúdo especializado sobre tecnologia e desenvolvimento"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Tutoriais",
            "description": "Guias práticos e tutoriais passo a passo"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Reviews",
            "description": "Análises de ferramentas e tecnologias"
          }
        }
      ]
    }
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(jsonLd, null, 2)}
      </script>
    </Helmet>
  );
};

export default JsonLdOrganization;
