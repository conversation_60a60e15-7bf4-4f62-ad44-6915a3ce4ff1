import type { Category, Post, Tag } from '../../types';
import JsonLdArticle from './JsonLdArticle';
import JsonLdBreadcrumbs from './JsonLdBreadcrumbs';
import JsonLdOrganization from './JsonLdOrganization';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface JsonLdSchemaProps {
  type: 'home' | 'blog' | 'post' | 'category' | 'tag' | 'page';
  post?: Post;
  category?: Category;
  tag?: Tag;
  breadcrumbs?: BreadcrumbItem[];
  siteUrl?: string;
}

const JsonLdSchema: React.FC<JsonLdSchemaProps> = ({
  type,
  post,
  breadcrumbs = [],
  siteUrl = 'https://blueprintblog.tech',
}) => {
  return (
    <>
      {/* Organization schema - sempre presente */}
      <JsonLdOrganization siteUrl={siteUrl} />

      {/* Article schema - apenas para posts */}
      {type === 'post' && post && (
        <JsonLdArticle post={post} siteUrl={siteUrl} />
      )}

      {/* Breadcrumbs schema - quando há navegação */}
      {breadcrumbs.length > 1 && (
        <JsonLdBreadcrumbs breadcrumbs={breadcrumbs} siteUrl={siteUrl} />
      )}
    </>
  );
};

export default JsonLdSchema;
