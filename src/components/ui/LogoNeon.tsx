/**
 * @fileoverview Componente LogoNeon - Logo com efeitos neon e glitch
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface LogoNeonProps extends BaseComponentProps {
  /**
   * Tamanho do logo
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Se deve mostrar o texto completo
   */
  showText?: boolean;

  /**
   * Se deve mostrar o subtítulo
   */
  showSubtitle?: boolean;

  /**
   * Se deve ter efeito de hover
   */
  hoverable?: boolean;

  /**
   * Se deve ter efeito glitch
   */
  glitchEffect?: boolean;

  /**
   * Intensidade do efeito neon
   */
  neonIntensity?: 'low' | 'medium' | 'high';

  /**
   * Função de clique
   */
  onClick?: () => void;
}

// ============================================================================
// CONSTANTES
// ============================================================================

/**
 * Configurações de tamanho
 */
const sizeConfig = {
  sm: {
    icon: 'w-8 h-8',
    text: 'text-lg',
    subtitle: 'text-xs',
    spacing: 'space-x-2',
  },
  md: {
    icon: 'w-10 h-10',
    text: 'text-xl',
    subtitle: 'text-xs',
    spacing: 'space-x-3',
  },
  lg: {
    icon: 'w-12 h-12',
    text: 'text-2xl',
    subtitle: 'text-sm',
    spacing: 'space-x-4',
  },
  xl: {
    icon: 'w-16 h-16',
    text: 'text-3xl',
    subtitle: 'text-base',
    spacing: 'space-x-5',
  },
};

/**
 * Configurações de intensidade neon
 */
const neonIntensityConfig = {
  low: {
    glow: 'drop-shadow-sm',
    shadow: 'shadow-neon-cyan/20',
    pulse: '',
  },
  medium: {
    glow: 'drop-shadow-md',
    shadow: 'shadow-neon-cyan/30',
    pulse: 'animate-pulse-neon',
  },
  high: {
    glow: 'drop-shadow-lg',
    shadow: 'shadow-neon-cyan/50',
    pulse: 'animate-pulse-neon',
  },
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Logo com efeitos neon e glitch cyberpunk
 */
export const LogoNeon: React.FC<LogoNeonProps> = ({
  className,
  size = 'md',
  showText = true,
  showSubtitle = true,
  hoverable = true,
  glitchEffect = false,
  neonIntensity = 'medium',
  onClick,
  ...props
}) => {
  const [isGlitching, setIsGlitching] = useState(false);

  const config = sizeConfig[size];
  const neonConfig = neonIntensityConfig[neonIntensity];

  /**
   * Trigger glitch effect
   */
  const triggerGlitch = () => {
    if (!glitchEffect) return;

    setIsGlitching(true);
    setTimeout(() => setIsGlitching(false), 300);
  };

  /**
   * Handle click
   */
  const handleClick = () => {
    triggerGlitch();
    onClick?.();
  };

  // Classes do container
  const containerClasses = twMerge(
    clsx(
      'flex items-center',
      config.spacing,
      hoverable && [
        'cursor-pointer',
        'transition-all duration-300',
        'hover:scale-105',
      ],
      onClick && 'cursor-pointer',
      className
    )
  );

  // Classes do ícone
  const iconClasses = twMerge(
    clsx(
      config.icon,
      'bg-gradient-neon rounded-lg',
      'flex items-center justify-center',
      'relative overflow-hidden',
      neonConfig.pulse,
      neonConfig.shadow,
      hoverable && [
        'hover:shadow-lg',
        'hover:shadow-neon-cyan/40',
        'transition-all duration-300',
      ],
      isGlitching && 'animate-glitch'
    )
  );

  // Classes do texto principal
  const textClasses = twMerge(
    clsx(
      config.text,
      'font-bold',
      'bg-gradient-neon bg-clip-text text-transparent',
      neonConfig.glow,
      hoverable && ['hover:text-shadow-neon', 'transition-all duration-300'],
      isGlitching && 'animate-glitch'
    )
  );

  // Classes do subtítulo
  const subtitleClasses = twMerge(
    clsx(
      config.subtitle,
      'text-cyber-muted',
      'font-mono',
      'tracking-wider',
      hoverable && ['hover:text-neon-cyan', 'transition-colors duration-300']
    )
  );

  return (
    <div
      className={containerClasses}
      onClick={handleClick}
      onMouseEnter={() => hoverable && Math.random() > 0.8 && triggerGlitch()}
      {...props}>
      {/* Ícone do Logo */}
      <div className={iconClasses}>
        {/* Background com efeito de circuito */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-1/2 h-px bg-cyber-bg"></div>
          <div className="absolute top-3/4 left-1/4 w-1/2 h-px bg-cyber-bg"></div>
          <div className="absolute top-1/4 left-1/2 w-px h-1/2 bg-cyber-bg"></div>
        </div>

        {/* Letra B */}
        <span
          className={clsx(
            'text-cyber-bg font-bold relative z-10',
            size === 'sm' && 'text-sm',
            size === 'md' && 'text-lg',
            size === 'lg' && 'text-xl',
            size === 'xl' && 'text-2xl'
          )}>
          B
        </span>

        {/* Efeito de scan line */}
        {hoverable && (
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-neon-cyan/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 animate-scan-vertical"></div>
        )}

        {/* Efeito glitch overlay */}
        {isGlitching && (
          <>
            <div className="absolute inset-0 bg-neon-magenta/30 mix-blend-multiply animate-glitch"></div>
            <div
              className="absolute inset-0 bg-neon-cyan/30 mix-blend-multiply animate-glitch"
              style={{ animationDelay: '0.1s' }}></div>
          </>
        )}
      </div>

      {/* Texto do Logo */}
      {showText && (
        <div className="hidden sm:block">
          <h1 className={textClasses}>
            BLUEPRINT
            {/* Efeito de data corruption */}
            {isGlitching && (
              <span className="absolute inset-0 text-neon-magenta opacity-50 animate-glitch">
                BL█EPR█NT
              </span>
            )}
          </h1>

          {showSubtitle && (
            <p className={subtitleClasses}>
              Blog v2.0
              {/* Efeito de scan no subtítulo */}
              {hoverable && (
                <span className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-cyan to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></span>
              )}
            </p>
          )}
        </div>
      )}

      {/* Partículas flutuantes (apenas em tamanhos maiores) */}
      {(size === 'lg' || size === 'xl') && hoverable && (
        <div className="absolute -inset-2 pointer-events-none">
          <div className="absolute top-0 left-0 w-1 h-1 bg-neon-cyan rounded-full opacity-60 animate-float"></div>
          <div
            className="absolute top-1/2 right-0 w-1 h-1 bg-neon-magenta rounded-full opacity-40 animate-float"
            style={{ animationDelay: '1s' }}></div>
          <div
            className="absolute bottom-0 left-1/3 w-1 h-1 bg-neon-purple rounded-full opacity-50 animate-float"
            style={{ animationDelay: '2s' }}></div>
        </div>
      )}
    </div>
  );
};

export default LogoNeon;
