/**
 * @fileoverview Componente BreakingNews - Ticker de notícias em carrossel
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import {
  FaPalette,
  FaReact,
  FaRobot,
  FaShieldAlt,
  FaTools,
} from 'react-icons/fa';
import { usePosts } from '../../hooks/usePosts';
import type { Post } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface BreakingNewsProps {
  className?: string;
  posts?: Post[];
  autoPlay?: boolean;
  speed?: number;
  pauseOnHover?: boolean;
}

interface NewsItem {
  id: string;
  title: string;
  category: string;
  icon: React.ReactNode;
  color: string;
  slug: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Ticker de breaking news com carrossel automático
 */
export const BreakingNews: React.FC<BreakingNewsProps> = ({
  className,
  posts: propsPosts = [],
  autoPlay = true,
  speed = 25,
  pauseOnHover = true,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Busca posts reais do Supabase se não foram fornecidos via props
  const { posts: supabasePosts, loading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
    limit: 6,
  });

  // Usa posts das props ou do Supabase
  const posts = propsPosts.length > 0 ? propsPosts : supabasePosts;

  // Converte posts em news items
  const newsItems: NewsItem[] = posts.slice(0, 6).map((post: Post) => {
    // Mapeia categorias para ícones
    const getCategoryIcon = (categoryName: string) => {
      const name = categoryName.toLowerCase();
      if (name.includes('ia') || name.includes('ai')) return <FaRobot />;
      if (name.includes('frontend') || name.includes('css'))
        return <FaPalette />;
      if (name.includes('react')) return <FaReact />;
      if (name.includes('segurança') || name.includes('security'))
        return <FaShieldAlt />;
      if (name.includes('devops') || name.includes('tools')) return <FaTools />;
      return '📁'; // Ícone padrão
    };

    return {
      id: post.id,
      title: post.title,
      category: post.category.name,
      icon: getCategoryIcon(post.category.name),
      color: post.category.color,
      slug: post.slug,
    };
  });

  // Fallback apenas se não houver posts e não estiver carregando
  const defaultNews: NewsItem[] =
    !loading && newsItems.length === 0
      ? [
          {
            id: 'default-1',
            title: 'Bem-vindo ao Blueprint Blog',
            category: 'Geral',
            icon: <FaRobot />,
            color: '#00ffff',
            slug: 'bem-vindo',
          },
        ]
      : [];

  const allNews = newsItems.length > 0 ? newsItems : defaultNews;

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay || isPaused || allNews.length === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % allNews.length);
    }, speed * 1000);

    return () => clearInterval(interval);
  }, [autoPlay, isPaused, allNews.length, speed]);

  if (allNews.length === 0) return null;

  const currentNews = allNews[currentIndex];

  return (
    <section
      className={clsx(
        'relative bg-cyber-surface border-b border-cyber-border overflow-hidden',
        'before:absolute before:inset-0 before:bg-gradient-to-r before:from-neon-cyan/10 before:via-transparent before:to-neon-magenta/10',
        'shadow-lg shadow-cyber-bg/20',
        className
      )}
      onMouseEnter={() => pauseOnHover && setIsPaused(true)}
      onMouseLeave={() => pauseOnHover && setIsPaused(false)}>
      {/* Scanning line effect */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-cyan to-transparent animate-scan-horizontal opacity-50" />

      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Breaking Badge */}
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="bg-gradient-to-r from-neon-cyan to-neon-magenta text-cyber-bg px-4 py-1 rounded-full text-sm font-bold">
                BREAKING
              </div>
              <div className="absolute -inset-1 bg-gradient-to-r from-neon-cyan to-neon-magenta rounded-full blur opacity-15"></div>
            </div>

            {/* Current News */}
            <div className="flex items-center space-x-3 min-w-0 flex-1 group">
              <div
                className="text-lg flex-shrink-0 group-hover:scale-110 transition-transform duration-200"
                style={{
                  filter: `drop-shadow(0 0 3px ${currentNews.color})`,
                  color: currentNews.color,
                }}>
                {currentNews.icon}
              </div>

              <div className="min-w-0 flex-1">
                <a
                  href={`/post/${currentNews.slug}`}
                  className="text-cyber-text hover:text-neon-cyan transition-colors duration-300 font-medium truncate block hover:underline"
                  title={currentNews.title}>
                  {currentNews.title}
                </a>
              </div>

              <a
                href={`/categories/${currentNews.category
                  .toLowerCase()
                  .replace(/\s+/g, '-')}`}
                className="px-2 py-1 rounded text-xs font-medium flex-shrink-0 hover:opacity-80 transition-opacity duration-200"
                style={{
                  backgroundColor: `${currentNews.color}20`,
                  color: currentNews.color,
                  border: `1px solid ${currentNews.color}30`,
                }}
                title={`Ver mais posts de ${currentNews.category}`}>
                {currentNews.category}
              </a>
            </div>
          </div>

          {/* Navigation Dots */}
          <div className="hidden md:flex items-center space-x-2 ml-4">
            {allNews.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={clsx(
                  'w-2 h-2 rounded-full transition-all duration-300',
                  index === currentIndex
                    ? 'bg-neon-cyan shadow-lg shadow-neon-cyan/50'
                    : 'bg-cyber-border hover:bg-cyber-muted'
                )}
                aria-label={`Ir para notícia ${index + 1}`}
              />
            ))}
          </div>

          {/* Live indicator */}
          <div className="hidden lg:flex items-center space-x-2 ml-4">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-cyber-muted font-mono">LIVE</span>
          </div>
        </div>
      </div>

      {/* Bottom scanning line */}
      <div
        className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-magenta to-transparent animate-scan-horizontal opacity-30"
        style={{ animationDelay: '1.5s' }}
      />
    </section>
  );
};

export default BreakingNews;
