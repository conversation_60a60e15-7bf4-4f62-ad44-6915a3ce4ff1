/**
 * @fileoverview Componente Card com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface CardProps extends BaseComponentProps {
  /**
   * Se o card deve ter hover effect
   */
  hoverable?: boolean;

  /**
   * Se o card deve ter borda neon
   */
  neonBorder?: boolean;

  /**
   * Se o card deve ter padding
   */
  padding?: boolean;

  /**
   * Função de clique (torna o card clicável)
   */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
}

interface CardHeaderProps extends BaseComponentProps {
  /**
   * Título do header
   */
  title?: string;

  /**
   * Subtítulo do header
   */
  subtitle?: string;

  /**
   * Ações do header (botões, etc.)
   */
  actions?: React.ReactNode;
}

interface CardContentProps extends BaseComponentProps {}

interface CardFooterProps extends BaseComponentProps {
  /**
   * Se o footer deve ter borda superior
   */
  bordered?: boolean;
}

// ============================================================================
// ESTILOS
// ============================================================================

const cardVariants = {
  primary: [
    'bg-cyber-surface border border-cyber-border',
    'shadow-lg shadow-cyber-bg/20',
  ],
  secondary: [
    'bg-cyber-bg border border-cyber-border',
    'shadow-md shadow-cyber-surface/10',
  ],
  outline: [
    'bg-transparent border border-cyber-border',
    'shadow-md shadow-cyber-surface/10',
  ],
  success: [
    'bg-cyber-surface border border-neon-green/30',
    'shadow-lg shadow-neon-green/10',
  ],
  warning: [
    'bg-cyber-surface border border-neon-yellow/30',
    'shadow-lg shadow-neon-yellow/10',
  ],
  error: [
    'bg-cyber-surface border border-red-500/30',
    'shadow-lg shadow-red-500/10',
  ],
  info: [
    'bg-cyber-surface border border-neon-blue/30',
    'shadow-lg shadow-neon-blue/10',
  ],
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Componente Card principal
 */
export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      children,
      className,
      variant = 'primary',
      hoverable = false,
      neonBorder = false,
      padding = true,
      onClick,
      ...props
    },
    ref
  ) => {
    const baseStyles = [
      'rounded-lg overflow-hidden',
      'transition-all duration-300 ease-in-out',
      padding && 'p-6',
      hoverable && [
        'cursor-pointer',
        'hover:scale-[1.02]',
        'hover:shadow-xl',
        'active:scale-[0.98]',
      ],
      neonBorder && [
        'border-neon-cyan',
        'shadow-neon-cyan/20',
        'hover:shadow-neon-cyan/30',
      ],
      onClick && 'cursor-pointer',
    ];

    const variantStyles = cardVariants[variant] || cardVariants.primary;

    const cardClasses = twMerge(clsx(baseStyles, variantStyles, className));

    return (
      <div ref={ref} className={cardClasses} onClick={onClick} {...props}>
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// ============================================================================
// COMPONENTE HEADER
// ============================================================================

/**
 * Header do Card
 */
export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className, title, subtitle, actions, ...props }, ref) => {
    const headerClasses = twMerge(
      clsx(
        'flex items-start justify-between',
        'pb-4 mb-4 border-b border-cyber-border',
        className
      )
    );

    return (
      <div ref={ref} className={headerClasses} {...props}>
        <div className="flex-1">
          {title && (
            <h3 className="text-lg font-semibold text-cyber-text mb-1">
              {title}
            </h3>
          )}
          {subtitle && <p className="text-sm text-cyber-muted">{subtitle}</p>}
          {children}
        </div>

        {actions && <div className="flex-shrink-0 ml-4">{actions}</div>}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// ============================================================================
// COMPONENTE CONTENT
// ============================================================================

/**
 * Conteúdo do Card
 */
export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className, ...props }, ref) => {
    const contentClasses = twMerge(clsx('text-cyber-text', className));

    return (
      <div ref={ref} className={contentClasses} {...props}>
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// ============================================================================
// COMPONENTE FOOTER
// ============================================================================

/**
 * Footer do Card
 */
export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className, bordered = true, ...props }, ref) => {
    const footerClasses = twMerge(
      clsx(
        'flex items-center justify-between',
        'pt-4 mt-4',
        bordered && 'border-t border-cyber-border',
        className
      )
    );

    return (
      <div ref={ref} className={footerClasses} {...props}>
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

// ============================================================================
// EXPORTS
// ============================================================================

export default Card;
