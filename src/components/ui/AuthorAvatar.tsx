import React from 'react';
import BlueprintAvatar from './BlueprintAvatar';

interface AuthorAvatarProps {
  avatar: string;
  name: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const AuthorAvatar: React.FC<AuthorAvatarProps> = ({ 
  avatar, 
  name, 
  size = 'md', 
  className = '' 
}) => {
  // Se o avatar é 'blueprint-logo', usar o componente BlueprintAvatar
  if (avatar === 'blueprint-logo') {
    return <BlueprintAvatar size={size} className={className} />;
  }

  // Caso contrário, usar imagem normal
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  return (
    <img
      src={avatar}
      alt={name}
      className={`${sizeClasses[size]} ${className} rounded-full border-2 border-neon-cyan object-cover`}
    />
  );
};

export default AuthorAvatar;
