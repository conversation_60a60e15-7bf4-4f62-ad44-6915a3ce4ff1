/**
 * @fileoverview Componente FeaturedPosts - Seção de posts em destaque melhorada
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import type { Post } from '../../types';
import { But<PERSON>, Card } from './';

// ============================================================================
// INTERFACES
// ============================================================================

interface FeaturedPostsProps {
  className?: string;
  posts?: Post[];
  title?: string;
  subtitle?: string;
  showViewAll?: boolean;
  layout?: 'grid' | 'masonry' | 'hero';
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Seção de posts em destaque com layouts flexíveis
 */
export const FeaturedPosts: React.FC<FeaturedPostsProps> = ({
  className,
  posts = [],
  title = '🌟 Posts em Destaque',
  subtitle = 'Conteúdo selecionado especialmente para você',
  showViewAll = true,
  layout = 'hero',
}) => {
  if (posts.length === 0) return null;

  const mainPost = posts[0];
  const secondaryPosts = posts.slice(1, 4);

  const renderHeroLayout = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Post Principal */}
      <div className="lg:col-span-2">
        <Card
          className="group cursor-pointer h-full overflow-hidden"
          hoverable
          neonBorder>
          <div className="relative">
            {/* Featured Badge */}
            <div className="absolute top-4 left-4 z-10">
              <div className="bg-gradient-to-r from-neon-cyan to-neon-magenta text-cyber-bg px-3 py-1 rounded-full text-xs font-bold animate-pulse-neon">
                ⭐ DESTAQUE
              </div>
            </div>

            {/* Image */}
            <div className="aspect-[16/10] bg-gradient-cyber relative overflow-hidden">
              <img
                src={mainPost.thumbnail}
                alt={mainPost.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-cyber-bg/80 via-transparent to-transparent" />

              {/* Category */}
              <div className="absolute top-4 right-4">
                <span
                  className="px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm"
                  style={{
                    backgroundColor: `${mainPost.category.color}20`,
                    color: mainPost.category.color,
                    border: `1px solid ${mainPost.category.color}50`,
                  }}>
                  {mainPost.category.name}
                </span>
              </div>

              {/* Read Time */}
              <div className="absolute bottom-4 right-4 bg-cyber-bg/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-cyber-muted">
                {mainPost.readTime} min
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <h3 className="text-2xl font-bold text-cyber-text mb-3 group-hover:text-neon-cyan transition-colors line-clamp-2">
                {mainPost.title}
              </h3>

              <p className="text-cyber-muted mb-4 line-clamp-3">
                {mainPost.excerpt}
              </p>

              {/* Meta */}
              <div className="flex items-center justify-between text-sm text-cyber-muted">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <span>👤</span>
                    <span>{mainPost.author.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>📅</span>
                    <span>
                      {new Date(mainPost.date).toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1">
                    <span>👁️</span>
                    <span>{mainPost.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>❤️</span>
                    <span>{mainPost.likes}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Posts Secundários */}
      <div className="space-y-6">
        {secondaryPosts.map((post) => (
          <Card key={post.id} className="group cursor-pointer" hoverable>
            <div className="flex space-x-4">
              {/* Thumbnail */}
              <div className="w-24 h-24 bg-gradient-cyber rounded-lg relative overflow-hidden flex-shrink-0">
                <img
                  src={post.thumbnail}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-1 left-1">
                  <span
                    className="w-3 h-3 rounded-full inline-block"
                    style={{ backgroundColor: post.category.color }}
                  />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-cyber-text group-hover:text-neon-cyan transition-colors line-clamp-2 mb-2">
                  {post.title}
                </h4>

                <div className="flex items-center justify-between text-xs text-cyber-muted">
                  <span>{post.author.name}</span>
                  <span>{post.readTime} min</span>
                </div>
              </div>
            </div>
          </Card>
        ))}

        {/* View All Card */}
        {showViewAll && (
          <Card className="group cursor-pointer border-dashed border-cyber-border hover:border-neon-cyan transition-colors">
            <div className="p-6 text-center">
              <div className="text-2xl mb-2 group-hover:animate-pulse">📚</div>
              <p className="text-cyber-muted group-hover:text-neon-cyan transition-colors">
                Ver todos os posts
              </p>
            </div>
          </Card>
        )}
      </div>
    </div>
  );

  const renderGridLayout = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {posts.map((post, index) => (
        <Card
          key={post.id}
          className="group cursor-pointer"
          hoverable
          neonBorder={index === 0}>
          <div className="aspect-video bg-gradient-cyber rounded-t-lg mb-4 relative overflow-hidden">
            <img
              src={post.thumbnail}
              alt={post.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute top-3 left-3">
              <span
                className="px-2 py-1 rounded text-xs font-medium"
                style={{
                  backgroundColor: `${post.category.color}20`,
                  color: post.category.color,
                  border: `1px solid ${post.category.color}30`,
                }}>
                {post.category.name}
              </span>
            </div>
            {index === 0 && (
              <div className="absolute top-3 right-3">
                <span className="bg-neon-yellow text-cyber-bg px-2 py-1 rounded text-xs font-bold">
                  ⭐ DESTAQUE
                </span>
              </div>
            )}
          </div>

          <div className="p-4">
            <h3 className="font-bold text-cyber-text mb-2 group-hover:text-neon-cyan transition-colors line-clamp-2">
              {post.title}
            </h3>
            <p className="text-cyber-muted text-sm mb-3 line-clamp-2">
              {post.excerpt}
            </p>
            <div className="flex items-center justify-between text-xs text-cyber-muted">
              <span>{post.author.name}</span>
              <span>{post.readTime} min</span>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  return (
    <section className={clsx('space-y-8', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-cyber-text mb-2">{title}</h2>
          {subtitle && <p className="text-cyber-muted">{subtitle}</p>}
        </div>

        {showViewAll && (
          <Button variant="secondary" size="sm">
            Ver Todos
          </Button>
        )}
      </div>

      {/* Content */}
      {layout === 'hero' ? renderHeroLayout() : renderGridLayout()}
    </section>
  );
};

export default FeaturedPosts;
