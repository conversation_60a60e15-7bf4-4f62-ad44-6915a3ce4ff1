import React from 'react';

interface BlueprintAvatarProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const BlueprintAvatar: React.FC<BlueprintAvatarProps> = ({ 
  size = 'md', 
  className = '' 
}) => {
  // Tamanhos responsivos
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 32,
    xl: 42
  };

  const iconSize = iconSizes[size];

  return (
    <div className={`${sizeClasses[size]} ${className} rounded-full bg-gradient-to-r from-neon-cyan via-neon-magenta to-neon-purple p-0.5 flex items-center justify-center`}>
      <div className="w-full h-full rounded-full bg-cyber-bg flex items-center justify-center">
        <svg 
          width={iconSize} 
          height={iconSize} 
          viewBox="0 0 30 40" 
          xmlns="http://www.w3.org/2000/svg"
          className="drop-shadow-lg"
        >
          <defs>
            {/* Gradiente Neon */}
            <linearGradient id={`neonGradient-${size}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style={{stopColor:'#00ffff', stopOpacity:1}} />
              <stop offset="50%" style={{stopColor:'#ff00ff', stopOpacity:1}} />
              <stop offset="100%" style={{stopColor:'#8b5cf6', stopOpacity:1}} />
            </linearGradient>
            
            {/* Glow Effect */}
            <filter id={`glow-${size}`}>
              <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {/* Logo Icon - Stylized "B" */}
          <g transform="translate(5, 5)">
            {/* Main B Shape */}
            <path 
              d="M0,5 L0,30 L12,30 Q20,30 20,22.5 Q20,17.5 16,15 Q20,12.5 20,7.5 Q20,0 12,0 Z M4,4 L12,4 Q16,4 16,7.5 Q16,11 12,11 L4,11 Z M4,15 L12,15 Q16,15 16,18.5 Q16,22 12,22 L4,22 Z" 
              fill={`url(#neonGradient-${size})`}
              filter={`url(#glow-${size})`}
              stroke="#00ffff" 
              strokeWidth="0.5"
            />
            
            {/* Circuit Lines on B */}
            <path 
              d="M6,8 L10,8 M6,18 L10,18" 
              stroke="#000" 
              strokeWidth="0.3" 
              opacity="0.8"
            />
            <circle cx="8" cy="8" r="0.3" fill="#000" opacity="0.8"/>
            <circle cx="8" cy="18" r="0.3" fill="#000" opacity="0.8"/>
          </g>
        </svg>
      </div>
    </div>
  );
};

export default BlueprintAvatar;
