/**
 * @fileoverview Componente Button com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ButtonProps extends BaseComponentProps {
  /**
   * Tipo do botão
   */
  type?: 'button' | 'submit' | 'reset';

  /**
   * Se o botão está carregando
   */
  loading?: boolean;

  /**
   * Ícone à esquerda do texto
   */
  leftIcon?: React.ReactNode;

  /**
   * Ícone à direita do texto
   */
  rightIcon?: React.ReactNode;

  /**
   * Se o botão deve ocupar toda a largura
   */
  fullWidth?: boolean;

  /**
   * Função de clique
   */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

// ============================================================================
// ESTILOS
// ============================================================================

const buttonVariants = {
  primary: [
    'bg-gradient-neon text-cyber-bg',
    'hover:shadow-lg hover:shadow-neon-cyan/25',
    'active:scale-95',
    'border border-transparent',
    'hover:animate-glow',
  ],
  secondary: [
    'bg-cyber-surface text-cyber-text border border-cyber-border',
    'hover:border-neon-cyan hover:text-neon-cyan',
    'hover:shadow-md hover:shadow-neon-cyan/20',
    'active:scale-95',
  ],
  outline: [
    'bg-transparent text-cyber-text border border-cyber-border',
    'hover:border-neon-cyan hover:text-neon-cyan hover:bg-neon-cyan/10',
    'hover:shadow-md hover:shadow-neon-cyan/20',
    'active:scale-95',
  ],
  success: [
    'bg-neon-green/20 text-neon-green border border-neon-green/30',
    'hover:bg-neon-green/30 hover:shadow-md hover:shadow-neon-green/20',
    'active:scale-95',
  ],
  warning: [
    'bg-neon-yellow/20 text-neon-yellow border border-neon-yellow/30',
    'hover:bg-neon-yellow/30 hover:shadow-md hover:shadow-neon-yellow/20',
    'active:scale-95',
  ],
  error: [
    'bg-red-500/20 text-red-400 border border-red-500/30',
    'hover:bg-red-500/30 hover:shadow-md hover:shadow-red-500/20',
    'active:scale-95',
  ],
  info: [
    'bg-neon-blue/20 text-neon-blue border border-neon-blue/30',
    'hover:bg-neon-blue/30 hover:shadow-md hover:shadow-neon-blue/20',
    'active:scale-95',
  ],
};

const buttonSizes = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
  xl: 'px-8 py-4 text-xl',
};

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Componente Button com tema cyberpunk neon
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = 'primary',
      size = 'md',
      type = 'button',
      disabled = false,
      loading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      onClick,
      ...props
    },
    ref
  ) => {
    const baseStyles = [
      'inline-flex items-center justify-center',
      'font-medium rounded-lg',
      'transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-2 focus:ring-neon-cyan focus:ring-offset-2 focus:ring-offset-cyber-bg',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
      fullWidth && 'w-full',
    ];

    const variantStyles = buttonVariants[variant] || buttonVariants.primary;
    const sizeStyles = buttonSizes[size] || buttonSizes.md;

    const buttonClasses = twMerge(
      clsx(baseStyles, variantStyles, sizeStyles, className)
    );

    return (
      <button
        ref={ref}
        type={type}
        className={buttonClasses}
        disabled={disabled || loading}
        onClick={onClick}
        {...props}>
        {/* Loading Spinner */}
        {loading && (
          <div className="mr-2 animate-spin">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full" />
          </div>
        )}

        {/* Left Icon */}
        {leftIcon && !loading && (
          <span className="mr-2 flex-shrink-0">{leftIcon}</span>
        )}

        {/* Content */}
        <span className="flex-1">{children}</span>

        {/* Right Icon */}
        {rightIcon && <span className="ml-2 flex-shrink-0">{rightIcon}</span>}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
