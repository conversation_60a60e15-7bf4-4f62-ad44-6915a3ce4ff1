/**
 * @fileoverview Componente BackToTop - Botão para voltar ao topo com smooth scroll
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { FaChevronUp } from 'react-icons/fa';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface BackToTopProps extends BaseComponentProps {
  /**
   * Threshold de scroll para mostrar o botão (em pixels)
   */
  threshold?: number;

  /**
   * Posição do botão na tela
   */
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';

  /**
   * Tamanho do botão
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Se deve ter efeito neon
   */
  neonEffect?: boolean;

  /**
   * Comportamento do scroll
   */
  behavior?: 'smooth' | 'auto';

  /**
   * Offset do topo (útil para headers fixos)
   */
  topOffset?: number;
}

// ============================================================================
// CONSTANTES
// ============================================================================

/**
 * Configurações de tamanho
 */
const sizeConfig = {
  sm: {
    button: 'w-10 h-10',
    icon: 'text-sm',
  },
  md: {
    button: 'w-12 h-12',
    icon: 'text-base',
  },
  lg: {
    button: 'w-14 h-14',
    icon: 'text-lg',
  },
};

/**
 * Configurações de posição
 */
const positionConfig = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6',
  'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2',
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Botão para voltar ao topo com animações e smooth scroll
 */
export const BackToTop: React.FC<BackToTopProps> = ({
  className,
  threshold = 300,
  position = 'bottom-right',
  size = 'md',
  neonEffect = true,
  behavior = 'smooth',
  topOffset = 0,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  const config = sizeConfig[size];
  const positionClasses = positionConfig[position];

  /**
   * Monitora o scroll para mostrar/esconder o botão
   */
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > threshold) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    const handleScroll = () => {
      toggleVisibility();

      // Detecta se está fazendo scroll
      setIsScrolling(true);
      clearTimeout((window as any).scrollTimeout);
      (window as any).scrollTimeout = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll);

    // Verifica posição inicial
    toggleVisibility();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout((window as any).scrollTimeout);
    };
  }, [threshold]);

  /**
   * Função para voltar ao topo
   */
  const scrollToTop = () => {
    const targetPosition = topOffset;

    if (behavior === 'smooth') {
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth',
      });
    } else {
      window.scrollTo(0, targetPosition);
    }
  };

  // Classes do botão
  const buttonClasses = twMerge(
    clsx(
      'fixed z-50',
      'flex items-center justify-center',
      'rounded-full',
      'cursor-pointer',
      'transition-all duration-300',
      'bg-cyber-surface/80 backdrop-blur-sm',
      'border border-cyber-border',
      'text-cyber-text hover:text-white',
      'shadow-lg',
      config.button,
      positionClasses,
      neonEffect && [
        'hover:bg-gradient-neon',
        'hover:border-neon-cyan',
        'hover:shadow-neon-cyan/30',
        'hover:shadow-lg',
      ],
      !neonEffect && ['hover:bg-cyber-accent', 'hover:border-cyber-accent'],
      isScrolling && 'scale-95',
      className
    )
  );

  // Variantes de animação
  const buttonVariants = {
    hidden: {
      opacity: 0,
      scale: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 200,
        damping: 20,
      },
    },
    exit: {
      opacity: 0,
      scale: 0,
      y: 20,
      transition: {
        duration: 0.2,
      },
    },
    hover: {
      scale: 1.1,
      transition: {
        type: 'spring' as const,
        stiffness: 400,
        damping: 10,
      },
    },
    tap: {
      scale: 0.95,
    },
  };

  // Variantes do ícone - TODO: Corrigir tipos do Framer Motion
  // const iconVariants = {
  //   idle: {
  //     y: 0,
  //   },
  //   hover: {
  //     y: -2,
  //     transition: {
  //       type: 'spring',
  //       stiffness: 400,
  //       damping: 10,
  //     },
  //   },
  // };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          className={buttonClasses}
          onClick={scrollToTop}
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          whileHover="hover"
          whileTap="tap"
          aria-label="Voltar ao topo"
          title="Voltar ao topo"
          {...props}>
          {/* Efeito de glow de fundo */}
          {neonEffect && (
            <motion.div
              className="absolute inset-0 rounded-full bg-gradient-neon opacity-0 blur-sm"
              whileHover={{ opacity: 0.3 }}
              transition={{ duration: 0.3 }}
            />
          )}

          {/* Ícone */}
          <motion.div
            // variants={iconVariants} // TODO: Corrigir tipos do Framer Motion
            initial="idle"
            whileHover="hover"
            className={clsx('relative z-10', config.icon)}>
            <FaChevronUp />
          </motion.div>

          {/* Efeito de pulse quando está scrolling */}
          {isScrolling && neonEffect && (
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-neon-cyan"
              initial={{ scale: 1, opacity: 0.5 }}
              animate={{ scale: 1.5, opacity: 0 }}
              transition={{ duration: 0.6, repeat: Infinity }}
            />
          )}
        </motion.button>
      )}
    </AnimatePresence>
  );
};

// ============================================================================
// HOOKS UTILITÁRIOS
// ============================================================================

/**
 * Hook para detectar se está no topo da página
 */
export const useIsAtTop = (threshold: number = 100) => {
  const [isAtTop, setIsAtTop] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      setIsAtTop(window.pageYOffset <= threshold);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Verifica posição inicial

    return () => window.removeEventListener('scroll', handleScroll);
  }, [threshold]);

  return isAtTop;
};

/**
 * Hook para smooth scroll para elemento específico
 */
export const useSmoothScroll = () => {
  const scrollToElement = (
    elementId: string,
    offset: number = 0,
    behavior: ScrollBehavior = 'smooth'
  ) => {
    const element = document.getElementById(elementId);
    if (element) {
      const targetPosition = element.offsetTop - offset;
      window.scrollTo({
        top: targetPosition,
        behavior,
      });
    }
  };

  const scrollToTop = (
    offset: number = 0,
    behavior: ScrollBehavior = 'smooth'
  ) => {
    window.scrollTo({
      top: offset,
      behavior,
    });
  };

  return { scrollToElement, scrollToTop };
};

export default BackToTop;
