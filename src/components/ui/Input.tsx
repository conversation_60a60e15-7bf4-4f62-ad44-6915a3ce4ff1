/**
 * @fileoverview Componente Input com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface InputProps extends BaseComponentProps {
  /**
   * Tipo do input
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';

  /**
   * Placeholder do input
   */
  placeholder?: string;

  /**
   * Valor do input
   */
  value?: string;

  /**
   * Valor padrão
   */
  defaultValue?: string;

  /**
   * Se o input está com erro
   */
  error?: boolean | string;

  /**
   * Mensagem de erro
   */
  errorMessage?: string;

  /**
   * Se o input é obrigatório
   */
  required?: boolean;

  /**
   * Ícone à esquerda
   */
  leftIcon?: React.ReactNode;

  /**
   * Ícone à direita
   */
  rightIcon?: React.ReactNode;

  /**
   * Label do input
   */
  label?: string;

  /**
   * Texto de ajuda
   */
  helpText?: string;

  /**
   * Se o input deve ocupar toda a largura
   */
  fullWidth?: boolean;

  /**
   * Função de mudança de valor
   */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;

  /**
   * Função de foco
   */
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;

  /**
   * Função de blur
   */
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
}

interface TextareaProps
  extends Omit<
    InputProps,
    'type' | 'leftIcon' | 'rightIcon' | 'onChange' | 'onFocus' | 'onBlur'
  > {
  /**
   * Número de linhas
   */
  rows?: number;

  /**
   * Se o textarea pode ser redimensionado
   */
  resize?: boolean;

  /**
   * Função de mudança de valor para textarea
   */
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;

  /**
   * Função de foco para textarea
   */
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;

  /**
   * Função de blur para textarea
   */
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
}

// ============================================================================
// ESTILOS
// ============================================================================

const inputVariants = {
  primary: [
    'bg-cyber-surface border-cyber-border text-cyber-text',
    'focus:border-neon-cyan focus:ring-1 focus:ring-neon-cyan/50',
    'placeholder:text-cyber-muted',
  ],
  secondary: [
    'bg-cyber-bg border-cyber-border text-cyber-text',
    'focus:border-neon-magenta focus:ring-1 focus:ring-neon-magenta/50',
    'placeholder:text-cyber-muted',
  ],
  outline: [
    'bg-transparent border-cyber-border text-cyber-text',
    'focus:border-neon-cyan focus:ring-1 focus:ring-neon-cyan/50',
    'placeholder:text-cyber-muted',
  ],
  success: [
    'bg-cyber-surface border-neon-green/30 text-cyber-text',
    'focus:border-neon-green focus:ring-1 focus:ring-neon-green/50',
    'placeholder:text-cyber-muted',
  ],
  warning: [
    'bg-cyber-surface border-neon-yellow/30 text-cyber-text',
    'focus:border-neon-yellow focus:ring-1 focus:ring-neon-yellow/50',
    'placeholder:text-cyber-muted',
  ],
  error: [
    'bg-cyber-surface border-red-500/30 text-cyber-text',
    'focus:border-red-500 focus:ring-1 focus:ring-red-500/50',
    'placeholder:text-cyber-muted',
  ],
  info: [
    'bg-cyber-surface border-neon-blue/30 text-cyber-text',
    'focus:border-neon-blue focus:ring-1 focus:ring-neon-blue/50',
    'placeholder:text-cyber-muted',
  ],
};

const inputSizes = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-5 py-3 text-lg',
  xl: 'px-6 py-4 text-xl',
};

// ============================================================================
// COMPONENTE INPUT
// ============================================================================

/**
 * Componente Input com tema cyberpunk neon
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      type = 'text',
      placeholder,
      value,
      defaultValue,
      error = false,
      errorMessage,
      required = false,
      leftIcon,
      rightIcon,
      label,
      helpText,
      fullWidth = false,
      disabled = false,
      onChange,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const baseStyles = [
      'rounded-lg border transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-offset-2 focus:ring-offset-cyber-bg',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      fullWidth && 'w-full',
    ];

    const currentVariant = error ? 'error' : variant;
    const variantStyles =
      inputVariants[currentVariant] || inputVariants.primary;
    const sizeStyles = inputSizes[size] || inputSizes.md;

    const inputClasses = twMerge(
      clsx(
        baseStyles,
        variantStyles,
        sizeStyles,
        leftIcon && 'pl-10',
        rightIcon && 'pr-10',
        className
      )
    );

    const inputElement = (
      <div className={clsx('relative', fullWidth && 'w-full')}>
        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyber-muted">
            {leftIcon}
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          type={type}
          className={inputClasses}
          placeholder={placeholder}
          value={value}
          defaultValue={defaultValue}
          required={required}
          disabled={disabled}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted">
            {rightIcon}
          </div>
        )}
      </div>
    );

    // Se não tem label nem helpText, retorna apenas o input
    if (!label && !helpText && !errorMessage) {
      return inputElement;
    }

    // Retorna com wrapper completo
    return (
      <div className={clsx('space-y-2', fullWidth && 'w-full')}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-cyber-text">
            {label}
            {required && <span className="text-red-400 ml-1">*</span>}
          </label>
        )}

        {/* Input */}
        {inputElement}

        {/* Help Text ou Error Message */}
        {(helpText || errorMessage) && (
          <p
            className={clsx(
              'text-xs',
              error ? 'text-red-400' : 'text-cyber-muted'
            )}>
            {error ? errorMessage : helpText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// ============================================================================
// COMPONENTE TEXTAREA
// ============================================================================

/**
 * Componente Textarea com tema cyberpunk neon
 */
export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      placeholder,
      value,
      defaultValue,
      error = false,
      errorMessage,
      required = false,
      label,
      helpText,
      fullWidth = false,
      disabled = false,
      rows = 4,
      resize = true,
      onChange,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const baseStyles = [
      'rounded-lg border transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-offset-2 focus:ring-offset-cyber-bg',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      !resize && 'resize-none',
      fullWidth && 'w-full',
    ];

    const currentVariant = error ? 'error' : variant;
    const variantStyles =
      inputVariants[currentVariant] || inputVariants.primary;
    const sizeStyles = inputSizes[size] || inputSizes.md;

    const textareaClasses = twMerge(
      clsx(baseStyles, variantStyles, sizeStyles, className)
    );

    const textareaElement = (
      <textarea
        ref={ref}
        className={textareaClasses}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        required={required}
        disabled={disabled}
        rows={rows}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        {...props}
      />
    );

    // Se não tem label nem helpText, retorna apenas o textarea
    if (!label && !helpText && !errorMessage) {
      return textareaElement;
    }

    // Retorna com wrapper completo
    return (
      <div className={clsx('space-y-2', fullWidth && 'w-full')}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-cyber-text">
            {label}
            {required && <span className="text-red-400 ml-1">*</span>}
          </label>
        )}

        {/* Textarea */}
        {textareaElement}

        {/* Help Text ou Error Message */}
        {(helpText || errorMessage) && (
          <p
            className={clsx(
              'text-xs',
              error ? 'text-red-400' : 'text-cyber-muted'
            )}>
            {error ? errorMessage : helpText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Input;
