/**
 * @fileoverview Componente AnimatedWrapper - Wrapper para animações com Framer Motion
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import type { HTMLMotionProps } from 'motion/react';
import { motion } from 'motion/react';
import type { BaseComponentProps } from '../../types';
import { animationPresets, withDelay } from '../../utils/animations';

// ============================================================================
// INTERFACES
// ============================================================================

type AnimationType =
  | 'fadeIn'
  | 'slideFromLeft'
  | 'slideFromRight'
  | 'slideFromTop'
  | 'slideFromBottom'
  | 'scale'
  | 'rotate'
  | 'blur'
  | 'staggerContainer'
  | 'staggerItem';

interface AnimatedWrapperProps
  extends BaseComponentProps,
    Omit<HTMLMotionProps<'div'>, 'children'> {
  /**
   * Tipo de animação
   */
  animation?: AnimationType;

  /**
   * Delay da animação em segundos
   */
  delay?: number;

  /**
   * Se deve animar apenas quando visível (intersection observer)
   */
  animateOnView?: boolean;

  /**
   * Threshold para intersection observer
   */
  viewThreshold?: number;

  /**
   * Se deve animar apenas uma vez
   */
  once?: boolean;

  /**
   * Elemento HTML a ser renderizado
   */
  as?: keyof React.JSX.IntrinsicElements;

  /**
   * Conteúdo do componente
   */
  children: React.ReactNode;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Wrapper para animações com Framer Motion
 */
export const AnimatedWrapper: React.FC<AnimatedWrapperProps> = ({
  children,
  animation = 'fadeIn',
  delay = 0,
  animateOnView = false,
  viewThreshold = 0.1,
  once = true,
  as = 'div',
  className,
  ...props
}) => {
  // Pega o preset de animação
  const preset = animationPresets[animation];

  // Aplica delay se especificado
  const animationProps = delay > 0 ? withDelay(preset, delay) : preset;

  // Configurações para animação on view
  const viewportConfig = animateOnView
    ? {
        viewport: {
          once,
          amount: viewThreshold,
        },
      }
    : {};

  // Props finais para o motion component
  const motionProps = {
    ...animationProps,
    ...viewportConfig,
    className,
    ...props,
  };

  // Renderiza o componente motion apropriado
  const MotionComponent = motion[as as keyof typeof motion] as any;

  return <MotionComponent {...motionProps}>{children}</MotionComponent>;
};

// ============================================================================
// COMPONENTES ESPECIALIZADOS
// ============================================================================

/**
 * Wrapper para animações de fade-in
 */
export const FadeIn: React.FC<Omit<AnimatedWrapperProps, 'animation'>> = (
  props
) => <AnimatedWrapper animation="fadeIn" {...props} />;

/**
 * Wrapper para animações de slide
 */
export const SlideIn: React.FC<
  Omit<AnimatedWrapperProps, 'animation'> & {
    direction?: 'left' | 'right' | 'top' | 'bottom';
  }
> = ({ direction = 'left', ...props }) => {
  const animationMap = {
    left: 'slideFromLeft',
    right: 'slideFromRight',
    top: 'slideFromTop',
    bottom: 'slideFromBottom',
  } as const;

  return <AnimatedWrapper animation={animationMap[direction]} {...props} />;
};

/**
 * Wrapper para animações de escala
 */
export const ScaleIn: React.FC<Omit<AnimatedWrapperProps, 'animation'>> = (
  props
) => <AnimatedWrapper animation="scale" {...props} />;

/**
 * Wrapper para animações de rotação
 */
export const RotateIn: React.FC<Omit<AnimatedWrapperProps, 'animation'>> = (
  props
) => <AnimatedWrapper animation="rotate" {...props} />;

/**
 * Wrapper para animações de blur
 */
export const BlurIn: React.FC<Omit<AnimatedWrapperProps, 'animation'>> = (
  props
) => <AnimatedWrapper animation="blur" {...props} />;

/**
 * Container para animações staggered
 */
export const StaggerContainer: React.FC<
  Omit<AnimatedWrapperProps, 'animation'>
> = (props) => <AnimatedWrapper animation="staggerContainer" {...props} />;

/**
 * Item para animações staggered
 */
export const StaggerItem: React.FC<Omit<AnimatedWrapperProps, 'animation'>> = (
  props
) => <AnimatedWrapper animation="staggerItem" {...props} />;

// ============================================================================
// HOOKS UTILITÁRIOS
// ============================================================================

/**
 * Hook para criar animações personalizadas
 */
export const useAnimation = (type: AnimationType, delay: number = 0) => {
  const preset = animationPresets[type];
  return delay > 0 ? withDelay(preset, delay) : preset;
};

/**
 * Hook para animações on scroll
 */
export const useScrollAnimation = (
  type: AnimationType,
  options: {
    delay?: number;
    threshold?: number;
    once?: boolean;
  } = {}
) => {
  const { delay = 0, threshold = 0.1, once = true } = options;
  const preset = animationPresets[type];
  const animationProps = delay > 0 ? withDelay(preset, delay) : preset;

  return {
    ...animationProps,
    viewport: {
      once,
      amount: threshold,
    },
  };
};

export default AnimatedWrapper;
