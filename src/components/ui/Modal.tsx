/**
 * @fileoverview Componente Modal com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useRef } from 'react';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ModalProps extends Omit<BaseComponentProps, 'size'> {
  /**
   * Se o modal está aberto
   */
  isOpen: boolean;

  /**
   * Função para fechar o modal
   */
  onClose: () => void;

  /**
   * Título do modal
   */
  title?: string;

  /**
   * Se deve mostrar o botão de fechar (X)
   */
  showCloseButton?: boolean;

  /**
   * Se deve fechar ao clicar no backdrop
   */
  closeOnBackdropClick?: boolean;

  /**
   * Se deve fechar ao pressionar Escape
   */
  closeOnEscape?: boolean;

  /**
   * Tamanho do modal
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';

  /**
   * Se deve centralizar o modal
   */
  centered?: boolean;

  /**
   * Classe personalizada para o backdrop
   */
  backdropClassName?: string;

  /**
   * Classe personalizada para o conteúdo
   */
  contentClassName?: string;
}

interface ModalHeaderProps extends BaseComponentProps {
  /**
   * Título do header
   */
  title?: string;

  /**
   * Se deve mostrar o botão de fechar
   */
  showCloseButton?: boolean;

  /**
   * Função para fechar o modal
   */
  onClose?: () => void;
}

interface ModalBodyProps extends BaseComponentProps {}

interface ModalFooterProps extends BaseComponentProps {
  /**
   * Se deve ter borda superior
   */
  bordered?: boolean;
}

// ============================================================================
// ESTILOS
// ============================================================================

const modalSizes = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-full mx-4',
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Componente Modal principal
 */
export const Modal: React.FC<ModalProps> = ({
  children,
  className,
  variant = 'primary',
  isOpen,
  onClose,
  title,
  showCloseButton = true,
  closeOnBackdropClick = true,
  closeOnEscape = true,
  size = 'md',
  centered = true,
  backdropClassName,
  contentClassName,
  ...props
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Efeito para fechar com Escape
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Efeito para gerenciar scroll do body
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Função para lidar com clique no backdrop
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnBackdropClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const backdropClasses = twMerge(
    clsx(
      'fixed inset-0 z-50',
      'bg-cyber-bg/80 backdrop-blur-sm',
      'flex items-center justify-center p-4',
      centered ? 'items-center' : 'items-start pt-16',
      backdropClassName
    )
  );

  const contentClasses = twMerge(
    clsx(
      'relative w-full',
      'bg-cyber-surface border border-cyber-border rounded-lg',
      'shadow-2xl shadow-cyber-bg/50',
      'transform transition-all duration-300 ease-out',
      'animate-in fade-in zoom-in-95',
      modalSizes[size],
      contentClassName,
      className
    )
  );

  return (
    <div className={backdropClasses} onClick={handleBackdropClick} {...props}>
      <div
        ref={modalRef}
        className={contentClasses}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}>
        {/* Header automático se tem título ou showCloseButton */}
        {(title || showCloseButton) && (
          <ModalHeader
            title={title}
            showCloseButton={showCloseButton}
            onClose={onClose}
          />
        )}

        {/* Conteúdo */}
        {children}
      </div>
    </div>
  );
};

// ============================================================================
// COMPONENTE HEADER
// ============================================================================

/**
 * Header do Modal
 */
export const ModalHeader: React.FC<ModalHeaderProps> = ({
  children,
  className,
  title,
  showCloseButton = true,
  onClose,
  ...props
}) => {
  const headerClasses = twMerge(
    clsx(
      'flex items-center justify-between',
      'px-6 py-4 border-b border-cyber-border',
      className
    )
  );

  return (
    <div className={headerClasses} {...props}>
      <div className="flex-1">
        {title && (
          <h3
            id="modal-title"
            className="text-lg font-semibold text-cyber-text">
            {title}
          </h3>
        )}
        {children}
      </div>

      {showCloseButton && onClose && (
        <button
          type="button"
          className={clsx(
            'ml-4 p-2 rounded-lg',
            'text-cyber-muted hover:text-cyber-text',
            'hover:bg-cyber-border/50',
            'transition-colors duration-200',
            'focus:outline-none focus:ring-2 focus:ring-neon-cyan focus:ring-offset-2 focus:ring-offset-cyber-surface'
          )}
          onClick={onClose}
          aria-label="Fechar modal">
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

// ============================================================================
// COMPONENTE BODY
// ============================================================================

/**
 * Body do Modal
 */
export const ModalBody: React.FC<ModalBodyProps> = ({
  children,
  className,
  ...props
}) => {
  const bodyClasses = twMerge(clsx('px-6 py-4', 'text-cyber-text', className));

  return (
    <div className={bodyClasses} {...props}>
      {children}
    </div>
  );
};

// ============================================================================
// COMPONENTE FOOTER
// ============================================================================

/**
 * Footer do Modal
 */
export const ModalFooter: React.FC<ModalFooterProps> = ({
  children,
  className,
  bordered = true,
  ...props
}) => {
  const footerClasses = twMerge(
    clsx(
      'flex items-center justify-end space-x-3',
      'px-6 py-4',
      bordered && 'border-t border-cyber-border',
      className
    )
  );

  return (
    <div className={footerClasses} {...props}>
      {children}
    </div>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

export default Modal;
