/**
 * @fileoverview Componente TopOfMonth - Posts mais acessados/avaliados do mês
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import {
  FaAward,
  FaEye,
  FaFire,
  FaHeart,
  FaMedal,
  FaTrophy,
} from 'react-icons/fa';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps, Post } from '../../types';
import { Card, CardContent, CardHeader } from './Card';

// ============================================================================
// INTERFACES
// ============================================================================

interface TopOfMonthProps extends BaseComponentProps {
  /**
   * Posts do ranking
   */
  posts: Post[];

  /**
   * Título da seção
   */
  title?: string;

  /**
   * Subtítulo da seção
   */
  subtitle?: string;

  /**
   * Número máximo de posts a exibir
   */
  maxPosts?: number;

  /**
   * Layout do componente
   */
  layout?: 'vertical' | 'horizontal' | 'grid';

  /**
   * Se deve mostrar métricas
   */
  showMetrics?: boolean;

  /**
   * Se deve mostrar thumbnails
   */
  showThumbnails?: boolean;

  /**
   * Se deve ter animações
   */
  animated?: boolean;

  /**
   * Função de clique no post
   */
  onPostClick?: (post: Post) => void;
}

// ============================================================================
// CONSTANTES
// ============================================================================

/**
 * Ícones para as posições do ranking
 */
const rankingIcons = {
  1: { icon: FaTrophy, color: '#FFD700', bg: 'bg-yellow-500' },
  2: { icon: FaMedal, color: '#C0C0C0', bg: 'bg-gray-400' },
  3: { icon: FaAward, color: '#CD7F32', bg: 'bg-orange-500' },
};

/**
 * Configurações de layout
 */
const layoutConfig = {
  vertical: 'space-y-4',
  horizontal: 'flex flex-wrap gap-4',
  grid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4',
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Ranking dos posts mais populares do mês
 */
export const TopOfMonth: React.FC<TopOfMonthProps> = ({
  posts,
  title = '🔥 Top do Mês',
  subtitle = 'Posts mais visualizados',
  maxPosts = 5,
  layout = 'vertical',
  showMetrics = true,
  showThumbnails = true,
  animated = true,
  onPostClick,
  className,
  ...props
}) => {
  // Limita o número de posts
  const topPosts = posts.slice(0, maxPosts);

  /**
   * Handle post click
   */
  const handlePostClick = (post: Post) => {
    onPostClick?.(post);
  };

  /**
   * Renderiza um post do ranking
   */
  const renderRankingPost = (post: Post, index: number) => {
    const position = index + 1;
    const rankingData = rankingIcons[position as keyof typeof rankingIcons];
    const isTopThree = position <= 3;

    const postContent = (
      <div
        className={clsx(
          'flex items-start gap-3 p-3 rounded-lg cursor-pointer group',
          'hover:bg-cyber-surface/50 transition-all duration-300',
          isTopThree && 'bg-gradient-to-r from-cyber-surface/30 to-transparent'
        )}
        onClick={() => handlePostClick(post)}>
        {/* Ranking Number/Icon */}
        <div className="flex-shrink-0">
          {isTopThree && rankingData ? (
            <div
              className={clsx(
                'w-8 h-8 rounded-full flex items-center justify-center',
                rankingData.bg,
                'text-cyber-bg font-bold text-sm'
              )}>
              <rankingData.icon />
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-cyber-surface text-cyber-muted flex items-center justify-center text-sm font-bold">
              {position}
            </div>
          )}
        </div>

        {/* Thumbnail */}
        {showThumbnails && (
          <div className="flex-shrink-0 w-16 h-12 rounded overflow-hidden">
            <img
              src={post.thumbnail}
              alt={post.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
            />
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-cyber-text group-hover:text-neon-cyan transition-colors line-clamp-2 mb-1">
            {post.title}
          </h4>

          <div className="flex items-center gap-3 text-xs text-cyber-muted">
            <span>{post.author.name}</span>

            {showMetrics && (
              <>
                <div className="flex items-center gap-1">
                  <FaEye className="text-neon-cyan" />
                  <span>{post.views?.toLocaleString() || '0'}</span>
                </div>

                <div className="flex items-center gap-1">
                  <FaHeart className="text-neon-cyan" />
                  <span>{post.likes?.toLocaleString() || '0'}</span>
                </div>
              </>
            )}
          </div>

          {/* Category Badge */}
          <div className="mt-2">
            <span
              className="px-2 py-1 rounded text-xs font-medium"
              style={{
                backgroundColor: `${post.category.color}20`,
                color: post.category.color,
                border: `1px solid ${post.category.color}30`,
              }}>
              {post.category.name}
            </span>
          </div>
        </div>

        {/* Position Badge for Top 3 */}
        {isTopThree && (
          <div className="flex-shrink-0">
            <div
              className={clsx(
                'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',
                position === 1 && 'bg-yellow-500 text-cyber-bg',
                position === 2 && 'bg-gray-400 text-cyber-bg',
                position === 3 && 'bg-orange-500 text-cyber-bg'
              )}>
              #{position}
            </div>
          </div>
        )}
      </div>
    );

    return layout === 'grid' ? (
      <Card key={post.id} className="h-full">
        <CardContent className="p-0">{postContent}</CardContent>
      </Card>
    ) : (
      <div key={post.id}>{postContent}</div>
    );
  };

  // Classes do container
  const containerClasses = twMerge(clsx('space-y-4', className));

  return (
    <section className={containerClasses} {...props}>
      {layout === 'vertical' ? (
        <Card className="shadow-2xl shadow-cyber-bg/40 hover:shadow-2xl hover:shadow-cyber-bg/60 transition-shadow duration-300">
          <CardHeader
            title={title}
            subtitle={subtitle}
            // icon={<FaFire className="text-neon-orange" />} // TODO: Corrigir props do CardHeader
          />
          <CardContent>
            <div className={layoutConfig[layout]}>
              {topPosts.map((post, index) => renderRankingPost(post, index))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Header para layouts não-vertical */}
          <div className="flex items-center gap-3 mb-6">
            <FaFire className="text-neon-orange text-xl" />
            <div>
              <h2 className="text-2xl font-bold text-cyber-text">{title}</h2>
              {subtitle && (
                <p className="text-cyber-muted text-sm">{subtitle}</p>
              )}
            </div>
          </div>

          {/* Posts Grid/Horizontal */}
          <div className={layoutConfig[layout]}>
            {topPosts.map((post, index) => renderRankingPost(post, index))}
          </div>
        </>
      )}

      {/* View All Link */}
      {posts.length > maxPosts && (
        <div className="text-center pt-4">
          <button className="text-neon-cyan hover:text-white transition-colors text-sm font-medium">
            Ver todos os {posts.length} posts →
          </button>
        </div>
      )}
    </section>
  );
};

// ============================================================================
// COMPONENTES AUXILIARES
// ============================================================================

/**
 * Skeleton loader para TopOfMonth
 */
export const TopOfMonthSkeleton: React.FC<{
  layout?: 'vertical' | 'horizontal' | 'grid';
  count?: number;
}> = ({ layout = 'vertical', count = 5 }) => (
  <div className="space-y-4 animate-pulse">
    {layout === 'vertical' ? (
      <Card>
        <CardHeader>
          <div className="h-6 bg-cyber-border rounded w-32" />
          <div className="h-4 bg-cyber-border rounded w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-cyber-border rounded-full" />
                <div className="w-16 h-12 bg-cyber-border rounded" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-cyber-border rounded w-3/4" />
                  <div className="h-3 bg-cyber-border rounded w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    ) : (
      <div className={layoutConfig[layout]}>
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="p-4 bg-cyber-surface rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-cyber-border rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-cyber-border rounded w-3/4" />
                <div className="h-3 bg-cyber-border rounded w-1/2" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )}
  </div>
);

export default TopOfMonth;
