/**
 * @fileoverview Componente para preload de recursos críticos
 * <AUTHOR> Blog Team
 * @version 1.0.0
 */

import { useEffect } from 'react';

interface ResourcePreloaderProps {
  /** URLs de recursos para preload */
  resources?: string[];
  /** Componentes para preload */
  components?: (() => Promise<any>)[];
}

/**
 * Componente para preload de recursos críticos
 * Melhora a performance carregando recursos antecipadamente
 */
export const ResourcePreloader: React.FC<ResourcePreloaderProps> = ({
  resources = [],
  components = []
}) => {
  useEffect(() => {
    // Preload de recursos (imagens, fonts, etc)
    resources.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      
      // Detecta tipo do recurso
      if (url.match(/\.(woff2?|ttf|otf)$/)) {
        link.as = 'font';
        link.crossOrigin = 'anonymous';
      } else if (url.match(/\.(jpg|jpeg|png|webp|svg)$/)) {
        link.as = 'image';
      } else if (url.match(/\.css$/)) {
        link.as = 'style';
      } else if (url.match(/\.js$/)) {
        link.as = 'script';
      }
      
      document.head.appendChild(link);
    });

    // Preload de componentes críticos
    components.forEach(componentLoader => {
      // Carrega componente em background
      componentLoader().catch(error => {
        console.warn('Falha no preload de componente:', error);
      });
    });

    // Preload de rotas críticas
    const criticalRoutes = [
      () => import('../../pages/Post'),
      () => import('../../components/layout/MainLayout'),
      () => import('../../components/ui/BreakingNews')
    ];

    criticalRoutes.forEach(routeLoader => {
      // Delay para não interferir no carregamento inicial
      setTimeout(() => {
        routeLoader().catch(error => {
          console.warn('Falha no preload de rota:', error);
        });
      }, 2000);
    });

  }, [resources, components]);

  return null; // Componente invisível
};

/**
 * Hook para preload manual de recursos
 */
export const useResourcePreloader = () => {
  const preloadResource = (url: string) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    if (url.match(/\.(woff2?|ttf|otf)$/)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    } else if (url.match(/\.(jpg|jpeg|png|webp|svg)$/)) {
      link.as = 'image';
    }
    
    document.head.appendChild(link);
  };

  const preloadComponent = (componentLoader: () => Promise<any>) => {
    componentLoader().catch(error => {
      console.warn('Falha no preload de componente:', error);
    });
  };

  return { preloadResource, preloadComponent };
};

export default ResourcePreloader;
