/**
 * @fileoverview Header principal unificado da aplicação
 * Combina Topbar + Navegação + BreakingNews em um único componente
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { FcFolder, FcHome } from 'react-icons/fc';
import { twMerge } from 'tailwind-merge';
import { useAuth } from '../../contexts/AuthContext';
import type {
  BaseComponentProps,
  MenuItem,
  Post,
  UserPreferences,
} from '../../types';
import { Input } from '../ui/Input';
import { LogoNeon } from '../ui/LogoNeon';

// ============================================================================
// INTERFACES
// ============================================================================

interface HeaderComplexProps extends BaseComponentProps {
  /**
   * Se deve mostrar a topbar
   */
  showTopbar?: boolean;

  /**
   * Se deve mostrar o header principal
   */
  showHeader?: boolean;

  /**
   * Se deve mostrar breaking news
   */
  showBreakingNews?: boolean;

  /**
   * Posts para breaking news
   */
  breakingNewsPosts?: Post[];

  /**
   * Itens do menu principal
   */
  menuItems?: MenuItem[];

  /**
   * Função de busca
   */
  onSearch?: (query: string) => void;
}

interface QuickLink {
  id: string;
  label: string;
  href: string;
  external?: boolean;
}

// ============================================================================
// CONSTANTES REUTILIZADAS
// ============================================================================

/**
 * Menu items padrão (reutilizando do HeaderNeon)
 */
const defaultMenuItems: MenuItem[] = [
  {
    id: 'home',
    label: 'Início',
    href: '/',
    icon: <FcHome />,
  },
  {
    id: 'blog',
    label: 'Posts',
    href: '/blog',
    icon: '📝',
  },
  {
    id: 'categories',
    label: 'Categorias',
    href: '/blog',
    icon: <FcFolder />,
    children: [
      { id: 'ia', label: 'IA & Machine Learning', href: '/categories/ia' },
      { id: 'frontend', label: 'Frontend', href: '/categories/frontend' },
      { id: 'backend', label: 'Backend', href: '/categories/backend' },
      { id: 'mobile', label: 'Mobile', href: '/categories/mobile' },
    ],
  },
  {
    id: 'tags',
    label: 'Tags',
    href: '/blog',
    icon: '🏷️',
    children: [
      { id: 'react', label: 'React', href: '/tags/react' },
      { id: 'typescript', label: 'TypeScript', href: '/tags/typescript' },
      { id: 'javascript', label: 'JavaScript', href: '/tags/javascript' },
      { id: 'nodejs', label: 'Node.js', href: '/tags/nodejs' },
    ],
  },
  {
    id: 'about',
    label: 'Sobre',
    href: '/about',
    icon: '📧',
  },
];

/**
 * Links rápidos da topbar (reutilizando do TopbarFixed)
 */
const defaultQuickLinks: QuickLink[] = [
  { id: 'about', label: 'Sobre', href: '/about' },
  { id: 'admin', label: 'Admin', href: '/admin/posts' },
];

/**
 * Idiomas disponíveis (reutilizando do TopbarFixed)
 */
const languageOptions = [
  { code: 'pt', label: '🇧🇷 PT', fullLabel: 'Português' },
  { code: 'en', label: '🇺🇸 EN', fullLabel: 'English' },
  { code: 'es', label: '🇪🇸 ES', fullLabel: 'Español' },
] as const;

// ============================================================================
// HOOKS REUTILIZADOS
// ============================================================================

/**
 * Hook para formatação de data (reutilizando lógica existente)
 */
const useDateFormatter = (locale = 'pt-BR') => {
  const [currentDate] = useState(new Date());

  const formatTopbarDate = (): string => {
    return currentDate.toLocaleDateString(locale, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return { formatTopbarDate };
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Header principal da aplicação
 * Componente unificado que combina Topbar + Navegação + BreakingNews
 * Evita duplicação e melhora a performance
 */
export const HeaderComplex: React.FC<HeaderComplexProps> = ({
  className,
  showTopbar = true,
  showHeader = true,
  showBreakingNews = true,
  breakingNewsPosts = [],
  menuItems = defaultMenuItems,
  onSearch,
  ...props
}) => {
  // Estados unificados
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const [currentNewsIndex, setCurrentNewsIndex] = useState(0);

  // Hooks reutilizados
  const { user, updateUser } = useAuth();
  const currentLanguage = user?.preferences?.language || 'pt';
  const localeMap = { pt: 'pt-BR', en: 'en-US', es: 'es-ES' };
  const { formatTopbarDate } = useDateFormatter(localeMap[currentLanguage]);

  /**
   * Handlers reutilizados
   */
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  const handleLanguageChange = (newLanguage: 'pt' | 'en' | 'es') => {
    if (!user) return;

    const updatedPreferences: UserPreferences = {
      ...user.preferences,
      language: newLanguage,
    };

    updateUser({ preferences: updatedPreferences });
    setIsLanguageDropdownOpen(false);
  };

  // Preparação de dados para breaking news
  const newsItems = breakingNewsPosts.slice(0, 6).map((post) => ({
    id: post.id,
    title: post.title,
    category: post.category.name,
    color: post.category.color,
    slug: post.slug,
  }));

  const currentLanguageOption = languageOptions.find(
    (lang) => lang.code === currentLanguage
  );

  // Classes unificadas (sem conflitos de z-index)
  const headerComplexClasses = twMerge(
    clsx(
      'sticky top-0 z-40', // Z-index único para todo o conjunto
      'bg-cyber-surface/95 backdrop-blur-md',
      'border-b border-cyber-border',
      'transition-all duration-300',
      className
    )
  );

  return (
    <div className={headerComplexClasses} {...props}>
      {/* 1. TOPBAR FIXA */}
      {showTopbar && (
        <div className="border-b border-cyber-border/30 bg-cyber-surface/80">
          <div className="container mx-auto px-4 h-10">
            <div className="flex items-center justify-between h-full text-xs">
              {/* Data Dinâmica */}
              <div className="flex items-center space-x-2 text-cyber-muted">
                <span className="hidden sm:inline">📅</span>
                <span className="font-medium capitalize">
                  {formatTopbarDate()}
                </span>
              </div>

              {/* Links Rápidos + Idioma */}
              <div className="flex items-center space-x-4">
                {/* Links Rápidos */}
                <nav className="hidden md:flex items-center space-x-3">
                  {defaultQuickLinks.map((link, index) => (
                    <span key={link.id} className="flex items-center space-x-3">
                      <a
                        href={link.href}
                        className="text-cyber-muted hover:text-neon-cyan transition-colors duration-200 font-medium">
                        {link.label}
                      </a>
                      {index < defaultQuickLinks.length - 1 && (
                        <span className="text-cyber-border">|</span>
                      )}
                    </span>
                  ))}
                </nav>

                {/* Toggle de Idioma */}
                <div className="relative">
                  <button
                    onClick={() =>
                      setIsLanguageDropdownOpen(!isLanguageDropdownOpen)
                    }
                    className="flex items-center space-x-1 px-2 py-1 rounded text-cyber-muted hover:text-neon-cyan hover:bg-cyber-border/20 transition-all duration-200 font-medium">
                    <span>{currentLanguageOption?.label}</span>
                    <span
                      className={clsx(
                        'transition-transform duration-200',
                        isLanguageDropdownOpen && 'rotate-180'
                      )}>
                      ▼
                    </span>
                  </button>

                  {/* Dropdown */}
                  {isLanguageDropdownOpen && (
                    <div className="absolute top-full right-0 mt-1 w-32 bg-cyber-surface border border-cyber-border rounded-lg shadow-xl shadow-cyber-bg/50 animate-in fade-in slide-in-from-top-2 duration-200 z-50">
                      {languageOptions.map((option) => (
                        <button
                          key={option.code}
                          onClick={() => handleLanguageChange(option.code)}
                          disabled={option.code !== 'pt'}
                          className={clsx(
                            'w-full text-left px-3 py-2 text-xs hover:bg-cyber-border/30 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg',
                            currentLanguage === option.code
                              ? 'text-neon-cyan bg-cyber-border/20'
                              : 'text-cyber-text',
                            option.code !== 'pt' &&
                              'opacity-50 cursor-not-allowed'
                          )}>
                          <span className="flex items-center justify-between">
                            <span>{option.label}</span>
                            {currentLanguage === option.code && (
                              <span className="text-neon-cyan">✓</span>
                            )}
                          </span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 2. HEADER PRINCIPAL */}
      {showHeader && (
        <div className="bg-cyber-surface">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              {/* Logo com efeitos neon/glitch */}
              <LogoNeon
                size="md"
                hoverable={true}
                glitchEffect={true}
                neonIntensity="medium"
                onClick={() => (window.location.href = '/')}
              />

              {/* Menu Desktop */}
              <nav className="hidden lg:flex items-center space-x-8">
                {menuItems.map((item) => (
                  <div key={item.id} className="relative group">
                    <a
                      href={item.href}
                      className="flex items-center space-x-2 text-cyber-text hover:text-neon-cyan transition-colors duration-200 font-medium">
                      <span>{item.icon}</span>
                      <span>{item.label}</span>
                    </a>

                    {/* Dropdown Menu */}
                    {item.children && (
                      <div className="absolute top-full left-0 mt-2 w-48 bg-cyber-surface border border-cyber-border rounded-lg shadow-xl shadow-cyber-bg/50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform translate-y-2 group-hover:translate-y-0">
                        {item.children.map((child) => (
                          <a
                            key={child.id}
                            href={child.href}
                            className="block px-4 py-3 text-sm text-cyber-text hover:text-neon-cyan hover:bg-cyber-border/30 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg">
                            {child.label}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </nav>

              {/* Busca + Menu Mobile */}
              <div className="flex items-center space-x-4">
                {/* Busca Desktop */}
                <form onSubmit={handleSearch} className="hidden md:block">
                  <Input
                    type="search"
                    placeholder="Buscar posts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    leftIcon={
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    }
                    size="sm"
                    className="w-64"
                  />
                </form>

                {/* Menu Mobile Toggle */}
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="lg:hidden p-2 rounded-lg hover:bg-cyber-border/30 transition-colors">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {/* Menu Mobile */}
            {isMobileMenuOpen && (
              <div className="lg:hidden border-t border-cyber-border bg-cyber-surface/95 backdrop-blur-sm animate-in slide-in-from-top-2 duration-200">
                <nav className="py-4 space-y-2">
                  {menuItems.map((item) => (
                    <div key={item.id}>
                      <a
                        href={item.href}
                        className="flex items-center space-x-3 px-4 py-3 rounded-lg text-cyber-text hover:text-neon-cyan hover:bg-cyber-border/30 transition-all duration-200"
                        onClick={() => setIsMobileMenuOpen(false)}>
                        <span>{item.icon}</span>
                        <span className="font-medium">{item.label}</span>
                      </a>

                      {/* Submenu Mobile */}
                      {item.children && (
                        <div className="ml-8 mt-2 space-y-1">
                          {item.children.map((child) => (
                            <a
                              key={child.id}
                              href={child.href}
                              className="block px-4 py-2 text-sm text-cyber-muted hover:text-neon-cyan transition-colors duration-200"
                              onClick={() => setIsMobileMenuOpen(false)}>
                              {child.label}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Busca Mobile */}
                  <div className="px-4 pt-4 border-t border-cyber-border">
                    <form onSubmit={handleSearch}>
                      <Input
                        type="search"
                        placeholder="Buscar posts..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        leftIcon={
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                          </svg>
                        }
                        fullWidth
                        size="sm"
                      />
                    </form>
                  </div>
                </nav>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 3. BREAKING NEWS */}
      {showBreakingNews && newsItems.length > 0 && (
        <div className="relative bg-cyber-surface border-t border-cyber-border/30 overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-neon-cyan/10 before:via-transparent before:to-neon-magenta/10">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              {/* Breaking Badge */}
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="bg-gradient-to-r from-neon-cyan to-neon-magenta text-cyber-bg px-4 py-1 rounded-full text-sm font-bold animate-pulse-neon">
                    BREAKING
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-neon-cyan to-neon-magenta rounded-full blur opacity-30 animate-glow"></div>
                </div>

                {/* Current News */}
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <div
                    className="w-3 h-3 rounded-full mr-3 flex-shrink-0"
                    style={{
                      backgroundColor: newsItems[currentNewsIndex]?.color,
                      filter: `drop-shadow(0 0 5px ${newsItems[currentNewsIndex]?.color})`,
                    }}
                  />

                  <div className="min-w-0 flex-1">
                    <a
                      href={`/post/${newsItems[currentNewsIndex]?.slug}`}
                      className="text-cyber-text hover:text-neon-cyan transition-colors duration-300 font-medium truncate block">
                      {newsItems[currentNewsIndex]?.title}
                    </a>
                  </div>

                  <span
                    className="px-2 py-1 rounded text-xs font-medium flex-shrink-0"
                    style={{
                      backgroundColor: `${newsItems[currentNewsIndex]?.color}20`,
                      color: newsItems[currentNewsIndex]?.color,
                      border: `1px solid ${newsItems[currentNewsIndex]?.color}30`,
                    }}>
                    {newsItems[currentNewsIndex]?.category}
                  </span>
                </div>
              </div>

              {/* Navigation Dots */}
              <div className="hidden sm:flex items-center space-x-2">
                {newsItems.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentNewsIndex(index)}
                    className={clsx(
                      'w-2 h-2 rounded-full transition-all duration-300',
                      index === currentNewsIndex
                        ? 'bg-neon-cyan shadow-neon-cyan/50 shadow-lg'
                        : 'bg-cyber-border hover:bg-cyber-muted'
                    )}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Bottom scanning line */}
          <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-neon-magenta to-transparent animate-scan-horizontal opacity-30" />
        </div>
      )}

      {/* Backdrop para fechar dropdown */}
      {isLanguageDropdownOpen && (
        <div
          className="fixed inset-0 z-45"
          onClick={() => setIsLanguageDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default HeaderComplex;
