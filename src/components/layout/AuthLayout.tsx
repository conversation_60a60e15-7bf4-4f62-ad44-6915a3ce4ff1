/**
 * @fileoverview Layout para páginas de autenticação
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import type { ReactNode } from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

interface AuthLayoutProps {
  children: ReactNode;
  className?: string;
  showBackground?: boolean;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Layout especializado para páginas de autenticação
 */
export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  className,
  showBackground = true,
}) => {
  return (
    <div
      className={clsx(
        'min-h-screen bg-cyber-bg text-cyber-text',
        showBackground && 'relative overflow-hidden',
        className
      )}>
      {/* Background Pattern */}
      {showBackground && (
        <>
          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
              `,
                backgroundSize: '50px 50px',
              }}
            />
          </div>

          {/* Gradient Overlays */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-96 h-96 bg-neon-cyan/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />
            <div className="absolute bottom-0 right-0 w-96 h-96 bg-neon-magenta/20 rounded-full blur-3xl translate-x-1/2 translate-y-1/2" />
            <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />
          </div>

          {/* Animated Elements */}
          <div className="absolute inset-0 overflow-hidden">
            {/* Floating Particles */}
            {Array.from({ length: 20 }, (_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 bg-neon-cyan rounded-full opacity-30 animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                }}
              />
            ))}

            {/* Scanning Lines */}
            <div className="absolute inset-0">
              <div className="absolute w-full h-px bg-gradient-to-r from-transparent via-neon-cyan to-transparent opacity-30 animate-scan-horizontal" />
              <div className="absolute w-px h-full bg-gradient-to-b from-transparent via-neon-magenta to-transparent opacity-30 animate-scan-vertical" />
            </div>
          </div>
        </>
      )}

      {/* Content */}
      <div className="relative z-10">{children}</div>

      {/* Footer */}
      <div className="relative z-10 fixed bottom-0 left-0 right-0 p-4">
        <div className="text-center text-xs text-cyber-muted">
          <p>Blueprint Blog v2.0 - Sistema de Autenticação Mock</p>
          <p className="mt-1">
            Desenvolvido com ❤️ usando React + TypeScript + Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
