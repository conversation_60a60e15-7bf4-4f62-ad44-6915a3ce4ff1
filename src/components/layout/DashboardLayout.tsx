/**
 * @fileoverview Layout para o painel administrativo do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';
import {
  MdAdminPanelSettings,
  MdAnalytics,
  MdArticle,
  MdBolt,
  MdCreate,
  MdDashboard,
  MdEdit,
  MdFolder,
  MdImage,
  MdLabel,
  MdLogout,
  MdMenu,
  MdPeople,
  MdPublic,
  MdSettings,
} from 'react-icons/md';
import { Link, useLocation } from 'react-router';
import { useAuth } from '../../contexts/AuthContext';
import { formatCount, useCounters } from '../../hooks/useDashboardStats';
import { Button } from '../ui';
import AuthorAvatar from '../ui/AuthorAvatar';

// ============================================================================
// INTERFACES
// ============================================================================

interface DashboardLayoutProps {
  children: ReactNode;
  className?: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: ReactNode;
  path: string;
  badge?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Layout especializado para o painel administrativo
 */
export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  className,
}) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { counters, loading: statsLoading } = useCounters();

  // Hook para detectar redimensionamento de tela e fechar sidebar automaticamente em mobile
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 1024; // lg breakpoint
      if (isMobile) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    // Verificar tamanho inicial
    handleResize();

    // Adicionar listener de resize
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Itens de navegação do dashboard
  const navItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <MdDashboard className="w-5 h-5" />,
      path: '/admin',
    },
    {
      id: 'posts',
      label: 'Posts',
      icon: <MdArticle className="w-5 h-5" />,
      path: '/admin/posts',
      badge: statsLoading ? '...' : formatCount(counters.posts),
    },
    {
      id: 'new-post',
      label: 'Novo Post',
      icon: <MdEdit className="w-5 h-5" />,
      path: '/admin/posts/new',
    },
    {
      id: 'categories',
      label: 'Categorias',
      icon: <MdFolder className="w-5 h-5" />,
      path: '/admin/categories',
      badge: statsLoading ? '...' : formatCount(counters.categories),
    },
    {
      id: 'tags',
      label: 'Tags',
      icon: <MdLabel className="w-5 h-5" />,
      path: '/admin/tags',
      badge: statsLoading ? '...' : formatCount(counters.tags),
    },
    {
      id: 'media',
      label: 'Mídia',
      icon: <MdImage className="w-5 h-5" />,
      path: '/admin/media',
      badge: statsLoading ? '...' : formatCount(counters.media),
    },
    {
      id: 'users',
      label: 'Usuários',
      icon: <MdPeople className="w-5 h-5" />,
      path: '/admin/users',
      badge: formatCount(counters.users),
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <MdAnalytics className="w-5 h-5" />,
      path: '/admin/analytics',
    },
    {
      id: 'settings',
      label: 'Configurações',
      icon: <MdSettings className="w-5 h-5" />,
      path: '/admin/settings',
    },
  ];

  // Todos os itens são visíveis para o usuário único
  const filteredNavItems = navItems;

  // Sistema simplificado - sempre admin
  const getRoleBadge = () => {
    return {
      icon: <MdAdminPanelSettings className="w-4 h-4" />,
      label: 'Admin',
      color: 'from-neon-cyan to-blue-400',
    };
  };

  if (!user) return null;

  const roleBadge = getRoleBadge();

  return (
    <div
      className={clsx('min-h-screen bg-cyber-bg text-cyber-text', className)}>
      {/* Top Header */}
      <header className="sticky top-0 z-50 border-b bg-cyber-surface border-cyber-border">
        <div className="flex justify-between items-center px-6 py-4">
          {/* Left Side */}
          <div className="flex items-center space-x-4">
            {/* Sidebar Toggle */}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-lg transition-colors hover:bg-cyber-bg">
              <MdMenu className="w-5 h-5" />
            </button>

            {/* Logo */}
            <Link to="/admin" className="flex items-center space-x-2">
              <MdBolt className="w-6 h-6 text-neon-cyan" />
              <span className="text-xl font-bold text-transparent bg-clip-text bg-gradient-neon">
                Blueprint CMS
              </span>
            </Link>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Quick Actions - Hidden on mobile */}
            <div className="hidden items-center space-x-2 md:flex">
              <Button size="sm" leftIcon={<MdCreate className="w-4 h-4" />}>
                <Link to="/admin/posts/new">Novo Post</Link>
              </Button>

              <Button
                variant="outline"
                size="sm"
                leftIcon={<MdPublic className="w-4 h-4" />}>
                <Link to="/" target="_blank">
                  Ver Site
                </Link>
              </Button>
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-2 md:space-x-3">
              <AuthorAvatar avatar={user.avatar} name={user.name} size="md" />
              <div className="hidden md:block">
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-cyber-muted">{roleBadge.label}</p>
              </div>
              <Button variant="outline" size="sm" onClick={logout}>
                <span className="hidden sm:inline">Sair</span>
                <MdLogout className="sm:hidden w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside
          className={clsx(
            'bg-cyber-surface border-r border-cyber-border transition-all duration-300',
            'hidden lg:block', // Sempre oculto em mobile, visível em desktop
            sidebarOpen ? 'w-64' : 'w-16'
          )}>
          <nav className="p-4 space-y-2">
            {filteredNavItems.map((item) => {
              const isActive = location.pathname === item.path;

              return (
                <Link
                  key={item.id}
                  to={item.path}
                  className={clsx(
                    'flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200',
                    isActive
                      ? 'bg-neon-cyan/20 text-neon-cyan border border-neon-cyan/30'
                      : 'hover:bg-cyber-bg hover:text-neon-cyan',
                    !sidebarOpen && 'justify-center'
                  )}>
                  <span className="flex-shrink-0 text-lg">{item.icon}</span>
                  {sidebarOpen && (
                    <>
                      <span className="flex-1">{item.label}</span>
                      {item.badge && (
                        <span className="px-2 py-1 text-xs rounded-full bg-neon-cyan/20 text-neon-cyan">
                          {item.badge}
                        </span>
                      )}
                    </>
                  )}
                </Link>
              );
            })}
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 min-h-screen">
          <div className="p-6">{children}</div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
