import { <PERSON>, EyeOff, Lock, LogIn, Mail, Shield, Terminal } from 'lucide-react';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Link, useLocation, useNavigate } from 'react-router';
import { useAuth } from '../../contexts/AuthContext';

// 🎯 TypeScript interfaces
interface FormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, loading } = useAuth();

  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const from = location.state?.from?.pathname || '/admin';

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    if (!formData.email || !formData.password) {
      setError('Por favor, preencha todos os campos.');
      setIsSubmitting(false);
      return;
    }

    toast.loading('Fazendo login...', { id: 'login-loading' });

    try {
      const { user, error } = await signIn(formData.email, formData.password);

      if (error) {
        setError(error);
        toast.error(`Erro no login: ${error}`, { id: 'login-loading' });
      } else if (user) {
        toast.success('Login realizado com sucesso!', { id: 'login-loading' });
        navigate(from, { replace: true });
      }
    } catch (err) {
      setError('Erro inesperado. Tente novamente.');
      toast.error('Erro inesperado no login', { id: 'login-loading' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // Limpar erro quando usuário começar a digitar
    if (error) setError('');
  };

  return (
    <div className="pt-16 min-h-screen">
      {/* Hero Section */}
      <section className="px-4 py-12 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16">
            {/* Left Side - Branding */}
            <div className="flex flex-col justify-center space-y-8">
              <div className="text-center lg:text-left">
                <Link to="/" className="inline-block mb-6">
                  <div className="flex items-center gap-3">
                    <Terminal className="w-8 h-8 text-adaptive-blue" />
                    <h1 className="text-3xl font-bold text-gradient">
                      Blueprint Blog
                    </h1>
                  </div>
                </Link>

                <h2 className="text-4xl font-bold mb-4 md:text-5xl">
                  Painel Administrativo
                </h2>

                <p className="text-xl text-adaptive-secondary mb-8">
                  Acesse sua área de administração para gerenciar posts,
                  configurações e conteúdo do blog.
                </p>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="p-4 rounded-lg bg-adaptive-card/30 border border-adaptive">
                    <div className="flex items-center gap-3 mb-2">
                      <Terminal className="w-5 h-5 text-adaptive-blue" />
                      <h3 className="font-semibold">Gerenciamento</h3>
                    </div>
                    <p className="text-sm text-adaptive-secondary">
                      Posts, páginas e configurações
                    </p>
                  </div>

                  <div className="p-4 rounded-lg bg-adaptive-card/30 border border-adaptive">
                    <div className="flex items-center gap-3 mb-2">
                      <Shield className="w-5 h-5 text-neon-green" />
                      <h3 className="font-semibold">Seguro</h3>
                    </div>
                    <p className="text-sm text-adaptive-secondary">
                      Acesso protegido e autenticado
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="flex flex-col justify-center">
              <div className="w-full max-w-md mx-auto lg:mx-0">
                <div className="p-8 rounded-lg bg-adaptive-card border border-adaptive shadow-2xl">
                  <div className="mb-6 text-center">
                    <h3 className="text-2xl font-bold mb-2">Fazer Login</h3>
                    <p className="text-adaptive-secondary">
                      Entre com suas credenciais
                    </p>
                  </div>

                  {error && (
                    <div className="p-4 mb-6 text-red-400 border rounded-lg bg-red-500/20 border-red-500/30 animate-pulse">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">⚠️</span>
                        <span className="font-medium">{error}</span>
                      </div>
                    </div>
                  )}

                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Email */}
                    <div>
                      <label className="block mb-2 text-sm font-medium">
                        Email
                      </label>
                      <div className="relative group">
                        <Mail className="absolute w-5 h-5 transform -translate-y-1/2 left-3 top-1/2 text-adaptive-secondary group-focus-within:text-adaptive-blue transition-colors" />
                        <input
                          type="email"
                          value={formData.email}
                          onChange={(e) =>
                            handleInputChange('email', e.target.value)
                          }
                          className="w-full py-3 pl-10 pr-4 border rounded-lg bg-adaptive-bg border-adaptive focus:border-neon-blue focus:outline-none transition-all duration-200 focus:ring-2 focus:ring-neon-blue/20"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>

                    {/* Password */}
                    <div>
                      <label className="block mb-2 text-sm font-medium">
                        Senha
                      </label>
                      <div className="relative group">
                        <Lock className="absolute w-5 h-5 transform -translate-y-1/2 left-3 top-1/2 text-adaptive-secondary group-focus-within:text-adaptive-blue transition-colors" />
                        <input
                          type={showPassword ? 'text' : 'password'}
                          value={formData.password}
                          onChange={(e) =>
                            handleInputChange('password', e.target.value)
                          }
                          className="w-full py-3 pl-10 pr-12 border rounded-lg bg-adaptive-bg border-adaptive focus:border-neon-blue focus:outline-none transition-all duration-200 focus:ring-2 focus:ring-neon-blue/20"
                          placeholder="Sua senha"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute transform -translate-y-1/2 right-3 top-1/2 text-adaptive-secondary hover:text-adaptive-blue transition-colors">
                          {showPassword ? (
                            <EyeOff className="w-5 h-5" />
                          ) : (
                            <Eye className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <button
                      type="submit"
                      disabled={loading || isSubmitting}
                      className="flex items-center justify-center w-full gap-2 px-4 py-3 font-medium transition-all duration-200 rounded-lg bg-neon-blue text-dark-bg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 hover:shadow-lg">
                      {loading || isSubmitting ? (
                        <div className="w-5 h-5 border-2 rounded-full border-dark-bg border-t-transparent animate-spin" />
                      ) : (
                        <>
                          <LogIn className="w-5 h-5" />
                          Entrar
                        </>
                      )}
                    </button>
                  </form>

                  {/* Links */}
                  <div className="mt-6 space-y-4 text-center">
                    <Link
                      to="/forgot-password"
                      className="text-sm transition-colors text-neon-blue hover:text-blue-400 hover:underline">
                      Esqueceu sua senha?
                    </Link>

                    <div className="text-sm text-adaptive-secondary">
                      Acesso restrito ao administrador do blog
                    </div>
                  </div>
                </div>

                {/* Back to site */}
                <div className="mt-6 text-center">
                  <Link
                    to="/"
                    className="text-sm transition-colors text-adaptive-secondary hover:text-adaptive-blue hover:underline">
                    ← Voltar ao site
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Login;
