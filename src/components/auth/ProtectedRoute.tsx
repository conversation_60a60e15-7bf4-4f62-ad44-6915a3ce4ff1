import type { ReactNode } from 'react';
import { Navigate } from 'react-router';
import { useAuth } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
}

function ProtectedRoute({ children, requireAuth = true }: ProtectedRouteProps) {
  const { user, status } = useAuth();

  // Mostrar loading enquanto verifica autenticação
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-adaptive">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-adaptive-blue border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-adaptive-secondary">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  // Se requer autenticação e não está logado, redirecionar para login
  if (requireAuth && !user) {
    return <Navigate to="/login" replace />;
  }

  // Se não requer autenticação e está logado, redirecionar para admin
  if (!requireAuth && user) {
    return <Navigate to="/admin" replace />;
  }

  // Sistema simplificado - usuário único sempre tem acesso total
  return <>{children}</>;
}

export default ProtectedRoute;
