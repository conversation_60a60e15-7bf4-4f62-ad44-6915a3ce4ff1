import { track } from '@vercel/analytics';

/**
 * Componente helper para tracking de eventos customizados
 * Útil para acompanhar interações específicas do usuário
 */

// Tipos de eventos que queremos trackear
type AnalyticsEvent =
  | 'post_view'
  | 'post_like'
  | 'post_share'
  | 'search_performed'
  | 'theme_changed'
  | 'project_view'
  | 'contact_form_submitted'
  | 'newsletter_signup';

interface AnalyticsEventData {
  [key: string]: string | number | boolean;
}

/**
 * Função para trackear eventos customizados
 */
export const trackEvent = (
  event: AnalyticsEvent,
  data?: AnalyticsEventData
) => {
  // Só trackeamos em produção para não poluir os dados de desenvolvimento
  if (process.env.NODE_ENV === 'production') {
    track(event, data);
  }
  // Removido log em desenvolvimento para manter console limpo
};

/**
 * Hook para tracking de page views
 */
export const usePageTracking = () => {
  const trackPageView = (
    pageName: string,
    additionalData?: AnalyticsEventData
  ) => {
    trackEvent('post_view', {
      page: pageName,
      timestamp: Date.now(),
      ...additionalData,
    });
  };

  return { trackPageView };
};

/**
 * Eventos pré-configurados para uso comum
 */
export const AnalyticsEvents = {
  // Post interactions
  viewPost: (slug: string, title: string) =>
    trackEvent('post_view', { slug, title }),

  likePost: (slug: string) => trackEvent('post_like', { slug }),

  sharePost: (slug: string, platform: string) =>
    trackEvent('post_share', { slug, platform }),

  // Search
  performSearch: (query: string, resultsCount: number) =>
    trackEvent('search_performed', { query, resultsCount }),

  // Theme
  changeTheme: (newTheme: string) =>
    trackEvent('theme_changed', { theme: newTheme }),

  // Projects
  viewProject: (projectName: string) =>
    trackEvent('project_view', { project: projectName }),

  // Contact
  submitContactForm: (type: string) =>
    trackEvent('contact_form_submitted', { type }),

  // Newsletter
  signupNewsletter: (source: string) =>
    trackEvent('newsletter_signup', { source }),
};

export default AnalyticsEvents;
