import { Bar<PERSON><PERSON><PERSON>, <PERSON>, Heart, TrendingUp, Users } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { useBlogStats, usePopularPosts } from '../../hooks/usePostAnalytics';
import DashboardLayout from '../layout/DashboardLayout';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: string;
  color: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  trend,
  color,
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className={`card border-${color}/20 bg-${color}/5`}>
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm text-adaptive-secondary">{title}</p>
        <p className={`text-2xl font-bold text-${color}`}>{value}</p>
        {trend && (
          <p className="text-xs text-adaptive-secondary mt-1">{trend}</p>
        )}
      </div>
      <div className={`p-3 rounded-lg bg-${color}/10`}>{icon}</div>
    </div>
  </motion.div>
);

const AnalyticsDashboard: React.FC = () => {
  const { stats, loading: statsLoading, error: statsError } = useBlogStats();
  const { popularPosts, loading: postsLoading } = usePopularPosts(5);
  const [webVitals, setWebVitals] = useState<any>(null);

  // Carregar Web Vitals
  useEffect(() => {
    import('web-vitals')
      .then((vitals: any) => {
        const { onCLS, onFID, onLCP } = vitals;

        if (onCLS) {
          onCLS((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, cls: metric.value }));
          });
        }

        if (onFID) {
          onFID((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, fid: metric.value }));
          });
        }

        if (onLCP) {
          onLCP((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, lcp: metric.value }));
          });
        }
      })
      .catch(console.error);
  }, []);

  if (statsLoading || postsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card animate-pulse">
              <div className="h-20 bg-adaptive-card rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <div className="card border-red-500/20 bg-red-500/5">
        <p className="text-red-400">Erro ao carregar analytics: {statsError}</p>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-cyber-text mb-2">
            📈 Analytics Dashboard
          </h1>
          <p className="text-cyber-muted">Métricas e performance do blog</p>
        </div>
        {/* Métricas Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total de Posts"
            value={stats?.totalPosts || 0}
            icon={<BarChart3 className="h-6 w-6 text-adaptive-blue" />}
            color="adaptive-blue"
          />

          <MetricCard
            title="Total de Views"
            value={stats?.totalViews?.toLocaleString() || 0}
            icon={<Eye className="h-6 w-6 text-adaptive-green" />}
            color="adaptive-green"
          />

          <MetricCard
            title="Média por Post"
            value={stats?.avgViewsPerPost || 0}
            icon={<TrendingUp className="h-6 w-6 text-adaptive-purple" />}
            color="adaptive-purple"
          />

          <MetricCard
            title="Post Popular"
            value={stats?.mostPopularPost?.views || 0}
            icon={<Heart className="h-6 w-6 text-adaptive-orange" />}
            trend={stats?.mostPopularPost?.title}
            color="adaptive-orange"
          />
        </div>

        {/* Web Vitals */}
        {webVitals && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gradient mb-4">
              Core Web Vitals
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border border-adaptive rounded-lg">
                <p className="text-sm text-adaptive-secondary">LCP</p>
                <p className="text-xl font-bold text-adaptive-blue">
                  {webVitals.lcp ? `${Math.round(webVitals.lcp)}ms` : '-'}
                </p>
                <p className="text-xs text-adaptive-secondary">
                  Largest Contentful Paint
                </p>
              </div>

              <div className="text-center p-4 border border-adaptive rounded-lg">
                <p className="text-sm text-adaptive-secondary">FID</p>
                <p className="text-xl font-bold text-adaptive-green">
                  {webVitals.fid ? `${Math.round(webVitals.fid)}ms` : '-'}
                </p>
                <p className="text-xs text-adaptive-secondary">
                  First Input Delay
                </p>
              </div>

              <div className="text-center p-4 border border-adaptive rounded-lg">
                <p className="text-sm text-adaptive-secondary">CLS</p>
                <p className="text-xl font-bold text-adaptive-purple">
                  {webVitals.cls ? webVitals.cls.toFixed(3) : '-'}
                </p>
                <p className="text-xs text-adaptive-secondary">
                  Cumulative Layout Shift
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Posts Populares */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gradient mb-4">
            Posts Mais Populares
          </h3>
          <div className="space-y-3">
            {popularPosts.map((post, index) => (
              <motion.div
                key={post.slug}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 border border-adaptive rounded-lg hover:bg-adaptive-card/50 transition-colors">
                <div className="flex items-center gap-3">
                  <span className="flex items-center justify-center w-8 h-8 rounded-full bg-adaptive-blue/10 text-adaptive-blue font-semibold text-sm">
                    {index + 1}
                  </span>
                  <div>
                    <h4 className="font-medium text-adaptive">{post.title}</h4>
                    <p className="text-sm text-adaptive-secondary">
                      {post.slug}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-adaptive-secondary">
                  <Eye className="h-4 w-4" />
                  <span className="font-medium">{post.views}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Analytics Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="card border-adaptive-blue/20 bg-adaptive-blue/5">
            <h3 className="text-lg font-semibold text-adaptive-blue mb-2">
              Google Analytics
            </h3>
            <p className="text-sm text-adaptive-secondary mb-4">
              Métricas detalhadas de tráfego e comportamento dos usuários
            </p>
            <a
              href="https://analytics.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-adaptive-blue text-white rounded-lg hover:bg-adaptive-blue/80 transition-colors">
              <Users className="h-4 w-4" />
              Abrir GA4
            </a>
          </div>

          <div className="card border-adaptive-green/20 bg-adaptive-green/5">
            <h3 className="text-lg font-semibold text-adaptive-green mb-2">
              Vercel Analytics
            </h3>
            <p className="text-sm text-adaptive-secondary mb-4">
              Performance e Core Web Vitals em tempo real
            </p>
            <a
              href="https://vercel.com/analytics"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-adaptive-green text-white rounded-lg hover:bg-adaptive-green/80 transition-colors">
              <TrendingUp className="h-4 w-4" />
              Abrir Vercel
            </a>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AnalyticsDashboard;
