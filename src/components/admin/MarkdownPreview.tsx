/**
 * @fileoverview Componente de preview de Markdown para o editor
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useMemo } from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

interface MarkdownPreviewProps {
  content: string;
  className?: string;
}

// ============================================================================
// UTILS
// ============================================================================

/**
 * Converte Markdown básico para HTML
 * Implementação simples para demonstração
 */
const parseMarkdown = (markdown: string): string => {
  let html = markdown;

  // Headers
  html = html.replace(
    /^### (.*$)/gim,
    '<h3 class="text-xl font-bold text-cyber-text mb-3 mt-6">$1</h3>'
  );
  html = html.replace(
    /^## (.*$)/gim,
    '<h2 class="text-2xl font-bold text-cyber-text mb-4 mt-8">$1</h2>'
  );
  html = html.replace(
    /^# (.*$)/gim,
    '<h1 class="text-3xl font-bold text-cyber-text mb-6 mt-8">$1</h1>'
  );

  // Bold
  html = html.replace(
    /\*\*(.*?)\*\*/g,
    '<strong class="font-bold text-neon-cyan">$1</strong>'
  );

  // Italic
  html = html.replace(
    /\*(.*?)\*/g,
    '<em class="italic text-neon-magenta">$1</em>'
  );

  // Code inline
  html = html.replace(
    /`(.*?)`/g,
    '<code class="px-2 py-1 bg-cyber-surface border border-cyber-border rounded text-neon-yellow font-mono text-sm">$1</code>'
  );

  // Code blocks
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (_, __, code) => {
    return `<pre class="bg-cyber-surface border border-cyber-border rounded-lg p-4 my-4 overflow-x-auto"><code class="text-neon-green font-mono text-sm">${code.trim()}</code></pre>`;
  });

  // Links
  html = html.replace(
    /\[([^\]]+)\]\(([^)]+)\)/g,
    '<a href="$2" class="text-neon-cyan hover:text-neon-magenta underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>'
  );

  // Lists
  html = html.replace(
    /^\* (.*$)/gim,
    '<li class="text-cyber-text mb-1">$1</li>'
  );
  html = html.replace(
    /^- (.*$)/gim,
    '<li class="text-cyber-text mb-1">$1</li>'
  );

  // Wrap lists
  html = html.replace(
    /(<li.*<\/li>)/gs,
    '<ul class="list-disc list-inside space-y-1 my-4 ml-4">$1</ul>'
  );

  // Paragraphs
  html = html.replace(
    /^(?!<[h|u|p|c])(.*$)/gim,
    '<p class="text-cyber-text mb-4 leading-relaxed">$1</p>'
  );

  // Line breaks
  html = html.replace(/\n/g, '<br>');

  return html;
};

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Preview em tempo real do conteúdo Markdown
 */
export const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({
  content,
  className,
}) => {
  const htmlContent = useMemo(() => {
    if (!content.trim()) {
      return '<div class="text-center py-12 text-cyber-muted"><div class="text-4xl mb-4">📝</div><p>Comece a escrever para ver o preview...</p></div>';
    }

    return parseMarkdown(content);
  }, [content]);

  return (
    <div className={clsx('prose prose-invert max-w-none', className)}>
      <div
        className="markdown-preview"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
};

export default MarkdownPreview;
