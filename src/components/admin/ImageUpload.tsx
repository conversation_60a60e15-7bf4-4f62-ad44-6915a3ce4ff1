/**
 * @fileoverview Componente de upload de imagens simulado
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useState, useRef } from 'react';
import { clsx } from 'clsx';
import { Button } from '../ui';

// ============================================================================
// INTERFACES
// ============================================================================

interface ImageUploadProps {
  onImageSelect: (imageUrl: string) => void;
  currentImage?: string;
  className?: string;
}

interface UploadedImage {
  id: string;
  url: string;
  name: string;
  size: number;
  uploadedAt: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Componente de upload de imagens com preview
 * Simula upload usando URLs do Unsplash
 */
export const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageSelect,
  currentImage,
  className,
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [showGallery, setShowGallery] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Imagens mockadas da galeria
  const galleryImages: UploadedImage[] = [
    {
      id: '1',
      url: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
      name: 'tech-workspace.jpg',
      size: 245760,
      uploadedAt: '2025-06-14',
    },
    {
      id: '2',
      url: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop',
      name: 'coding-screen.jpg',
      size: 189440,
      uploadedAt: '2025-06-13',
    },
    {
      id: '3',
      url: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
      name: 'programming.jpg',
      size: 312320,
      uploadedAt: '2025-06-12',
    },
    {
      id: '4',
      url: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=800&h=400&fit=crop',
      name: 'laptop-code.jpg',
      size: 278528,
      uploadedAt: '2025-06-11',
    },
    {
      id: '5',
      url: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=400&fit=crop',
      name: 'server-room.jpg',
      size: 356352,
      uploadedAt: '2025-06-10',
    },
    {
      id: '6',
      url: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop',
      name: 'ai-circuit.jpg',
      size: 423936,
      uploadedAt: '2025-06-09',
    },
  ];

  /**
   * Simula upload de arquivo
   */
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validações
    if (!file.type.startsWith('image/')) {
      alert('Por favor, selecione apenas arquivos de imagem.');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      alert('A imagem deve ter no máximo 5MB.');
      return;
    }

    setUploading(true);

    // Simula delay de upload
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simula URL de upload (usando uma imagem aleatória do Unsplash)
    const randomId = Math.floor(Math.random() * 1000);
    const simulatedUrl = `https://images.unsplash.com/photo-${1500000000000 + randomId}?w=800&h=400&fit=crop`;
    
    onImageSelect(simulatedUrl);
    setUploading(false);
  };

  /**
   * Handle drag and drop
   */
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  /**
   * Formata tamanho do arquivo
   */
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={clsx('space-y-4', className)}>
      {/* Current Image Preview */}
      {currentImage && (
        <div className="relative">
          <img
            src={currentImage}
            alt="Preview"
            className="w-full h-48 object-cover rounded-lg border border-cyber-border"
          />
          <button
            onClick={() => onImageSelect('')}
            className="absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
          >
            ✕
          </button>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={clsx(
          'border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200',
          dragOver
            ? 'border-neon-cyan bg-neon-cyan/10'
            : 'border-cyber-border hover:border-neon-cyan/50',
          uploading && 'opacity-50 pointer-events-none'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {uploading ? (
          <div className="space-y-4">
            <div className="w-12 h-12 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="text-cyber-text">Fazendo upload...</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-6xl">📸</div>
            <div>
              <p className="text-cyber-text font-medium mb-2">
                Arraste uma imagem aqui ou clique para selecionar
              </p>
              <p className="text-cyber-muted text-sm">
                Suporte: JPG, PNG, GIF (máx. 5MB)
              </p>
            </div>
            
            <div className="flex justify-center space-x-3">
              <Button
                variant="outline"
                leftIcon="📁"
                onClick={() => fileInputRef.current?.click()}
              >
                Selecionar Arquivo
              </Button>
              
              <Button
                variant="outline"
                leftIcon="🖼️"
                onClick={() => setShowGallery(!showGallery)}
              >
                Galeria
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => handleFileUpload(e.target.files)}
        className="hidden"
      />

      {/* Gallery */}
      {showGallery && (
        <div className="border border-cyber-border rounded-lg p-4 bg-cyber-surface">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-cyber-text">
              📂 Galeria de Imagens
            </h3>
            <button
              onClick={() => setShowGallery(false)}
              className="text-cyber-muted hover:text-cyber-text"
            >
              ✕
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {galleryImages.map((image) => (
              <div
                key={image.id}
                className="group relative cursor-pointer rounded-lg overflow-hidden border border-cyber-border hover:border-neon-cyan transition-colors"
                onClick={() => {
                  onImageSelect(image.url);
                  setShowGallery(false);
                }}
              >
                <img
                  src={image.url}
                  alt={image.name}
                  className="w-full h-24 object-cover group-hover:scale-105 transition-transform duration-200"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-cyber-bg/80 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                  <div className="text-center text-xs text-cyber-text">
                    <p className="font-medium truncate px-2">{image.name}</p>
                    <p className="text-cyber-muted">{formatFileSize(image.size)}</p>
                  </div>
                </div>
                
                {/* Selected Indicator */}
                {currentImage === image.url && (
                  <div className="absolute top-2 right-2 w-6 h-6 bg-neon-cyan text-cyber-bg rounded-full flex items-center justify-center text-xs">
                    ✓
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <p className="text-cyber-muted text-sm">
              {galleryImages.length} imagens disponíveis
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
