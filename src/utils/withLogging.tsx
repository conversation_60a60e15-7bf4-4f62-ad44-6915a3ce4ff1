/**
 * @fileoverview HOC para adicionar logging automático a componentes
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useEffect } from 'react';
import { ComponentLogger } from './component-logger';

/**
 * HOC que adiciona logging automático de mount/unmount a qualquer componente
 *
 * @param WrappedComponent - Componente a ser envolvido
 * @param componentName - Nome personalizado para o componente (opcional)
 * @returns Componente com logging automático
 *
 * @example
 * ```tsx
 * const MyComponent = ({ title }: { title: string }) => (
 *   <div>{title}</div>
 * );
 *
 * export default withLogging(MyComponent, 'MyCustomComponent');
 * ```
 */
export function withLogging<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const LoggedComponent = (props: P) => {
    const name =
      componentName ||
      WrappedComponent.displayName ||
      WrappedComponent.name ||
      'UnknownComponent';

    useEffect(() => {
      ComponentLogger.logMount(name, props);

      return () => {
        ComponentLogger.logUnmount(name);
      };
    }, [name]);

    return <WrappedComponent {...props} />;
  };

  // Preservar nome do componente para debugging
  LoggedComponent.displayName = `withLogging(${
    componentName || WrappedComponent.displayName || WrappedComponent.name
  })`;

  return LoggedComponent;
}

/**
 * HOC que adiciona logging de props e performance
 *
 * @param WrappedComponent - Componente a ser envolvido
 * @param options - Opções de configuração
 * @returns Componente com logging avançado
 */
export function withAdvancedLogging<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    componentName?: string;
    logProps?: boolean;
    logPerformance?: boolean;
    logRenders?: boolean;
  } = {}
) {
  const {
    componentName,
    logProps = false,
    logPerformance = false,
    logRenders = false,
  } = options;

  const LoggedComponent = (props: P) => {
    const name =
      componentName ||
      WrappedComponent.displayName ||
      WrappedComponent.name ||
      'UnknownComponent';
    const renderStartTime = React.useRef<number>(Date.now());
    const renderCount = React.useRef<number>(0);

    // Log de renders
    if (logRenders) {
      renderCount.current += 1;
      ComponentLogger.logMount(`${name}.render`, {
        renderCount: renderCount.current,
        props: logProps ? props : undefined,
      });
    }

    useEffect(() => {
      const mountTime = Date.now();

      ComponentLogger.logMount(name, {
        props: logProps ? props : undefined,
        performance: logPerformance
          ? {
              mountTime,
              renderTime: mountTime - renderStartTime.current,
            }
          : undefined,
      });

      return () => {
        const unmountTime = Date.now();
        ComponentLogger.logUnmount(name);

        if (logPerformance) {
          ComponentLogger.logMount(`${name}.performance`, {
            totalLifetime: unmountTime - mountTime,
            totalRenders: renderCount.current,
          });
        }
      };
    }, [name]);

    // Atualizar tempo de início do render
    renderStartTime.current = Date.now();

    return <WrappedComponent {...props} />;
  };

  LoggedComponent.displayName = `withAdvancedLogging(${
    componentName || WrappedComponent.displayName || WrappedComponent.name
  })`;

  return LoggedComponent;
}

/**
 * HOC específico para páginas
 * Adiciona logging de navegação e tempo de carregamento
 */
export function withPageLogging<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  pageName?: string
) {
  const LoggedPage = (props: P) => {
    const name =
      pageName ||
      WrappedComponent.displayName ||
      WrappedComponent.name ||
      'UnknownPage';

    useEffect(() => {
      const pageLoadStart = Date.now();

      ComponentLogger.logMount(`Page.${name}`, {
        url: window.location.href,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
      });

      // Log quando a página termina de carregar
      const handleLoad = () => {
        ComponentLogger.logMount(`Page.${name}.loaded`, {
          loadTime: Date.now() - pageLoadStart,
        });
      };

      if (document.readyState === 'complete') {
        handleLoad();
      } else {
        window.addEventListener('load', handleLoad);
      }

      return () => {
        ComponentLogger.logUnmount(`Page.${name}`);
        window.removeEventListener('load', handleLoad);
      };
    }, [name]);

    return <WrappedComponent {...props} />;
  };

  LoggedPage.displayName = `withPageLogging(${
    pageName || WrappedComponent.displayName || WrappedComponent.name
  })`;

  return LoggedPage;
}
