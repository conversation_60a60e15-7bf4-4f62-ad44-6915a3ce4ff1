/**
 * @fileoverview Utilitário para promoção de usuário
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { supabaseAuthService } from '../services/supabaseAuthService';

/**
 * Promove o usuário atual para writer
 * Execute no console do navegador: promoteCurrentUserToWriter()
 */
export const promoteCurrentUserToWriter = async (): Promise<void> => {
  try {
    console.log('🔄 Promovendo usuário atual para Writer...');

    // Obter usuário atual
    const { user: currentUser, error: getUserError } =
      await supabaseAuthService.getCurrentUser();

    if (getUserError || !currentUser) {
      console.log('❌ Erro: Nenhum usuário logado encontrado');
      return;
    }

    console.log('✅ Usuário encontrado:');
    console.log(`   📧 Email: ${currentUser.email}`);
    console.log(`   👤 Nome: ${currentUser.name}`);
    console.log(`   🎭 Role atual: ${currentUser.role}`);

    // Verificar se já é writer ou superior
    const roleHierarchy = ['reader', 'writer', 'editor', 'admin'];
    const currentLevel = roleHierarchy.indexOf(currentUser.role);
    const writerLevel = roleHierarchy.indexOf('writer');

    if (currentLevel >= writerLevel) {
      console.log('ℹ️  Usuário já tem permissões de Writer ou superior!');
      return;
    }

    // Promover para writer
    const { error } = await supabaseAuthService.updateUser({ role: 'admin' });

    if (error) {
      console.log('❌ Erro ao promover usuário:', error);
      return;
    }

    console.log('');
    console.log('🎉 SUCESSO! Usuário promovido para Writer!');
    console.log('');
    console.log('✅ Permissões concedidas:');
    console.log('   ✍️ Criar posts');
    console.log('   📝 Editar seus próprios posts');
    console.log('   👀 Ver todos os posts');
    console.log('');
    console.log('🔄 Recarregue a página para aplicar as mudanças.');
    console.log('');
  } catch (error) {
    console.log('❌ Erro inesperado:', error);
  }
};

/**
 * Promove o usuário atual para admin
 * Execute no console do navegador: promoteCurrentUserToAdmin()
 */
export const promoteCurrentUserToAdmin = async (): Promise<void> => {
  try {
    console.log('🔄 Promovendo usuário atual para Admin...');

    // Sistema simplificado - usuário sempre é admin
    const result = { success: true, error: null };

    if (result.success) {
      console.log('🔄 Recarregue a página para aplicar as mudanças.');
    }
  } catch (error) {
    console.log('❌ Erro inesperado:', error);
  }
};

// Expor funções globalmente para uso no console
if (typeof window !== 'undefined') {
  (window as any).promoteCurrentUserToWriter = promoteCurrentUserToWriter;
  (window as any).promoteCurrentUserToAdmin = promoteCurrentUserToAdmin;
}
