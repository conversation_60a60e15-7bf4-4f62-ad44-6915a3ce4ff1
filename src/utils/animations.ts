/**
 * @fileoverview Configurações de animações com Framer Motion
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import type { Variants, Transition } from 'motion/react';

// ============================================================================
// TRANSIÇÕES BÁSICAS
// ============================================================================

/**
 * Transições padrão para diferentes tipos de animação
 */
export const transitions = {
  // Transição suave padrão
  smooth: {
    type: 'spring',
    stiffness: 100,
    damping: 15,
    mass: 1,
  } as Transition,

  // Transição rápida
  fast: {
    type: 'spring',
    stiffness: 200,
    damping: 20,
    mass: 0.8,
  } as Transition,

  // Transição lenta
  slow: {
    type: 'spring',
    stiffness: 50,
    damping: 20,
    mass: 1.2,
  } as Transition,

  // Transição linear
  linear: {
    type: 'tween',
    duration: 0.3,
    ease: 'easeInOut',
  } as Transition,

  // Transição cyberpunk (mais dramática)
  cyberpunk: {
    type: 'spring',
    stiffness: 150,
    damping: 12,
    mass: 0.9,
  } as Transition,
};

// ============================================================================
// VARIANTES DE ANIMAÇÃO
// ============================================================================

/**
 * Animações de fade-in com diferentes direções
 */
export const fadeInVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: transitions.smooth,
  },
  exit: {
    opacity: 0,
    transition: transitions.fast,
  },
};

/**
 * Animações de slide com diferentes direções
 */
export const slideVariants = {
  // Slide da esquerda
  fromLeft: {
    hidden: {
      opacity: 0,
      x: -50,
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      x: -50,
      transition: transitions.fast,
    },
  } as Variants,

  // Slide da direita
  fromRight: {
    hidden: {
      opacity: 0,
      x: 50,
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      x: 50,
      transition: transitions.fast,
    },
  } as Variants,

  // Slide de cima
  fromTop: {
    hidden: {
      opacity: 0,
      y: -50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      y: -50,
      transition: transitions.fast,
    },
  } as Variants,

  // Slide de baixo
  fromBottom: {
    hidden: {
      opacity: 0,
      y: 50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      y: 50,
      transition: transitions.fast,
    },
  } as Variants,
};

/**
 * Animações de escala
 */
export const scaleVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: transitions.cyberpunk,
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: transitions.fast,
  },
};

/**
 * Animações de rotação
 */
export const rotateVariants: Variants = {
  hidden: {
    opacity: 0,
    rotate: -10,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: transitions.cyberpunk,
  },
  exit: {
    opacity: 0,
    rotate: 10,
    scale: 0.9,
    transition: transitions.fast,
  },
};

/**
 * Animações de blur (efeito cyberpunk)
 */
export const blurVariants: Variants = {
  hidden: {
    opacity: 0,
    filter: 'blur(10px)',
    scale: 1.1,
  },
  visible: {
    opacity: 1,
    filter: 'blur(0px)',
    scale: 1,
    transition: transitions.smooth,
  },
  exit: {
    opacity: 0,
    filter: 'blur(10px)',
    scale: 1.1,
    transition: transitions.fast,
  },
};

/**
 * Animações de glitch (efeito cyberpunk)
 */
export const glitchVariants: Variants = {
  hidden: {
    opacity: 0,
    x: 0,
    y: 0,
  },
  visible: {
    opacity: 1,
    x: [0, -2, 2, 0],
    y: [0, 2, -2, 0],
    transition: {
      duration: 0.3,
      times: [0, 0.3, 0.6, 1],
      ease: 'easeInOut',
    },
  },
  exit: {
    opacity: 0,
    transition: transitions.fast,
  },
};

// ============================================================================
// ANIMAÇÕES DE CONTAINER
// ============================================================================

/**
 * Container para animações staggered (em cascata)
 */
export const containerVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
};

/**
 * Item para animações staggered
 */
export const itemVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: transitions.smooth,
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: transitions.fast,
  },
};

// ============================================================================
// ANIMAÇÕES ESPECIAIS
// ============================================================================

/**
 * Animação de typewriter (efeito de digitação)
 */
export const typewriterVariants: Variants = {
  hidden: {
    width: 0,
  },
  visible: {
    width: 'auto',
    transition: {
      duration: 2,
      ease: 'easeInOut',
    },
  },
};

/**
 * Animação de neon glow
 */
export const neonGlowVariants: Variants = {
  hidden: {
    opacity: 0,
    filter: 'drop-shadow(0 0 0px #00ffff)',
  },
  visible: {
    opacity: 1,
    filter: [
      'drop-shadow(0 0 5px #00ffff)',
      'drop-shadow(0 0 15px #00ffff)',
      'drop-shadow(0 0 5px #00ffff)',
    ],
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: 'reverse',
      ease: 'easeInOut',
    },
  },
};

/**
 * Animação de float (flutuação)
 */
export const floatVariants: Variants = {
  visible: {
    y: [0, -10, 0],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: 'easeInOut',
    },
  },
};

// ============================================================================
// PRESETS DE ANIMAÇÃO
// ============================================================================

/**
 * Presets prontos para uso comum
 */
export const animationPresets = {
  // Entrada suave
  fadeIn: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: fadeInVariants,
  },

  // Entrada da esquerda
  slideFromLeft: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: slideVariants.fromLeft,
  },

  // Entrada da direita
  slideFromRight: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: slideVariants.fromRight,
  },

  // Entrada de cima
  slideFromTop: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: slideVariants.fromTop,
  },

  // Entrada de baixo
  slideFromBottom: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: slideVariants.fromBottom,
  },

  // Escala
  scale: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: scaleVariants,
  },

  // Rotação
  rotate: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: rotateVariants,
  },

  // Blur cyberpunk
  blur: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: blurVariants,
  },

  // Container staggered
  staggerContainer: {
    initial: 'hidden',
    animate: 'visible',
    exit: 'exit',
    variants: containerVariants,
  },

  // Item staggered
  staggerItem: {
    variants: itemVariants,
  },
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Cria uma animação com delay personalizado
 */
export const withDelay = (preset: any, delay: number) => ({
  ...preset,
  transition: {
    ...preset.transition,
    delay,
  },
});

/**
 * Cria uma animação staggered personalizada
 */
export const createStaggered = (staggerDelay: number = 0.1, childDelay: number = 0.1) => ({
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: staggerDelay,
      delayChildren: childDelay,
    },
  },
});
