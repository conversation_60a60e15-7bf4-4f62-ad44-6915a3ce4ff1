import type { Post, SEOData } from '../types';

/**
 * Gera um slug SEO-friendly a partir de um texto
 */
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .normalize('NFD') // Decompor caracteres acentuados
    .replace(/[\u0300-\u036f]/g, '') // Remover acentos
    .replace(/[^a-z0-9\s-]/g, '') // Remover caracteres especiais
    .replace(/\s+/g, '-') // Substituir espaços por hífens
    .replace(/-+/g, '-') // Remover hífens duplicados
    .replace(/^-|-$/g, ''); // Remover hífens do início e fim
};

/**
 * Trunca texto para um tamanho específico mantendo palavras completas
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;

  const truncated = text.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(' ');

  if (lastSpace > 0) {
    return truncated.substring(0, lastSpace) + '...';
  }

  return truncated + '...';
};

/**
 * Extrai texto limpo do conteúdo HTML/Markdown
 */
export const extractPlainText = (htmlContent: string): string => {
  // Remove tags HTML
  const withoutTags = htmlContent.replace(/<[^>]*>/g, '');

  // Remove entidades HTML
  const withoutEntities = withoutTags
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");

  // Remove quebras de linha excessivas e espaços
  return withoutEntities
    .replace(/\n\s*\n/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();
};

/**
 * Valida se uma URL de imagem é válida
 */
export const isValidImageUrl = (url: string): boolean => {
  if (!url) return false;

  try {
    const urlObj = new URL(url);
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    const pathname = urlObj.pathname.toLowerCase();

    return validExtensions.some((ext) => pathname.endsWith(ext));
  } catch {
    return false;
  }
};

/**
 * Gera keywords automáticas baseadas no conteúdo
 */
export const generateKeywords = (
  title: string,
  content: string,
  categories?: string[],
  tags?: string[]
): string[] => {
  const commonWords = [
    'o',
    'a',
    'os',
    'as',
    'um',
    'uma',
    'uns',
    'umas',
    'de',
    'do',
    'da',
    'dos',
    'das',
    'em',
    'no',
    'na',
    'nos',
    'nas',
    'para',
    'por',
    'com',
    'sem',
    'sobre',
    'entre',
    'até',
    'desde',
    'que',
    'se',
    'como',
    'quando',
    'onde',
    'porque',
    'mas',
    'ou',
    'e',
    'é',
    'são',
    'foi',
    'foram',
    'ser',
    'estar',
    'ter',
    'haver',
    'este',
    'esta',
    'estes',
    'estas',
    'esse',
    'essa',
    'esses',
    'essas',
    'aquele',
    'aquela',
    'aqueles',
    'aquelas',
    'seu',
    'sua',
    'seus',
    'suas',
    'meu',
    'minha',
    'meus',
    'minhas',
    'nosso',
    'nossa',
    'nossos',
    'nossas',
  ];

  // Extrair palavras do título e conteúdo
  const allText = `${title} ${extractPlainText(content)}`.toLowerCase();
  const words = allText
    .split(/\s+/)
    .map((word) => word.replace(/[^a-záàâãéèêíìîóòôõúùûç]/g, ''))
    .filter((word) => word.length > 3 && !commonWords.includes(word));

  // Contar frequência das palavras
  const wordCount: Record<string, number> = {};
  words.forEach((word) => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  // Pegar as palavras mais frequentes
  const topWords = Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);

  // Combinar com categorias e tags
  const keywords = [
    ...topWords,
    ...(categories || []).map((cat) => cat.toLowerCase()),
    ...(tags || []).map((tag) => tag.toLowerCase()),
  ];

  // Remover duplicatas e retornar
  return [...new Set(keywords)].slice(0, 15);
};

/**
 * Valida dados SEO
 */
export const validateSEOData = (
  seo: SEOData
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validar título
  if (!seo.title) {
    errors.push('Título é obrigatório');
  } else if (seo.title.length > 60) {
    errors.push('Título deve ter no máximo 60 caracteres');
  }

  // Validar descrição
  if (!seo.description) {
    errors.push('Descrição é obrigatória');
  } else if (seo.description.length > 160) {
    errors.push('Descrição deve ter no máximo 160 caracteres');
  }

  // Validar URL canonical
  if (seo.canonical) {
    try {
      new URL(seo.canonical);
    } catch {
      errors.push('URL canonical inválida');
    }
  }

  // Validar imagem OG
  if (seo.ogImage && !isValidImageUrl(seo.ogImage)) {
    errors.push('URL da imagem Open Graph inválida');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Gera dados SEO automáticos para um post
 */
export const generatePostSEO = (post: Post): Partial<SEOData> => {
  const plainContent = extractPlainText(post.content);
  const excerpt = post.excerpt || truncateText(plainContent, 160);
  const keywords = generateKeywords(
    post.title,
    post.content,
    [post.category?.name].filter(Boolean),
    post.tags?.map((tag) => tag.name)
  );

  return {
    title: post.title,
    description: excerpt,
    keywords: keywords.join(', '),
    ogTitle: post.title,
    ogDescription: excerpt,
    ogImage: post.thumbnail,
    ogType: 'article',
    author: post.author?.name,
    publishedTime: post.createdAt,
    modifiedTime: post.updatedAt,
    section: post.category?.name,
    tags: post.tags?.map((tag) => tag.name),
  };
};

/**
 * Gera breadcrumbs para uma página
 */
export const generateBreadcrumbs = (
  pathname: string,
  post?: Post,
  category?: string,
  tag?: string
) => {
  const breadcrumbs = [{ name: 'Home', url: '/' }];

  const segments = pathname.split('/').filter(Boolean);

  if (segments[0] === 'blog') {
    breadcrumbs.push({ name: 'Blog', url: '/blog' });

    if (segments[1] === 'category' && category) {
      breadcrumbs.push({
        name: category,
        url: `/blog/category/${generateSlug(category)}`,
      });
    } else if (segments[1] === 'tag' && tag) {
      breadcrumbs.push({
        name: `#${tag}`,
        url: `/blog/tag/${generateSlug(tag)}`,
      });
    } else if (segments[1] && post) {
      breadcrumbs.push({ name: post.title, url: pathname });
    }
  }

  return breadcrumbs;
};

/**
 * Formata data para ISO string (SEO)
 */
export const formatDateForSEO = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
};
