import { logger } from './logger';

export class ApiLogger {
  static logRequest(url: string, method: string, payload?: any): void {
    logger.info('API Request', {
      type: 'api_request',
      url,
      method,
      payload: payload ? JSON.stringify(payload) : undefined,
    });
  }

  static logResponse(
    url: string,
    status: number,
    data?: any,
    duration?: number
  ): void {
    const level = status >= 400 ? 'error' : 'info';
    logger[level]('API Response', {
      type: 'api_response',
      url,
      status,
      duration,
      dataSize: data ? JSON.stringify(data).length : 0,
    });
  }

  static logError(url: string, error: Error): void {
    logger.error('API Error', {
      type: 'api_error',
      url,
      error: error.message,
      stack: error.stack,
    });
  }
}
