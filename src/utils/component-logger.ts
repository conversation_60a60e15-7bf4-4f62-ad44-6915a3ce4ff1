import { logger } from './logger';

export class ComponentLogger {
  static logMount(componentName: string, props?: Record<string, any>): void {
    logger.debug('Component Mounted', {
      type: 'component_lifecycle',
      component: componentName,
      event: 'mount',
      props,
    });
  }

  static logUnmount(componentName: string): void {
    logger.debug('Component Unmounted', {
      type: 'component_lifecycle',
      component: componentName,
      event: 'unmount',
    });
  }

  static logStateChange(
    componentName: string,
    oldState: any,
    newState: any
  ): void {
    logger.debug('State Change', {
      type: 'state_change',
      component: componentName,
      oldState,
      newState,
    });
  }

  static logError(componentName: string, error: Error, errorInfo?: any): void {
    logger.error('Component Error', {
      type: 'component_error',
      component: componentName,
      error: error.message,
      stack: error.stack,
      errorInfo,
    });
  }
}
