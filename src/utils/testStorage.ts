/**
 * @fileoverview Utilitário para testar configuração do Supabase Storage
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { supabase } from '../lib/supabaseClient';

/**
 * Testa se o Storage está configurado corretamente
 */
export const testStorageConfiguration = async () => {
  console.log('🧪 Testando configuração do Supabase Storage...');

  try {
    // 1. Testar se o bucket existe
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Erro ao listar buckets:', bucketsError);
      return false;
    }

    const mediaBucket = buckets?.find(bucket => bucket.id === 'media');
    if (!mediaBucket) {
      console.error('❌ Bucket "media" não encontrado');
      console.log('📋 Buckets disponíveis:', buckets?.map(b => b.id));
      return false;
    }

    console.log('✅ Bucket "media" encontrado:', mediaBucket);

    // 2. Testar listagem de arquivos
    const { data: files, error: listError } = await supabase.storage
      .from('media')
      .list('', { limit: 1 });

    if (listError) {
      console.error('❌ Erro ao listar arquivos:', listError);
      return false;
    }

    console.log('✅ Listagem de arquivos funcionando');
    console.log('📁 Arquivos encontrados:', files?.length || 0);

    // 3. Testar upload de arquivo pequeno (apenas se autenticado)
    const { data: user } = await supabase.auth.getUser();
    
    if (user.user) {
      console.log('👤 Usuário autenticado, testando upload...');
      
      // Criar arquivo de teste pequeno
      const testFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('media')
        .upload(`temp/test-${Date.now()}.txt`, testFile);

      if (uploadError) {
        console.error('❌ Erro no upload de teste:', uploadError);
        return false;
      }

      console.log('✅ Upload de teste funcionando:', uploadData);

      // Limpar arquivo de teste
      if (uploadData?.path) {
        await supabase.storage.from('media').remove([uploadData.path]);
        console.log('🧹 Arquivo de teste removido');
      }
    } else {
      console.log('⚠️ Usuário não autenticado, pulando teste de upload');
    }

    console.log('🎉 Configuração do Storage está funcionando!');
    return true;

  } catch (error) {
    console.error('❌ Erro inesperado no teste:', error);
    return false;
  }
};

/**
 * Testa upload de imagem específica
 */
export const testImageUpload = async (file: File) => {
  try {
    console.log('🖼️ Testando upload de imagem:', file.name);

    const fileName = `test-${Date.now()}-${file.name}`;
    const { data, error } = await supabase.storage
      .from('media')
      .upload(`temp/${fileName}`, file);

    if (error) {
      console.error('❌ Erro no upload da imagem:', error);
      return { success: false, error: error.message };
    }

    // Obter URL pública
    const { data: urlData } = supabase.storage
      .from('media')
      .getPublicUrl(`temp/${fileName}`);

    console.log('✅ Upload da imagem bem-sucedido!');
    console.log('🔗 URL pública:', urlData.publicUrl);

    return {
      success: true,
      data: {
        path: data.path,
        url: urlData.publicUrl
      }
    };

  } catch (error) {
    console.error('❌ Erro inesperado no upload:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

// Para usar no console do navegador:
// import { testStorageConfiguration } from './utils/testStorage';
// testStorageConfiguration();
