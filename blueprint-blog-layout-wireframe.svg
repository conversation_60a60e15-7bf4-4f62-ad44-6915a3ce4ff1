<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="800" fill="#0a0a0a"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" fill="#00ffff" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    Blueprint Blog v2 - Layout Wireframe
  </text>
  
  <!-- Desktop Layout -->
  <g id="desktop-layout">
    <!-- Desktop Label -->
    <text x="300" y="70" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
      🖥️ DESKTOP (≥1024px)
    </text>
    
    <!-- Above the Fold Container -->
    <rect x="50" y="90" width="500" height="280" fill="none" stroke="#00ffff" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="55" y="110" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">Above the Fold</text>
    
    <!-- Hero Post (2/3 width) -->
    <rect x="60" y="120" width="320" height="240" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="65" y="140" fill="#00ffff" font-family="Arial, sans-serif" font-size="10">⭐ DESTAQUE</text>
    <text x="320" y="140" fill="#888888" font-family="Arial, sans-serif" font-size="10">🏷️ TECH</text>
    <text x="65" y="160" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Como Implementar</text>
    <text x="65" y="175" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Autenticação JWT</text>
    <text x="65" y="190" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">em Node.js</text>
    <text x="65" y="210" fill="#888888" font-family="Arial, sans-serif" font-size="10">Aprenda a implementar autenticação segura...</text>
    <text x="65" y="330" fill="#888888" font-family="Arial, sans-serif" font-size="9">👤 João Silva • ⏱️ 8 min • 📅 14 Jun 2025</text>
    <rect x="65" y="340" width="80" height="20" fill="#00ffff" rx="3"/>
    <text x="105" y="352" text-anchor="middle" fill="#0a0a0a" font-family="Arial, sans-serif" font-size="9">Leia mais →</text>
    
    <!-- Top do Mês (1/3 width) -->
    <rect x="390" y="120" width="150" height="240" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="395" y="140" fill="#ff6b35" font-family="Arial, sans-serif" font-size="12" font-weight="bold">🔥 Top do Mês</text>
    <text x="395" y="155" fill="#888888" font-family="Arial, sans-serif" font-size="8">Posts mais visualizados</text>
    
    <!-- Top posts list -->
    <g id="top-posts">
      <circle cx="405" cy="175" r="8" fill="#00ffff"/>
      <text x="408" y="179" text-anchor="middle" fill="#0a0a0a" font-family="Arial, sans-serif" font-size="8" font-weight="bold">1</text>
      <text x="420" y="179" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">React 19: Novas Features</text>
      <text x="520" y="179" fill="#888888" font-family="Arial, sans-serif" font-size="7">👁️ 15.2k ❤️ 892</text>
      
      <circle cx="405" cy="195" r="8" fill="#ffa500"/>
      <text x="408" y="199" text-anchor="middle" fill="#0a0a0a" font-family="Arial, sans-serif" font-size="8" font-weight="bold">2</text>
      <text x="420" y="199" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">TypeScript 5.0 Guide</text>
      <text x="520" y="199" fill="#888888" font-family="Arial, sans-serif" font-size="7">👁️ 12.1k ❤️ 654</text>
      
      <circle cx="405" cy="215" r="8" fill="#ff4444"/>
      <text x="408" y="219" text-anchor="middle" fill="#0a0a0a" font-family="Arial, sans-serif" font-size="8" font-weight="bold">3</text>
      <text x="420" y="219" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">Next.js App Router</text>
      <text x="520" y="219" fill="#888888" font-family="Arial, sans-serif" font-size="7">👁️ 9.8k ❤️ 432</text>
      
      <circle cx="405" cy="235" r="8" fill="#888888"/>
      <text x="408" y="239" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="8" font-weight="bold">4</text>
      <text x="420" y="239" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">CSS Grid vs Flexbox</text>
      <text x="520" y="239" fill="#888888" font-family="Arial, sans-serif" font-size="7">👁️ 8.1k ❤️ 321</text>
      
      <circle cx="405" cy="255" r="8" fill="#888888"/>
      <text x="408" y="259" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="8" font-weight="bold">5</text>
      <text x="420" y="259" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">Docker para Devs</text>
      <text x="520" y="259" fill="#888888" font-family="Arial, sans-serif" font-size="7">👁️ 7.3k ❤️ 287</text>
    </g>
    
    <!-- Below the Fold Container -->
    <rect x="50" y="390" width="500" height="160" fill="none" stroke="#00ffff" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="55" y="410" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">Below the Fold</text>
    
    <!-- Posts Recentes Header -->
    <text x="60" y="430" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">📝 Posts Recentes</text>
    <text x="60" y="445" fill="#888888" font-family="Arial, sans-serif" font-size="10">Continue lendo</text>
    
    <!-- Navigation Arrows -->
    <circle cx="480" cy="435" r="12" fill="#1a1a1a" stroke="#00ffff"/>
    <text x="480" y="440" text-anchor="middle" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">←</text>
    <circle cx="510" cy="435" r="12" fill="#1a1a1a" stroke="#00ffff"/>
    <text x="510" y="440" text-anchor="middle" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">→</text>
    
    <!-- Carousel Cards (3 slides) -->
    <g id="carousel-desktop">
      <rect x="60" y="460" width="150" height="80" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
      <text x="65" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">🏷️ JS</text>
      <text x="200" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">5min</text>
      <text x="65" y="515" fill="#ffffff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Introdução ao GraphQL</text>
      <text x="65" y="530" fill="#888888" font-family="Arial, sans-serif" font-size="8">👤 Maria Santos • 📅 13 Jun</text>
      
      <rect x="220" y="460" width="150" height="80" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
      <text x="225" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">🏷️ VUE</text>
      <text x="360" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">7min</text>
      <text x="225" y="515" fill="#ffffff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Vue 3 Composition API</text>
      <text x="225" y="530" fill="#888888" font-family="Arial, sans-serif" font-size="8">👤 Pedro Lima • 📅 12 Jun</text>
      
      <rect x="380" y="460" width="150" height="80" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
      <text x="385" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">🏷️ CSS</text>
      <text x="520" y="475" fill="#888888" font-family="Arial, sans-serif" font-size="8">4min</text>
      <text x="385" y="515" fill="#ffffff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Tailwind CSS Tips</text>
      <text x="385" y="530" fill="#888888" font-family="Arial, sans-serif" font-size="8">👤 Ana Costa • 📅 11 Jun</text>
    </g>
  </g>
  
  <!-- Mobile Layout -->
  <g id="mobile-layout">
    <!-- Mobile Label -->
    <text x="900" y="70" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
      📱 MOBILE (&lt;640px)
    </text>
    
    <!-- Above the Fold Container -->
    <rect x="750" y="90" width="300" height="350" fill="none" stroke="#00ffff" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="755" y="110" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">Above the Fold</text>
    
    <!-- Hero Post (full width) -->
    <rect x="760" y="120" width="280" height="180" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="765" y="135" fill="#00ffff" font-family="Arial, sans-serif" font-size="9">⭐</text>
    <text x="1020" y="135" fill="#888888" font-family="Arial, sans-serif" font-size="9">🏷️</text>
    <text x="765" y="260" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Como Implementar</text>
    <text x="765" y="275" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Autenticação JWT</text>
    <text x="765" y="290" fill="#888888" font-family="Arial, sans-serif" font-size="8">👤 João Silva • ⏱️ 8min</text>
    
    <!-- Top do Mês (full width) -->
    <rect x="760" y="310" width="280" height="120" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="765" y="325" fill="#ff6b35" font-family="Arial, sans-serif" font-size="11" font-weight="bold">🔥 Top do Mês</text>
    <text x="765" y="345" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">1️⃣ React 19: Novas Features</text>
    <text x="765" y="360" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">2️⃣ TypeScript 5.0 Guide</text>
    <text x="765" y="375" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">3️⃣ Next.js App Router</text>
    <text x="765" y="390" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">4️⃣ CSS Grid vs Flexbox</text>
    <text x="765" y="405" fill="#ffffff" font-family="Arial, sans-serif" font-size="9">5️⃣ Docker para Devs</text>
    
    <!-- Below the Fold Container -->
    <rect x="750" y="460" width="300" height="120" fill="none" stroke="#00ffff" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="755" y="480" fill="#00ffff" font-family="Arial, sans-serif" font-size="12">Below the Fold</text>
    
    <!-- Posts Recentes Header -->
    <text x="760" y="500" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">📝 Posts Recentes</text>
    <text x="760" y="515" fill="#888888" font-family="Arial, sans-serif" font-size="9">Touch/Swipe →</text>
    
    <!-- Single Card (1 slide) -->
    <rect x="760" y="525" width="280" height="50" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="765" y="540" fill="#888888" font-family="Arial, sans-serif" font-size="8">🏷️ JS</text>
    <text x="1020" y="540" fill="#888888" font-family="Arial, sans-serif" font-size="8">5min</text>
    <text x="765" y="555" fill="#ffffff" font-family="Arial, sans-serif" font-size="10" font-weight="bold">Introdução ao GraphQL</text>
    <text x="765" y="570" fill="#888888" font-family="Arial, sans-serif" font-size="8">👤 Maria Santos</text>
  </g>
  
  <!-- Responsive Indicators -->
  <g id="responsive-indicators">
    <text x="600" y="620" text-anchor="middle" fill="#00ffff" font-family="Arial, sans-serif" font-size="16" font-weight="bold">
      📐 RESPONSIVE BREAKPOINTS
    </text>
    
    <!-- Mobile -->
    <rect x="200" y="640" width="200" height="60" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="300" y="655" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">📱 MOBILE</text>
    <text x="300" y="670" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="10">&lt; 640px (sm:)</text>
    <text x="300" y="685" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">1 slide • Touch/Swipe</text>
    <text x="300" y="695" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">Compact layout</text>
    
    <!-- Tablet -->
    <rect x="420" y="640" width="200" height="60" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="520" y="655" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">📱 TABLET</text>
    <text x="520" y="670" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="10">640px - 1024px</text>
    <text x="520" y="685" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">2 slides • Touch + Arrows</text>
    <text x="520" y="695" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">Medium layout</text>
    
    <!-- Desktop -->
    <rect x="640" y="640" width="200" height="60" fill="#1a1a1a" stroke="#00ffff" stroke-width="1"/>
    <text x="740" y="655" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">🖥️ DESKTOP</text>
    <text x="740" y="670" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="10">≥ 1024px (lg:+)</text>
    <text x="740" y="685" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">3 slides • Arrows + Auto</text>
    <text x="740" y="695" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="9">Full layout</text>
  </g>
  
  <!-- Footer -->
  <text x="600" y="780" text-anchor="middle" fill="#888888" font-family="Arial, sans-serif" font-size="12">
    Blueprint Blog v2 - Above-the-Fold Layout with Optimized Mobile Responsiveness
  </text>
</svg>
