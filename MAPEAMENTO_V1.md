# 📋 MAPEAMENTO COMPLETO - Blueprint Blog v1

**Data**: 2025-06-15  
**Objetivo**: Documentar configurações críticas do v1 para migração segura para v2

---

## 🔍 **CONFIGURAÇÕES CRÍTICAS ENCONTRADAS**

### 📊 **Google Analytics**
- **ID**: `G-4MLRCV03LK` ✅ **CONFIGURADO**
- **Localização**: `src/hooks/useAnalytics.ts` (linha 108)
- **Implementação**: Google Analytics 4 com gtag
- **Features**:
  - Page tracking automático
  - Event tracking customizado
  - Web Vitals monitoring
  - Performance metrics
  - Error tracking

### 📈 **Vercel Analytics**
- **Implementação**: `@vercel/analytics` ✅ **CONFIGURADO**
- **Localização**: `src/components/analytics/AnalyticsTracker.tsx`
- **Features**:
  - Custom event tracking
  - Production-only tracking
  - Post interactions (view, like, share)
  - Search tracking
  - Theme change tracking

### 🗄️ **Supabase (Configurado mas não ativo)**
- **Cliente**: `src/lib/supabaseClient.ts` ✅ **PRONTO**
- **Variáveis**: `.env.example` com template
- **Schema**: Compatível com estrutura atual do banco
- **Tabelas mapeadas**:
  - `posts` (16 campos) ✅
  - `categories` (6 campos) ✅
  - `tags` (8 campos) ✅
  - `post_categories` (relacionamento) ✅
  - `post_tags` (relacionamento) ✅

### 🛠️ **Stack Técnica**
- **Framework**: React 19.1.0 + Vite 6.3.5
- **Styling**: Tailwind CSS 3.4.17
- **Routing**: React Router 7.6.2
- **Animation**: Motion 12.18.1 (Framer Motion)
- **Icons**: React Icons 5.5.0
- **TypeScript**: 5.8.3 (strict mode)

### 📁 **Estrutura de Arquivos**
```
src/
├── components/
│   ├── analytics/           # ✅ Analytics configurado
│   ├── auth/               # ✅ Sistema de auth
│   ├── layout/             # ✅ Layouts principais
│   └── ui/                 # ✅ Componentes base
├── hooks/
│   ├── useAnalytics.ts     # ✅ GA4 + Web Vitals
│   ├── useAuth.tsx         # ✅ Autenticação
│   └── usePosts.ts         # ✅ Gerenciamento posts
├── pages/                  # ✅ Todas as páginas
├── services/               # ✅ Serviços Supabase
└── types/                  # ✅ TypeScript types
```

---

## 🚨 **CONFIGURAÇÕES AUSENTES (Para implementar no v2)**

### 🔍 **SEO**
- ❌ Meta tags dinâmicas
- ❌ Open Graph
- ❌ JSON-LD structured data
- ❌ Sitemap.xml
- ❌ Robots.txt

### 🚀 **Deploy/Performance**
- ❌ vercel.json
- ❌ Service Worker
- ❌ PWA manifest
- ❌ Lazy loading otimizado

### 🌐 **Internacionalização**
- ❌ i18next
- ❌ Multi-idioma

---

## ✅ **MIGRAÇÃO SEGURA - CHECKLIST**

### **Preservar (Copiar do v1)**
- [x] Google Analytics ID: `G-4MLRCV03LK`
- [x] Estrutura Supabase completa
- [x] Hooks de analytics
- [x] Sistema de autenticação
- [x] Componentes UI base
- [x] Configurações TypeScript

### **Adaptar (Modificar para v2)**
- [ ] Layout components (Hero, TopOfMonth)
- [ ] Tema cyberpunk (cores, animações)
- [ ] Estrutura de rotas
- [ ] Dados mockados

### **Implementar (Novo no v2)**
- [ ] SEO dinâmico
- [ ] Sitemap automático
- [ ] PWA básico
- [ ] Vercel.json otimizado

---

## 🔧 **SCRIPTS DE MIGRAÇÃO**

### **1. Preservar Analytics**
```typescript
// Manter exatamente como está
const GA_ID = 'G-4MLRCV03LK';
```

### **2. Supabase Schema**
```sql
-- Schema já compatível, apenas remover:
DROP TABLE devlogs;
DROP TABLE projects;
```

### **3. Variáveis de Ambiente**
```env
# Copiar configurações existentes
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-key
```

---

## 📊 **MÉTRICAS ATUAIS**
- **Build Size**: ~292KB (90KB gzipped)
- **Performance**: Otimizado com lazy loading
- **TypeScript**: 100% tipado
- **Analytics**: Totalmente funcional

---

## 🎯 **PRÓXIMOS PASSOS**
1. Copiar configurações críticas para v2
2. Adaptar layout e tema
3. Implementar SEO ausente
4. Testar migração Supabase
5. Deploy com configurações preservadas
