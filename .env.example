# ============================================================================
# BLUEPRINT BLOG - VARIÁVEIS DE AMBIENTE
# ============================================================================

# ===== SUPABASE =====
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# ===== ANALYTICS =====
VITE_GA_MEASUREMENT_ID=G-4MLRCV03LK



# ===== APLICAÇÃO =====
VITE_APP_VERSION=2.0.0
VITE_APP_NAME=Blueprint Blog

# ===== API BACKEND =====
VITE_API_URL=https://api.blueprintblog.tech

# ============================================================================
# INSTRUÇÕES DE CONFIGURAÇÃO
# ============================================================================

# 1. SUPABASE
# - Crie um projeto em https://supabase.com
# - Copie a URL e a chave anônima do projeto
# - Configure as tabelas conforme SUPABASE_IMPLEMENTATION.md



# ============================================================================
# EXEMPLO DE CONFIGURAÇÃO MÍNIMA
# ============================================================================

# Para desenvolvimento local (mínimo necessário):
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your-anon-key
# VITE_GA_MEASUREMENT_ID=G-4MLRCV03LK

# Para produção (recomendado):
# + Todas as variáveis acima
# + VITE_APP_VERSION=2.0.0
